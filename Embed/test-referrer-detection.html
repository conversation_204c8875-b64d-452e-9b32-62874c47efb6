<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Referrer Client Detection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background-color: #f9fff9; }
        .failure { border-color: #f44336; background-color: #fff9f9; }
        .test-url {
            font-family: monospace;
            background-color: #f5f5f5;
            padding: 5px;
            border-radius: 3px;
            word-break: break-all;
        }
        button {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a87;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Referrer Client Detection Test</h1>
    <p>This page tests the referrer-based client detection functionality for the Repd embed system.</p>

    <h2>Test Cases</h2>
    <div id="test-cases"></div>

    <h2>Interactive Tests</h2>
    <div>
        <input type="text" id="custom-referrer" placeholder="Enter referrer URL to test" style="width: 400px; padding: 8px;">
        <button onclick="testCustomReferrer()">Test Referrer</button>
    </div>

    <div>
        <button onclick="testCurrentPage()">Test Current Page Detection</button>
        <button onclick="runAllTests()">Run All Tests</button>
    </div>

    <div id="results" class="results" style="display: none;">
        <h3>Results</h3>
        <pre id="results-content"></pre>
    </div>

    <script>
        // Mock the client domain mapping for testing
        const CLIENT_DOMAIN_MAP = {
            'emporia.gov': 'emporia',
            'arlington.gov': 'arlington',
            'cityofpage.org': 'page',
            'yorkcounty.gov': 'york',
            'mountjacksonva.gov': 'mountjackson',
            'loudoun.gov': 'loudoun',
            'parkcity.org': 'parkcity',
            'roelandpark.net': 'roelandpark',
            'cityofmyrtlebeach.com': 'myrtlebeach',
            'townofmorrisville.org': 'morrisville',
            'traversecitymi.gov': 'traversecity',
            'cityoftulare.com': 'tulare',
            'salisburymd.gov': 'salisbury'
        };

        // Mock client name to ID mapping
        const clientNameToId = {
            'emporia': '283',
            'arlington': '211',
            'page': '295',
            'york': '294',
            'mountjackson': '293',
            'loudoun': '292',
            'parkcity': '288',
            'roelandpark': '287',
            'myrtlebeach': '299',
            'morrisville': '281',
            'traversecity': '269',
            'tulare': '234',
            'salisbury': '221'
        };

        // Mock the extractClientFromReferrer function
        function extractClientFromReferrer(referrerUrl) {
            try {
                const url = new URL(referrerUrl);
                const hostname = url.hostname.toLowerCase();
                
                // Direct domain match
                if (CLIENT_DOMAIN_MAP[hostname]) {
                    return CLIENT_DOMAIN_MAP[hostname];
                }

                // Check for subdomain patterns (e.g., www.emporia.gov)
                const domainParts = hostname.split('.');
                if (domainParts.length > 2) {
                    const baseDomain = domainParts.slice(-2).join('.');
                    if (CLIENT_DOMAIN_MAP[baseDomain]) {
                        return CLIENT_DOMAIN_MAP[baseDomain];
                    }
                }

                // Check for client name in subdomain or path
                for (const [domain, clientName] of Object.entries(CLIENT_DOMAIN_MAP)) {
                    if (hostname.includes(domain.split('.')[0]) || 
                        url.pathname.toLowerCase().includes(clientName)) {
                        return clientName;
                    }
                }

                return null;
            } catch (error) {
                console.error('Error parsing referrer URL:', error);
                return null;
            }
        }

        // Test cases
        const testCases = [
            {
                name: 'Emporia.gov direct domain',
                referrer: 'https://emporia.gov/services',
                expected: 'emporia'
            },
            {
                name: 'Arlington.gov with www subdomain',
                referrer: 'https://www.arlington.gov/departments',
                expected: 'arlington'
            },
            {
                name: 'Page city with path',
                referrer: 'https://cityofpage.org/government/departments',
                expected: 'page'
            },
            {
                name: 'Unknown domain',
                referrer: 'https://unknown-city.com/page',
                expected: null
            },
            {
                name: 'Myrtle Beach with complex path',
                referrer: 'https://cityofmyrtlebeach.com/departments/public-works',
                expected: 'myrtlebeach'
            },
            {
                name: 'Invalid URL',
                referrer: 'not-a-valid-url',
                expected: null
            }
        ];

        function runTest(testCase) {
            const result = extractClientFromReferrer(testCase.referrer);
            const success = result === testCase.expected;
            
            return {
                ...testCase,
                result,
                success,
                clientId: result ? clientNameToId[result] : null
            };
        }

        function displayTestResults() {
            const container = document.getElementById('test-cases');
            container.innerHTML = '';

            testCases.forEach((testCase, index) => {
                const testResult = runTest(testCase);
                const div = document.createElement('div');
                div.className = `test-case ${testResult.success ? 'success' : 'failure'}`;
                
                div.innerHTML = `
                    <h4>Test ${index + 1}: ${testResult.name}</h4>
                    <div class="test-url"><strong>Referrer:</strong> ${testResult.referrer}</div>
                    <div><strong>Expected:</strong> ${testResult.expected || 'null'}</div>
                    <div><strong>Result:</strong> ${testResult.result || 'null'}</div>
                    <div><strong>Client ID:</strong> ${testResult.clientId || 'null'}</div>
                    <div><strong>Status:</strong> ${testResult.success ? '✅ PASS' : '❌ FAIL'}</div>
                `;
                
                container.appendChild(div);
            });
        }

        function testCustomReferrer() {
            const referrer = document.getElementById('custom-referrer').value;
            if (!referrer) {
                alert('Please enter a referrer URL');
                return;
            }

            const result = extractClientFromReferrer(referrer);
            const clientId = result ? clientNameToId[result] : null;
            
            showResults({
                referrer,
                clientName: result,
                clientId,
                embedUrl: `https://embed.repd.us/?referrer=${encodeURIComponent(referrer)}`
            });
        }

        function testCurrentPage() {
            const currentUrl = window.location.href;
            const urlParams = new URLSearchParams(window.location.search);
            const referrer = urlParams.get('referrer');
            
            let result = null;
            if (referrer) {
                result = extractClientFromReferrer(decodeURIComponent(referrer));
            }

            showResults({
                currentUrl,
                referrer: referrer ? decodeURIComponent(referrer) : 'None',
                clientName: result,
                clientId: result ? clientNameToId[result] : null,
                method: 'Current page analysis'
            });
        }

        function runAllTests() {
            const results = testCases.map(runTest);
            const passed = results.filter(r => r.success).length;
            const total = results.length;
            
            showResults({
                summary: `${passed}/${total} tests passed`,
                results: results.map(r => ({
                    name: r.name,
                    referrer: r.referrer,
                    expected: r.expected,
                    result: r.result,
                    success: r.success
                }))
            });
        }

        function showResults(data) {
            const resultsDiv = document.getElementById('results');
            const resultsContent = document.getElementById('results-content');
            
            resultsContent.textContent = JSON.stringify(data, null, 2);
            resultsDiv.style.display = 'block';
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            displayTestResults();
        });
    </script>
</body>
</html>
