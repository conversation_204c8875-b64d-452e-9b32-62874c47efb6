import { ClientSvgCustomization } from './types';

const emporiaCustomization: ClientSvgCustomization = {
  logos: {
    default: 'https://files.repd.us/manual/Emporia-Icon-White.png',
    cityLoaderSvg: 'https://files.repd.us/manual/Emporia-Logo-White.png',
    askButtonSvg: 'https://files.repd.us/manual/Emporia-Logo-White.png',
    cityIllustrationSvg: 'https://files.repd.us/manual/Emporia-Logo-White.png',
    AskTheCityButton: 'https://files.repd.us/manual/Emporia-Icon-White.png',
    cityLoader: 'https://files.repd.us/manual/Emporia-Logo-White.png',
    askButton: 'https://files.repd.us/manual/Emporia-Logo-White.png',
    askTheCityButton: 'https://files.repd.us/manual/Emporia-Logo-White.png',
    minifiedButton: 'https://files.repd.us/manual/Emporia-Logo-White.png'
  },
  
  applySvgCustomizations: (svgElement: SVGElement) => {
    // Find all elements with pink colors
    const pinkElements = svgElement.querySelectorAll(
      '[fill="#E94E77"], [fill="#EA3269"], [fill="#E83168"], [fill="#E73067"], ' +
      '[stroke="#E94E77"], [stroke="#EA3269"], [stroke="#E83168"], [stroke="#E73067"]'
    );

    // Replace with Emporia blue
    pinkElements.forEach(element => {
      if (element instanceof SVGElement) {
        if (element.hasAttribute('fill') && element.getAttribute('fill')?.includes('#E')) {
          element.setAttribute('fill', '#0E4EA0');
        }
        if (element.hasAttribute('stroke') && element.getAttribute('stroke')?.includes('#E')) {
          element.setAttribute('stroke', '#0E4EA0');
        }
      }
    });

    // Find all gradient stops with pink colors
    const pinkStops = svgElement.querySelectorAll(
      'stop[stop-color="#FF3974"], stop[stop-color="#E73067"], ' +
      'stop[stop-color="#E94E77"], stop[stop-color="#EA3269"], stop[stop-color="#E83168"]'
    );

    // Replace with Emporia blue
    pinkStops.forEach(stop => {
      if (stop instanceof SVGElement) {
        stop.setAttribute('stop-color', '#0E4EA0');
      }
    });
    
    // Hide the white circle background
    const logoBackground = svgElement.querySelector('#logoBackground');
    if (logoBackground && logoBackground.parentNode) {
      logoBackground.parentNode.removeChild(logoBackground);
    }
    
    
  }
};

export default emporiaCustomization;
