import { ClientSvgCustomization } from './types';

const yorkCustomization: ClientSvgCustomization = {
  logos: {
    default: 'https://files.repd.us/manual/York_Logo-removebg-preview.png',
    cityLoaderSvg: 'https://files.repd.us/manual/York_Logo-removebg-preview.png',
    askButtonSvg: 'https://files.repd.us/manual/York_Logo-removebg-preview.png',
    cityIllustrationSvg: 'https://files.repd.us/manual/York_Logo-removebg-preview.png',
    AskTheCityButton: 'https://files.repd.us/manual/York_Logo-removebg-preview.png',
    cityLoader: 'https://files.repd.us/manual/York_Logo-removebg-preview.png',
    askButton: 'https://files.repd.us/manual/York_Logo-removebg-preview.png',
    askTheCityButton: 'https://files.repd.us/manual/York_Logo-removebg-preview.png',
    minifiedButton: 'https://files.repd.us/manual/York_Logo-removebg-preview.png'
  },
  
  applySvgCustomizations: (svgElement: SVGElement) => {
    // Find the client name text element and update it
    const clientNameText = svgElement.querySelector('#clientNameText');
    if (clientNameText) {
      clientNameText.setAttribute('font-size', '14px !important');
      clientNameText.setAttribute('style', 'font-size: 14px !important;');

      // Update the text content to "City Hall"
      const tspan = clientNameText.querySelector('tspan');
      if (tspan) {
        tspan.textContent = 'City Hall';
      }
      
      // Update position if needed
      if (clientNameText instanceof SVGElement) {
        clientNameText.setAttribute('transform', 'translate(30.2 85.4)');
      }
    }
    
    // Update the "Ask" text position
    const askText = svgElement.querySelector('text.st6');
    if (askText instanceof SVGElement) {
      askText.setAttribute('font-size', '14px !important');
      askText.setAttribute('style', 'font-size: 14px !important;');
      askText.setAttribute('transform', 'translate(46.2 71.4)');
    }
    
    // Update logo placeholder dimensions and position
    const logoPlaceholder = svgElement.querySelector('#logoPlaceholder');
    if (logoPlaceholder instanceof SVGElement) {
      if (logoPlaceholder.tagName.toLowerCase() === 'image') {
        logoPlaceholder.setAttribute('x', '31');
        logoPlaceholder.setAttribute('y', '31');
        logoPlaceholder.setAttribute('width', '60');
        logoPlaceholder.setAttribute('height', '30');
        logoPlaceholder.setAttribute('preserveAspectRatio', 'xMidYMid meet');
      }
    }
    
    // Add or update the white background rect
    let bgRect = svgElement.querySelector('rect[x="31"][y="35"]');
    if (!bgRect) {
      bgRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      if (logoPlaceholder && logoPlaceholder.parentNode) {
        logoPlaceholder.parentNode.insertBefore(bgRect, logoPlaceholder);
      }
    }
    
    if (bgRect instanceof SVGElement) {
      bgRect.setAttribute('x', '31');
      bgRect.setAttribute('y', '35');
      bgRect.setAttribute('width', '60');
      bgRect.setAttribute('height', '20');
      bgRect.setAttribute('fill', 'white');
      bgRect.setAttribute('rx', '10');
      bgRect.setAttribute('ry', '10');
    }
    
    // Hide the white circle background
    const logoBackground = svgElement.querySelector('#logoBackground');
    if (logoBackground && logoBackground.parentNode) {
      logoBackground.parentNode.removeChild(logoBackground);
    }
    
    
    
    // Apply York-specific customizations to all SVGs, including loading icons
    applyYorkSvgCustomizations(svgElement);
  }
};

// Add this function to handle York-specific SVG customizations
export function applyYorkSvgCustomizations(svgElement: SVGElement) {
  // Check if this is a loading icon SVG
  const isLoadingIcon = svgElement.classList.contains('loadingIcon') || 
                        svgElement.id === 'cityQuestionIcon' ||
                        svgElement.querySelector('.loader-circle, .city-loader');
  
  if (isLoadingIcon) {
    // Get SVG dimensions
    const svgWidth = parseFloat(svgElement.getAttribute('width') || '100');
    const svgHeight = parseFloat(svgElement.getAttribute('height') || '100');
    
    // Create or find background rect
    let bgRect = svgElement.querySelector('rect.york-background');
    if (!bgRect) {
      bgRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      // Insert at the beginning of the SVG to be behind other elements
      if (svgElement.firstChild) {
        svgElement.insertBefore(bgRect, svgElement.firstChild);
      } else {
        svgElement.appendChild(bgRect);
      }
    }
    
    // Set background rect attributes
    if (bgRect instanceof SVGElement) {
      bgRect.setAttribute('class', 'york-background');
      bgRect.setAttribute('x', '0');
      bgRect.setAttribute('y', '0');
      bgRect.setAttribute('width', svgWidth.toString());
      bgRect.setAttribute('height', svgHeight.toString());
      bgRect.setAttribute('fill', 'white');
      bgRect.setAttribute('rx', '10');
      bgRect.setAttribute('ry', '10');
    }
    
    // Find the logo placeholder or image
    const logoPlaceholder = svgElement.querySelector('#logoPlaceholder, #cityLoaderButtonLogoPlaceholder, image[data-client-logo="true"]');
    if (logoPlaceholder instanceof SVGElement) {
      // Adjust the logo size and position to fit within the white background
      const x = parseFloat(logoPlaceholder.getAttribute('x') || '0');
      const y = parseFloat(logoPlaceholder.getAttribute('y') || '0');
      const width = parseFloat(logoPlaceholder.getAttribute('width') || '40');
      const height = parseFloat(logoPlaceholder.getAttribute('height') || '40');
      
      // Center the logo if needed
      if (x === 0 && y === 0) {
        const newX = (svgWidth - width) / 2;
        const newY = (svgHeight - height) / 2;
        logoPlaceholder.setAttribute('x', newX.toString());
        logoPlaceholder.setAttribute('y', newY.toString());
      }
    }
  }
}

export default yorkCustomization;

