import { ClientSvgCustomization } from './types';

const myrtleBeachCustomization: ClientSvgCustomization = {
  applySvgCustomizations: (svgElement: SVGElement) => {
    // Text content is now handled by the centralized button text mapping
    // Only handle positioning and styling here if needed
    const clientNameText = svgElement.querySelector('#clientNameText');

    if (clientNameText instanceof SVGElement) {
      // Update position for "Assistant" text (shorter than typical city names)
      clientNameText.setAttribute('transform', 'translate(33.2 85.4)');
    }

    // Update the "Ask" text position - change to "Staff" 
    const askText = svgElement.querySelector('text.st6');
    if (askText instanceof SVGElement) {
      // Adjust position for "Staff" text
      askText.setAttribute('transform', 'translate(45 73.4)');
    }
  }
};

export default myrtleBeachCustomization;
