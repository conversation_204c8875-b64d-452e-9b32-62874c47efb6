import { ClientCustomizationsMap } from './types';
import emporiaCustomization from './283';
import yorkCustomization from './294';
import dentonCustomization from './260';
import southBayCustomization from './276';
import mountJacksonCustomization from './293';
import lorainCustomization from './278';
import tulareCustomization from './234';
import myrtleBeachCustomization from './299';
import myrtleBeachPrstCustomization from './266';
import roelandParkCustomization from './287';
import morrisvilleCustomization from './281';
import redondoPdCustomization from './275';
import broadviewCustomization from './214';
import client300Customization from './300';
import loudounCustomization from './292';
import client304Customization from './304';
import client306Customization from './306';

// Import all client customizations here
const clientCustomizations: ClientCustomizationsMap = {
  '283': emporiaCustomization, // Emporia
  '294': yorkCustomization,    // York
  '260': dentonCustomization,  // Denton
  '276': southBayCustomization, // South Bay
  '293': mountJacksonCustomization, // Mount Jackson
  '278': lorainCustomization,  // Lorain
  '234': tulareCustomization,  // Tulare
  '299': myrtleBeachCustomization, // Myrtle Beach (Internal)
  '266': myrtleBeachPrstCustomization, // Myrtle Beach PRST
  '287': roelandParkCustomization, // Roeland Park
  '281': morrisvilleCustomization, // Town of Morrisville
  '275': redondoPdCustomization, // Redondo PD
  '214': broadviewCustomization, // Broadview
  '300': client300Customization, // Pleasantville
  '292': loudounCustomization, // Loudoun County
  '304': client304Customization, // Burke County
  '306': client306Customization // Shockey
  // Add more client customizations as needed
};

export default clientCustomizations;
