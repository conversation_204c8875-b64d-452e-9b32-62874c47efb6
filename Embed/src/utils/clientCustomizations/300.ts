import { ClientSvgCustomization } from './types';

const client300Customization: ClientSvgCustomization = {
  applySvgCustomizations: (svgElement: SVGElement) => {
    // Find the client name text element and update it
    const clientNameText = svgElement.querySelector('#clientNameText');

    if (clientNameText) {
      // Update the text content - customize this based on client needs
      const tspan = clientNameText.querySelector('tspan');

      if (tspan) {
        tspan.textContent = 'the Village'; // Update this text as needed
      }

      // Update position if needed - customize coordinates as required
      if (clientNameText instanceof SVGElement) {
        clientNameText.setAttribute('transform', 'translate(28.5 85.4)');
      }
    }
  }
};

export default client300Customization;
