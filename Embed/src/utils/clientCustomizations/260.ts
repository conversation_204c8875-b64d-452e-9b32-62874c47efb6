import { ClientSvgCustomization } from './types';

const dentonCustomization: ClientSvgCustomization = {
  logos: {
    default: 'https://files.repd.us/manual/denton%20logo.png',
    cityLoaderSvg: 'https://files.repd.us/manual/denton%20logo.png',
    askButtonSvg: 'https://files.repd.us/manual/denton%20logo.png',
    cityIllustrationSvg: 'https://files.repd.us/manual/denton%20logo.png',
    AskTheCityButton: 'https://files.repd.us/manual/denton%20logo.png',
    cityLoader: 'https://files.repd.us/manual/denton%20logo.png',
    askButton: 'https://files.repd.us/manual/denton%20logo.png',
    askTheCityButton: 'https://files.repd.us/manual/denton%20logo.png',
    minifiedButton: 'https://files.repd.us/manual/denton%20logo.png'
  },
  
  applySvgCustomizations: (svgElement: SVGElement) => {
    // Find the client name text element and update it
    const clientNameText = svgElement.querySelector('#clientNameText');
    if (clientNameText) {
      clientNameText.setAttribute('font-size', '14px !important');
      clientNameText.setAttribute('style', 'font-size: 14px !important;');

      // Update the text content to "City Hall"
      const tspan = clientNameText.querySelector('tspan');
      if (tspan) {
        tspan.textContent = 'City Hall';
      }
      
      // Update position if needed
      if (clientNameText instanceof SVGElement) {
        clientNameText.setAttribute('transform', 'translate(32.2 85.4)');
      }
    }
    
    // Update the "Ask" text position
    const askText = svgElement.querySelector('text.st6');
    if (askText instanceof SVGElement) {
      askText.setAttribute('font-size', '14px !important');
      askText.setAttribute('style', 'font-size: 14px !important;');
      askText.setAttribute('transform', 'translate(46.2 71.4)');
    }
    
    // Update logo placeholder dimensions and position
    const logoPlaceholder = svgElement.querySelector('#logoPlaceholder');
    if (logoPlaceholder instanceof SVGElement) {
      if (logoPlaceholder.tagName.toLowerCase() === 'image') {
        logoPlaceholder.setAttribute('x', '31');
        logoPlaceholder.setAttribute('y', '31');
        logoPlaceholder.setAttribute('width', '60');
        logoPlaceholder.setAttribute('height', '30');
        logoPlaceholder.setAttribute('preserveAspectRatio', 'xMidYMid meet');
      }
    }
    
    // Hide the white circle background
    const logoBackground = svgElement.querySelector('#logoBackground');
    if (logoBackground && logoBackground.parentNode) {
      logoBackground.parentNode.removeChild(logoBackground);
    }
    
    
    
    // Apply Denton-specific customizations to all SVGs, including loading icons
    applyDentonSvgCustomizations(svgElement);
  }
};

// Add this function to handle Denton-specific SVG customizations
export function applyDentonSvgCustomizations(svgElement: SVGElement) {
  // Check if this is a loading icon SVG
  const isLoadingIcon = svgElement.classList.contains('loadingIcon') || 
                        svgElement.id === 'cityQuestionIcon' ||
                        svgElement.querySelector('.loader-circle, .city-loader');
  
  if (isLoadingIcon) {
    // Get SVG dimensions
    const svgWidth = parseFloat(svgElement.getAttribute('width') || '100');
    const svgHeight = parseFloat(svgElement.getAttribute('height') || '100');
    
    // Find the logo placeholder or image
    const logoPlaceholder = svgElement.querySelector('#logoPlaceholder, #cityLoaderButtonLogoPlaceholder, image[data-client-logo="true"]');
    if (logoPlaceholder instanceof SVGElement) {
      // Adjust the logo size and position
      const width = parseFloat(logoPlaceholder.getAttribute('width') || '40');
      const height = parseFloat(logoPlaceholder.getAttribute('height') || '40');
      
      // Center the logo
      const newX = (svgWidth - width) / 2;
      const newY = (svgHeight - height) / 2;
      logoPlaceholder.setAttribute('x', newX.toString());
      logoPlaceholder.setAttribute('y', newY.toString());
    }
    
    // Find and remove any existing background rect
    const bgRect = svgElement.querySelector('rect.denton-background');
    if (bgRect && bgRect.parentNode) {
      bgRect.parentNode.removeChild(bgRect);
    }
  }
  
  // Handle the main page logo after clicking the minified button
  const mainPageLogo = svgElement.querySelector('.city-illustration-logo, #cityIllustrationLogo, .CityIllustration-module__logo');
  if (mainPageLogo instanceof SVGElement || (mainPageLogo instanceof HTMLElement)) {
    // Set the logo to the white Denton logo
    if (mainPageLogo instanceof SVGElement) {
      if (mainPageLogo.tagName.toLowerCase() === 'image') {
        mainPageLogo.setAttribute('href', 'https://files.repd.us/manual/denton%20logo.png');
      } else {
        const image = mainPageLogo.querySelector('image');
        if (image) {
          image.setAttribute('href', 'https://files.repd.us/manual/denton%20logo.png');
        }
      }
    } else if (mainPageLogo instanceof HTMLImageElement) {
      mainPageLogo.src = 'https://files.repd.us/manual/denton%20logo.png';
    }
    
    // Ensure the logo is visible and properly sized
    if (mainPageLogo instanceof HTMLElement) {
      mainPageLogo.style.display = 'block';
      mainPageLogo.style.maxWidth = '200px';
      mainPageLogo.style.maxHeight = '100px';
    }
  }
}

// Add a DOM observer to handle dynamically loaded content
function setupDentonDomObserver() {
  if (typeof window !== 'undefined' && typeof document !== 'undefined') {
    // Function to apply logo to main page elements
    const applyLogoToMainPage = () => {
      // Find the city illustration logo on the main page
      const mainPageLogos = document.querySelectorAll('.city-illustration-logo, #cityIllustrationLogo, .CityIllustration-module__logo, img[alt="City Question Icon"]');
      
      mainPageLogos.forEach(logo => {
        if (logo instanceof HTMLImageElement) {
          logo.src = 'https://files.repd.us/manual/denton%20logo.png';
          logo.style.display = 'block';
          logo.style.maxWidth = '200px';
          logo.style.maxHeight = '100px';
        }
      });
    };
    
    // Run once on load
    if (document.readyState === 'complete') {
      applyLogoToMainPage();
    } else {
      window.addEventListener('load', applyLogoToMainPage);
    }
    
    // Set up observer for dynamically loaded content
    const observer = new MutationObserver((mutations) => {
      applyLogoToMainPage();
    });
    
    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
}

// Call the setup function
setupDentonDomObserver();

export default dentonCustomization;
