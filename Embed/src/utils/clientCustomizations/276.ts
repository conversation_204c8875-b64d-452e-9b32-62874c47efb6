
import { ClientSvgCustomization } from './types';

const southBayCustomization: ClientSvgCustomization = {
  applySvgCustomizations: (svgElement: SVGElement) => {
    // Find the client name text element and update it
    const clientNameText = svgElement.querySelector('#clientNameText');
    if (clientNameText) {
      clientNameText.setAttribute('font-size', '14px !important');
      clientNameText.setAttribute('style', 'font-size: 14px !important;');

      // Update the text content to "City Hall"
      const tspan = clientNameText.querySelector('tspan');
      if (tspan) {
        tspan.textContent = 'City Hall';
      }
      
      // Update position if needed
      if (clientNameText instanceof SVGElement) {
        clientNameText.setAttribute('transform', 'translate(34.2 85.4)');
      }
    }
    
    
    
    // Update logo placeholder dimensions and position
    const logoPlaceholder = svgElement.querySelector('#logoPlaceholder');
    if (logoPlaceholder instanceof SVGElement) {
      if (logoPlaceholder.tagName.toLowerCase() === 'image') {
        logoPlaceholder.setAttribute('x', '31');
        logoPlaceholder.setAttribute('y', '31');
        logoPlaceholder.setAttribute('width', '60');
        logoPlaceholder.setAttribute('height', '30');
        logoPlaceholder.setAttribute('preserveAspectRatio', 'xMidYMid meet');
      }
    }
    
    // Hide the white circle background
    const logoBackground = svgElement.querySelector('#logoBackground');
    if (logoBackground && logoBackground.parentNode) {
      logoBackground.parentNode.removeChild(logoBackground);
    }
    
    // Find the logo placeholder or image
    const logoPlaceholderOrImage = svgElement.querySelector('#logoPlaceholder, #cityLoaderButtonLogoPlaceholder, image[data-client-logo="true"]');
    if (logoPlaceholderOrImage instanceof SVGElement) {
      // Adjust the logo size and position
      const width = parseFloat(logoPlaceholderOrImage.getAttribute('width') || '40');
      const height = parseFloat(logoPlaceholderOrImage.getAttribute('height') || '40');
      
      // Center the logo if needed
      const svgWidth = parseFloat(svgElement.getAttribute('width') || '120');
      const svgHeight = parseFloat(svgElement.getAttribute('height') || '120');
      const newX = (svgWidth - width) / 2;
      const newY = (svgHeight - height) / 2;
      logoPlaceholderOrImage.setAttribute('x', newX.toString());
      logoPlaceholderOrImage.setAttribute('y', newY.toString());
    }
    
    // Apply South Bay-specific customizations to all SVGs, including loading icons
    applySouthBaySvgCustomizations(svgElement);
  }
};

// Function to handle South Bay-specific SVG customizations
export function applySouthBaySvgCustomizations(svgElement: SVGElement) {
  // Check if this is a loading icon SVG
  const isLoadingIcon = svgElement.classList.contains('loadingIcon') || 
                        svgElement.id === 'cityQuestionIcon' ||
                        svgElement.querySelector('.loader-circle, .city-loader');
  
  if (isLoadingIcon) {
    // Add custom styling for South Bay loading icons
    svgElement.style.filter = 'drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.1))';
  }
}

export default southBayCustomization;

