import { ClientSvgCustomization } from './types';

const myrtleBeachPrstCustomization: ClientSvgCustomization = {
  applySvgCustomizations: (svgElement: SVGElement) => {
    // Find the client name text element and update it
    const clientNameText = svgElement.querySelector('#clientNameText');

    if (clientNameText instanceof SVGElement) {
      // Update position for "PRST" text (shorter than typical city names)
      clientNameText.setAttribute('transform', 'translate(44.2 85.4)');
      
      // Update the text content to "PRST"
      const tspan = clientNameText.querySelector('tspan');
      if (tspan) {
        tspan.textContent = 'PRST';
      }
    }
    
    // Update the "Ask" text position to center it
    const askText = svgElement.querySelector('text.st6');
    if (askText instanceof SVGElement) {
      // Center the "Ask" text
      askText.setAttribute('transform', 'translate(47 73.4)');
    }
  }
};

export default myrtleBeachPrstCustomization;
