import { ClientSvgCustomization } from './types';

const client306Customization: ClientSvgCustomization = {
  applySvgCustomizations: (svgElement: SVGElement) => {
    // Find the client name text element and update it
    const clientNameText = svgElement.querySelector('#clientNameText');

    if (clientNameText) {
      // Update position if needed - customize coordinates as required
      if (clientNameText instanceof SVGElement) {
        clientNameText.setAttribute('transform', 'translate(36.5 85.4)');
        clientNameText.textContent = 'Shockey';
      }
    }
  }
};

export default client306Customization;
