import { ClientSvgCustomization } from './types';

const lorainCustomization: ClientSvgCustomization = {
  logos: {
    default: 'https://files.repd.us/manual/Lorain logo.png',
    cityLoaderSvg: 'https://files.repd.us/manual/Lorain logo.png',
    askButtonSvg: 'https://files.repd.us/manual/Lorain logo.png',
    cityIllustrationSvg: 'https://files.repd.us/manual/Lorain logo.png',
    AskTheCityButton: 'https://files.repd.us/manual/Lorain logo.png',
    cityLoader: 'https://files.repd.us/manual/Lorain logo.png',
    askButton: 'https://files.repd.us/manual/Lorain logo.png',
    askTheCityButton: 'https://files.repd.us/manual/Lorain logo.png',
    minifiedButton: 'https://files.repd.us/manual/Lorain logo.png',
    loadingIcon: 'https://files.repd.us/manual/Lorain logo.png',
    QuestionInputLoadingIcon: 'https://files.repd.us/manual/Lorain logo.png'
  },
  
  applySvgCustomizations: (svgElement: SVGElement) => {
    // Special handling for loading icon in QuestionInput
    if (svgElement.classList.contains('loadingIcon') || 
        svgElement.classList.contains('QuestionInput-module__loadingIcon')) {
      
      // Find all image elements and update their href
      const images = svgElement.querySelectorAll('image');
      images.forEach(image => {
        if (image instanceof SVGImageElement) {
          image.setAttribute('href', 'https://files.repd.us/manual/Lorain logo.png');
          
          // Make the image larger - at least 60px wide and 30px tall
          const currentWidth = parseFloat(image.getAttribute('width') || '30');
          const currentHeight = parseFloat(image.getAttribute('height') || '15');
          
          // Double the size but ensure minimum dimensions
          const newWidth = Math.max(currentWidth * 2, 60);
          const newHeight = Math.max(currentHeight * 2, 30);
          
          image.setAttribute('width', newWidth.toString());
          image.setAttribute('height', newHeight.toString());
          
          // Adjust position to keep it centered
          const parentWidth = parseFloat(svgElement.getAttribute('width') || '120');
          const parentHeight = parseFloat(svgElement.getAttribute('height') || '120');
          
          const newX = (parentWidth - newWidth) / 2;
          const newY = (parentHeight - newHeight) / 2;
          
          image.setAttribute('x', newX.toString());
          image.setAttribute('y', newY.toString());
          image.setAttribute('preserveAspectRatio', 'xMidYMid meet');
        }
      });
      
      
      
      // Remove any existing background circles
      const circles = svgElement.querySelectorAll('circle');
      circles.forEach(circle => {
        if (circle.id === 'logoBackground' && circle.parentNode) {
          circle.parentNode.removeChild(circle);
        }
      });
      
      return; // Exit early for loading icons
    }
    
    // Find the client name text element and update it
    const clientNameText = svgElement.querySelector('#clientNameText');
    if (clientNameText) {
      clientNameText.setAttribute('font-size', '14px !important');
      clientNameText.setAttribute('style', 'font-size: 14px !important;');

      // Update the text content to "Lorain"
      const tspan = clientNameText.querySelector('tspan');
      if (tspan) {
        tspan.textContent = 'Lorain';
      }
      
      // Update position if needed
      if (clientNameText instanceof SVGElement) {
        clientNameText.setAttribute('transform', 'translate(39.2 85.4)');
      }
    }
    
    // Update the "Ask" text position
    const askText = svgElement.querySelector('text.st6');
    if (askText instanceof SVGElement) {
      askText.setAttribute('font-size', '14px !important');
      askText.setAttribute('style', 'font-size: 14px !important;');
      askText.setAttribute('transform', 'translate(46.2 71.4)');
    }
    
    // Hide the white circle background
    const logoBackground = svgElement.querySelector('#logoBackground');
    if (logoBackground && logoBackground.parentNode) {
      logoBackground.parentNode.removeChild(logoBackground);
    }
    
    
    
    // Update logo placeholder dimensions and position
    const logoPlaceholder = svgElement.querySelector('#logoPlaceholder');
    if (logoPlaceholder instanceof SVGElement) {
      if (logoPlaceholder.tagName.toLowerCase() === 'image') {
        logoPlaceholder.setAttribute('x', '31');
        logoPlaceholder.setAttribute('y', '31');
        logoPlaceholder.setAttribute('width', '60');
        logoPlaceholder.setAttribute('height', '30');
        logoPlaceholder.setAttribute('preserveAspectRatio', 'xMidYMid meet');
      }
    }
    
    // Find the logo placeholder or image
    const logoPlaceholderOrImage = svgElement.querySelector('#logoPlaceholder, #cityLoaderButtonLogoPlaceholder, image[data-client-logo="true"]');
    if (logoPlaceholderOrImage instanceof SVGElement) {
      // Adjust the logo size and position
      const width = parseFloat(logoPlaceholderOrImage.getAttribute('width') || '40');
      const height = parseFloat(logoPlaceholderOrImage.getAttribute('height') || '40');
      
      // Make the logo larger - at least 60px wide and 30px tall
      const newWidth = Math.max(width * 1.5, 60);
      const newHeight = Math.max(height * 1.5, 30);
      
      logoPlaceholderOrImage.setAttribute('width', newWidth.toString());
      logoPlaceholderOrImage.setAttribute('height', newHeight.toString());
      
      // Center the logo if needed
      const svgWidth = parseFloat(svgElement.getAttribute('width') || '120');
      const svgHeight = parseFloat(svgElement.getAttribute('height') || '120');
      const newX = (svgWidth - newWidth) / 2;
      const newY = (svgHeight - newHeight) / 2;
      logoPlaceholderOrImage.setAttribute('x', newX.toString());
      logoPlaceholderOrImage.setAttribute('y', newY.toString());
    }
  }
};

// Add a DOM observer to handle dynamically loaded content
function setupLorainDomObserver() {
  if (typeof window !== 'undefined' && typeof document !== 'undefined') {
    // Function to apply logo to main page elements
    const applyLogoToMainPage = () => {
      // Find the city illustration logo on the main page
      const mainPageLogos = document.querySelectorAll('.city-illustration-logo, #cityIllustrationLogo, .CityIllustration-module__logo, img[alt="City Question Icon"], #cityLoaderLogo, .loadingIcon');
      
      mainPageLogos.forEach(logo => {
        if (logo instanceof HTMLImageElement) {
          logo.src = 'https://files.repd.us/manual/Lorain logo.png';
          logo.style.display = 'block';
          logo.style.maxWidth = '200px';
          logo.style.maxHeight = '100px';
          logo.style.backgroundColor = 'transparent';
          logo.style.borderRadius = '0';
        }
      });

      // Find SVG images that might contain the logo
      const svgImages = document.querySelectorAll('svg image');
      svgImages.forEach(image => {
        if (image instanceof SVGImageElement) {
          const href = image.getAttribute('href') || '';
          if (href.includes('logo') || href.includes('icon')) {
            image.setAttribute('href', 'https://files.repd.us/manual/Lorain logo.png');
          }
        }
      });
    };

    // Apply immediately
    applyLogoToMainPage();

    // Set up observer
    const observer = new MutationObserver(() => {
      applyLogoToMainPage();
    });

    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    return observer;
  }
  return null;
}

// Export the observer setup function
export { setupLorainDomObserver };

// Note: setupDomObserver is called separately and not part of the main customization interface

export default lorainCustomization;
