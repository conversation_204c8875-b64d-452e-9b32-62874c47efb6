import { ClientSvgCustomization } from './types';

const mountJacksonCustomization: ClientSvgCustomization = {
  applySvgCustomizations: (svgElement: SVGElement) => {
    // Find the client name text element and update it
    const clientNameText = svgElement.querySelector('#clientNameText');

    if (clientNameText) {
      // Update the text content to "the Town"
      const tspan = clientNameText.querySelector('tspan');

      if (tspan) {
        tspan.textContent = 'the Town';
      }

      // Update position if needed
      if (clientNameText instanceof SVGElement) {
        clientNameText.setAttribute('transform', 'translate(32.5 85.4)');
      }
    }
  }
};

export default mountJacksonCustomization;
