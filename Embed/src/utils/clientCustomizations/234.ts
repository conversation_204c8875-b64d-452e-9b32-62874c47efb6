import { ClientSvgCustomization } from './types';

const tulareCustomization: ClientSvgCustomization = {
  applySvgCustomizations: (svgElement: SVGElement) => {
    // Text content is now handled by the centralized button text mapping
    // Only handle positioning and styling here if needed
    const clientNameText = svgElement.querySelector('#clientNameText');

    if (clientNameText instanceof SVGElement) {
      // Update position if needed for "City Hall" text
      clientNameText.setAttribute('transform', 'translate(35.2 85.4)');
    }

    // Update the "Ask" text position if needed
    const askText = svgElement.querySelector('text.st6');
    if (askText instanceof SVGElement) {
      askText.setAttribute('transform', 'translate(46.2 71.4)');
    }
  }
};

export default tulareCustomization;
