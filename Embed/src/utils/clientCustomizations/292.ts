import { ClientSvgCustomization } from './types';

const loudounCustomization: ClientSvgCustomization = {
  applySvgCustomizations: (svgElement: SVGElement) => {
    // Find the client name text element and update it
    const clientNameText = svgElement.querySelector('#clientNameText');

    if (clientNameText) {
      // Update the text content to "the County"
      const tspan = clientNameText.querySelector('tspan');

      if (tspan) {
        tspan.textContent = 'The County';
      }

      // Update position if needed
      if (clientNameText instanceof SVGElement) {
        clientNameText.setAttribute('transform', 'translate(28.5 85.4)');
      }
    }
  }
};

export default loudounCustomization;