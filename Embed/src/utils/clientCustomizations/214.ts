import { ClientSvgCustomization } from './types';

const broadviewCustomization: ClientSvgCustomization = {
  applySvgCustomizations: (svgElement: SVGElement) => {
    // Text content is now handled by the centralized button text mapping
    // Only handle positioning and styling here if needed
    const clientNameText = svgElement.querySelector('#clientNameText');

    if (clientNameText instanceof SVGElement) {
      // Update position for "Broadview" text
      clientNameText.setAttribute('transform', 'translate(31 85.4)');
    }

    // Update the "Ask" text position
    const askText = svgElement.querySelector('text.st6');
    if (askText instanceof SVGElement) {
      askText.setAttribute('transform', 'translate(48.2 71.4)');
    }
  }
};

export default broadviewCustomization;
