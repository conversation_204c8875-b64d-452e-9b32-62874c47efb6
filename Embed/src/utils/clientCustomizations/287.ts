import { ClientSvgCustomization } from './types';

const roelandParkCustomization: ClientSvgCustomization = {
  applySvgCustomizations: (svgElement: SVGElement) => {
    // Text content is now handled by the centralized button text mapping
    // Only handle positioning and styling here if needed
    const clientNameText = svgElement.querySelector('#clientNameText');

    if (clientNameText instanceof SVGElement) {
      // Update position for "Roeland Park" text (longer than typical city names)
      clientNameText.setAttribute('transform', 'translate(29.3 84.4)');
      // Make font slightly smaller to fit
      clientNameText.setAttribute('style', 'font-size: 10px !important;');
    }

    // Update the "Ask" text position
    const askText = svgElement.querySelector('text.st6');
    if (askText instanceof SVGElement) {
      askText.setAttribute('transform', 'translate(50.2 71.4)');
      askText.setAttribute('style', 'font-size: 10px !important;');
    }
  }
};

export default roelandParkCustomization;
