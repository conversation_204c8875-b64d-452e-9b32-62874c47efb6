import React from "react";
import type { ClientInterface } from '../interfaces/client.interface';
import { createButtonGradientColors } from './colorUtils';
import clientCustomizations from './clientCustomizations';
import { ClientColorService } from '../services/clientColorService';
import { getMinimizedButtonLogoUrl } from '../config/clientLogoConfig';

// Client IDs that require white background circles for their logos
const clientsLogosWhiteBg = ['293', '288'];

interface LogoDimensions {
  width?: number;
  height?: number;
  x?: number;
  y?: number;
  scaleFactor?: number;
  minWidth?: number;
  minHeight?: number;
  centerLogo?: boolean;
}

const clientLogoDimensions: Record<string, LogoDimensions> = {
  '211': { // Arlington
    width: 55,
  },
  '214': { // Broadview
    width: 30,
    y: 23,
    x: 45
  },
  '221': { // Salisbury
    width: 50,
    y: 27,
    x: 35
  },
  '234': { // Tulare
    width: 50,
  },
  '262': { // City of Leeds
    width: 55,
    y: 29,
    x: 33
  },
  '263': { // City of Gadsden
    width: 55,
    y: 29,
    x: 33
  },
  '269': { // Traverse City
    width: 55,
  },
  '270': { // Purcellville
    width: 55,
  },
  '273': { // Cullman
    width: 45,
    y: 29,
    x: 38
  },
  '275': { // Redondo PD
    width: 30,
  },
  '279': { // Maple Valley
    width: 45,
    y: 28,
    x: 38
  },
  '281': { // Morrisville
    width: 55,
  },
  '282': { // Kirkland
    width: 55,
  },
  '287': { // Roeland Park
    width: 50,
    y: 27,
    x: 35
  },
  '288': { // Park City
    width: 30,
  },
  '292': { // Loudoun County
    width: 80,
    y: 27,
    x: 21
  },
  '293': { // Mount Jackson
    width: 28,
  },
  '295': { // Page
    width: 50
  },
  '296': { // Richland
    width: 50,
    y: 27,
    x: 35
  },
  '300': { // Pleasantville
    width: 50,
    y: 27,
    x: 35.5
  },
  '302': { // Toledo
    width: 45,
    y: 28.5,
    x: 38
  },
  '304': { // Burke County
    width: 70,
    y: 23.5,
    x: 25
  },
  '306': { // Shockey
    width: 70,
    y: 30,
    x: 26
  }
};

/**
 * Apply client-specific logo dimensions to a logo element
 * @param logoElement The logo element (image or rect)
 * @param clientId The client ID
 * @param svgElement The parent SVG element for context
 * @param originalDimensions Original dimensions of the element
 */
function applyClientLogoDimensions(
  logoElement: SVGElement,
  clientId: string,
  svgElement: SVGElement,
  originalDimensions: { x: number; y: number; width: number; height: number }
): void {
  const clientDimensions = clientLogoDimensions[clientId];
  if (!clientDimensions) return;

  const { x: origX, y: origY, width: origWidth, height: origHeight } = originalDimensions;

  // Apply specific width and height if provided
  let newWidth = clientDimensions.width || origWidth;
  let newHeight = clientDimensions.height || origHeight;

  // Apply scale factor if provided
  if (clientDimensions.scaleFactor) {
    newWidth = origWidth * clientDimensions.scaleFactor;
    newHeight = origHeight * clientDimensions.scaleFactor;
  }

  // Apply minimum dimensions if provided
  if (clientDimensions.minWidth) {
    newWidth = Math.max(newWidth, clientDimensions.minWidth);
  }
  if (clientDimensions.minHeight) {
    newHeight = Math.max(newHeight, clientDimensions.minHeight);
  }

  // Calculate position
  let newX = clientDimensions.x !== undefined ? clientDimensions.x : origX;
  let newY = clientDimensions.y !== undefined ? clientDimensions.y : origY;

  // Center the logo if requested
  if (clientDimensions.centerLogo) {
    const svgWidth = parseFloat(svgElement.getAttribute('width') || '120');
    const svgHeight = parseFloat(svgElement.getAttribute('height') || '120');
    newX = (svgWidth - newWidth) / 2;
    newY = (svgHeight - newHeight) / 2;
  } else if (clientDimensions.x === undefined && clientDimensions.y === undefined) {
    // If no specific position is provided, center the resized logo in the original space
    newX = origX + (origWidth - newWidth) / 2;
    newY = origY + (origHeight - newHeight) / 2;
  }

  // Apply the calculated dimensions
  logoElement.setAttribute('x', newX.toString());
  logoElement.setAttribute('y', newY.toString());
  logoElement.setAttribute('width', newWidth.toString());
  logoElement.setAttribute('height', newHeight.toString());
}

/**
 * Helper function to dynamically apply client-specific colors and logo to SVG elements
 * @param svgElement The SVG element to modify
 * @param client The client data containing logoURL and other customizations
 */
export function applyClientCustomizationsToSvg(svgElement: SVGElement, client: ClientInterface | null) {
  if (!svgElement || !client) return;

  console.log('Processing SVG customization for client:', client.id, 'logoURL:', client.logoURL);
  const clientId = client.id;

  // Since svgUtils.ts is specifically for MinimizedButton, always check for MinimizedButton-specific overrides first
  let logoURL = getMinimizedButtonLogoUrl(clientId, client.logoURL);

  // Special case for Lorain loading icon
  if (clientId === '278' &&
      (svgElement.classList.contains('loadingIcon') ||
       svgElement.id === 'cityQuestionIcon' ||
       svgElement.classList.contains('QuestionInput-module__loadingIcon'))) {
    logoURL = 'https://files.repd.us/manual/Lorain logo.png';
    console.log('Using Lorain-specific logo for loading icon:', logoURL);
  }

  // SVG-specific logo overrides - check these FIRST
  const clientCustomization = clientCustomizations[clientId];
  if (clientCustomization) {
    // Get the SVG ID or try to determine the SVG type from its content or class
    const svgId = svgElement.id || '';

    // Try to determine SVG type if no ID is present or if the ID isn't in our mapping
    let svgType = svgId;

    // Only check for logo overrides if the customization has a logos property with entries
    if (clientCustomization.logos && Object.keys(clientCustomization.logos).length > 0) {
      // Check if the ID is directly in our mapping
      if (clientCustomization.logos[svgType]) {
        console.log('Found exact match for SVG ID:', svgType);
        logoURL = clientCustomization.logos[svgType];
      }
      // If no match by ID, try to determine the SVG type from its content
      else {
        // Check for specific elements that indicate the SVG type
        if (svgElement.querySelector('.loader-circle, .city-loader') || 
            svgElement.classList.contains('loadingIcon') || 
            svgElement.classList.contains('QuestionInput-module__loadingIcon')) {
          svgType = 'cityLoaderSvg';
        } else if (svgElement.id === 'AskTheCityButton' || svgElement.querySelector('#askButtonIcon, #clientNameText')) {
          svgType = 'askButtonSvg';
        } else if (svgElement.querySelector('.city-illustration, .skyline')) {
          svgType = 'cityIllustrationSvg';
        }

        if (svgType !== svgId) {
          logoURL = clientCustomization.logos[svgType] || logoURL;
        }
      }

      // If we still don't have a logo URL, use the default from our mapping
      if (!logoURL && clientCustomization.logos.default) {
        logoURL = clientCustomization.logos.default;
      }

      console.log('Detected SVG type:', svgType, 'for element with ID:', svgId, 'using logo:', logoURL);
    }
    
    // Apply client-specific SVG customizations
    clientCustomization.applySvgCustomizations(svgElement);
  }

  // Apply client logo URL if available
  if (logoURL) {
    console.log('Applying logo URL:', logoURL, 'for client:', clientId);

    // First, hide any existing icons that might show behind our logo
    const existingIcons = svgElement.querySelectorAll('#askButtonIcon, #cityLoaderButtonIcon, .icon-element, .default-icon');
    existingIcons.forEach(icon => {
      if (icon instanceof SVGElement) {
        icon.style.display = 'none';
        console.log('Hiding existing icon element:', icon.id || 'unnamed');
      }
    });

    // Method 1: Set href on images with data-client-logo="true"
    const logoImages = svgElement.querySelectorAll('image[data-client-logo="true"], #img1');
    logoImages.forEach(image => {
      if (image instanceof SVGElement) {
        image.setAttribute('href', logoURL);
        console.log('Set href on image element:', image.id || 'unnamed', logoURL);
      }
    });

    // Method 2: Handle logoPlaceholder and cityLoaderButtonLogoPlaceholder elements
    const logoPlaceholders = svgElement.querySelectorAll('#logoPlaceholder, #cityLoaderButtonLogoPlaceholder');
    logoPlaceholders.forEach(placeholder => {
      if (placeholder instanceof SVGElement) {
        // Convert rect to image element if needed
        if (placeholder.tagName.toLowerCase() === 'rect') {
          const x = parseFloat(placeholder.getAttribute('x') || '0');
          const y = parseFloat(placeholder.getAttribute('y') || '0');
          const width = parseFloat(placeholder.getAttribute('width') || '168');
          const height = parseFloat(placeholder.getAttribute('height') || '168');

          // Add white circle background for specific clients (e.g., Mount Jackson - client 293)
          if (clientsLogosWhiteBg.includes(clientId)) {
            const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            circle.setAttribute('cx', (x + width / 2).toString());
            circle.setAttribute('cy', (y + height / 2).toString());
            circle.setAttribute('r', (Math.min(width, height) / 2).toString());
            circle.setAttribute('fill', 'white');
            circle.setAttribute('id', 'logoBackground');

            // Insert the circle before the image
            if (placeholder.parentNode) {
              placeholder.parentNode.insertBefore(circle, placeholder);
            }
          }

          // Now create the image element - make it smaller to fit within the circle
          const image = document.createElementNS('http://www.w3.org/2000/svg', 'image');
          image.setAttribute('href', logoURL);

          // Determine if this is the CityQuestionIcon
          const isCityQuestionIcon =
            svgElement.id === 'cityQuestionIcon' ||
            svgElement.classList.contains('CityIllustration-module__icon') ||
            placeholder.id === 'cityLoaderButtonLogoPlaceholder';

          // Calculate new dimensions - make CityQuestionIcon even smaller (60% of original)
          const scaleFactor = isCityQuestionIcon ? 0.7 : 0.9;
          let newWidth = width * scaleFactor;
          let newHeight = height * scaleFactor;
          // Adjust position to keep it centered
          let newX = x + (width - newWidth) / 2;
          let newY = y + (height - newHeight) / 2;

          // Apply client-specific dimensions if configured
          const originalDimensions = { x: newX, y: newY, width: newWidth, height: newHeight };
          image.setAttribute('x', newX.toString());
          image.setAttribute('y', newY.toString());
          image.setAttribute('width', newWidth.toString());
          image.setAttribute('height', newHeight.toString());

          // Apply client-specific logo dimensions after setting initial dimensions
          applyClientLogoDimensions(image, clientId, svgElement, originalDimensions);
          image.setAttribute('preserveAspectRatio', 'xMidYMid meet');
          image.setAttribute('data-client-logo', 'true');
          image.setAttribute('id', 'img1'); // Add id="img1" to make it easier to target

          // Preserve the original ID if it exists
          if (placeholder.id) {
            image.setAttribute('id', placeholder.id);
          }

          // Replace the rect with the image
          if (placeholder.parentNode) {
            placeholder.parentNode.replaceChild(image, placeholder);
            console.log('Replaced rect placeholder with image:', logoURL, isCityQuestionIcon ? '(CityQuestionIcon - smaller size)' : '');
          }
        } else if (placeholder.tagName.toLowerCase() === 'image') {
          // If it's already an image, just update the href
          placeholder.setAttribute('href', logoURL);

          // Get current dimensions
          const x = parseFloat(placeholder.getAttribute('x') || '0');
          const y = parseFloat(placeholder.getAttribute('y') || '0');
          const width = parseFloat(placeholder.getAttribute('width') || '168');
          const height = parseFloat(placeholder.getAttribute('height') || '168');

          // Determine if this is the CityQuestionIcon
          const isCityQuestionIcon =
            svgElement.id === 'cityQuestionIcon' ||
            svgElement.classList.contains('CityIllustration-module__icon') ||
            placeholder.id === 'cityLoaderButtonLogoPlaceholder';

          // Calculate new dimensions - make CityQuestionIcon even smaller (60% of original)
          const scaleFactor = isCityQuestionIcon ? 0.7 : 1;
          let newWidth = width * scaleFactor;
          let newHeight = height * scaleFactor;
          // Adjust position to keep it centered
          let newX = x + (width - newWidth) / 2;
          let newY = y + (height - newHeight) / 2;

          // Update dimensions to make it smaller
          placeholder.setAttribute('x', newX.toString());
          placeholder.setAttribute('y', newY.toString());
          placeholder.setAttribute('width', newWidth.toString());
          placeholder.setAttribute('height', newHeight.toString());

          // Apply client-specific logo dimensions
          const originalDimensions = { x: newX, y: newY, width: newWidth, height: newHeight };
          applyClientLogoDimensions(placeholder, clientId, svgElement, originalDimensions);

          // Remove any existing background circle for all clients except 293
          const existingBackground = svgElement.querySelector('#logoBackground');
          if (existingBackground && existingBackground.parentNode && !clientsLogosWhiteBg.includes(clientId)) {
            existingBackground.parentNode.removeChild(existingBackground);
          }

          // Add white circle background for specific clients (e.g., Mount Jackson - client 293)
          if (clientsLogosWhiteBg.includes(clientId) && !existingBackground) {
            const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            circle.setAttribute('cx', (x + width / 2).toString());
            circle.setAttribute('cy', (y + height / 2).toString());
            circle.setAttribute('r', (Math.min(width + 2, height + 2) / 2).toString());
            circle.setAttribute('fill', 'white');
            circle.setAttribute('id', 'logoBackground');

            // Insert the circle before the image
            if (placeholder.parentNode) {
              placeholder.parentNode.insertBefore(circle, placeholder);
            }
          }

          console.log('Updated existing image href and adjusted size:', logoURL, isCityQuestionIcon ? '(CityQuestionIcon - smaller size)' : '');
        }
      }
    });

    // If no placeholder was found, try to find a suitable element to replace
    if (logoPlaceholders.length === 0 && logoImages.length === 0) {
      // Look for common icon elements
      const iconElements = svgElement.querySelectorAll('.icon, .svg-icon, path[d*="M12"], circle.icon-circle');

      if (iconElements.length > 0) {
        console.log('No placeholder found, but found icon elements to replace:', iconElements.length);

        // Hide all icon elements
        iconElements.forEach(icon => {
          if (icon instanceof SVGElement) {
            icon.style.display = 'none';
          }
        });

        // Create a new image element
        const svgRoot = svgElement.querySelector('svg') || svgElement;
        const image = document.createElementNS('http://www.w3.org/2000/svg', 'image');
        image.setAttribute('href', logoURL);
        image.setAttribute('x', '0');
        image.setAttribute('y', '0');
        image.setAttribute('width', '100%');
        image.setAttribute('height', '100%');
        image.setAttribute('preserveAspectRatio', 'xMidYMid meet');
        image.setAttribute('data-client-logo', 'true');

        // Add the image to the SVG
        svgRoot.appendChild(image);
        console.log('Added new image element as fallback:', logoURL);
      }
    }
  } else {
    console.log('No logo URL available for client:', clientId);
  }

  // Apply client-specific gradient colors to SVG gradients (like in ask-the-city-button.svg)
  updateSvgGradientColors(svgElement, client);

  // Update SVG button text based on client mapping
  updateSvgButtonText(svgElement, clientId);

  // Apply client-specific color customizations
  applyClientSpecificColors(svgElement, clientId);
}

/**
 * Update SVG text content based on client button text mapping
 * This handles the text in ask-the-city-button.svg
 */
function updateSvgButtonText(svgElement: SVGElement, clientId: string): void {
  const buttonText = ClientColorService.getClientButtonText(clientId);
  if (!buttonText) return;

  // Split the button text into "Ask" and the rest
  const parts = buttonText.split(' ');
  const askText = parts[0]; // "Ask"
  const remainingText = parts.slice(1).join(' '); // Everything after "Ask"

  // Update the "Ask" text
  const askTextElement = svgElement.querySelector('text.st6');
  if (askTextElement) {
    const askTspan = askTextElement.querySelector('tspan');
    if (askTspan) {
      askTspan.textContent = askText;
    }
  }

  // Update the client name text (everything after "Ask")
  const clientNameText = svgElement.querySelector('#clientNameText');
  if (clientNameText) {
    const tspan = clientNameText.querySelector('tspan');
    if (tspan) {
      tspan.textContent = remainingText;
    }
  }
}

/**
 * Update SVG gradient colors to match client colors
 * This handles gradients like the one in ask-the-city-button.svg
 */
function updateSvgGradientColors(svgElement: SVGElement, client: ClientInterface | null): void {
  if (!client) return;

  // Get client color or use default
  const clientColor = client.topBarColour || '#E94E77';

  // Create gradient colors that match the button gradient pattern
  // Original: #ff3974 (lighter) -> #e73067 (darker)
  const { lightGradientColor, darkGradientColor } = createButtonGradientColors(clientColor);

  // Find all gradient stop elements and update their colors
  const gradientStops = svgElement.querySelectorAll('linearGradient stop, radialGradient stop');

  gradientStops.forEach(stop => {
    if (stop instanceof SVGElement) {
      const stopColor = stop.getAttribute('stop-color');
      const offset = stop.getAttribute('offset');

      // Update gradient stops based on their position
      if (stopColor && (stopColor.includes('#ff3974') || stopColor.includes('#e73067'))) {
        if (offset === '0') {
          // First stop - use lighter color
          stop.setAttribute('stop-color', lightGradientColor);
        } else if (offset === '1') {
          // Last stop - use darker color
          stop.setAttribute('stop-color', darkGradientColor);
        }
      }
    }
  });
}



/**
 * Helper function to apply client-specific color schemes
 * @param svgElement The SVG element to modify
 * @param clientId The client ID
 */
function applyClientSpecificColors(svgElement: SVGElement, clientId: string) {
  // Apply Emporia colors
  if (clientId === '283') {
    // Find all elements with pink colors
    const pinkElements = svgElement.querySelectorAll(
      '[fill="#E94E77"], [fill="#EA3269"], [fill="#E83168"], [fill="#E73067"], ' +
      '[stroke="#E94E77"], [stroke="#EA3269"], [stroke="#E83168"], [stroke="#E73067"]'
    );

    // Replace with Emporia blue
    pinkElements.forEach(element => {
      if (element instanceof SVGElement) {
        if (element.hasAttribute('fill') && element.getAttribute('fill')?.includes('#E')) {
          element.setAttribute('fill', '#0E4EA0');
        }
        if (element.hasAttribute('stroke') && element.getAttribute('stroke')?.includes('#E')) {
          element.setAttribute('stroke', '#0E4EA0');
        }
      }
    });

    // Find all gradient stops with pink colors
    const pinkStops = svgElement.querySelectorAll(
      'stop[stop-color="#FF3974"], stop[stop-color="#E73067"], ' +
      'stop[stop-color="#E94E77"], stop[stop-color="#EA3269"], stop[stop-color="#E83168"]'
    );

    // Replace with Emporia blue
    pinkStops.forEach(stop => {
      if (stop instanceof SVGElement) {
        stop.setAttribute('stop-color', '#0E4EA0');
      }
    });
  }
  
  // Apply York colors and layout
  if (clientId === '294') {
    // Update text positioning for York
    const clientNameText = svgElement.querySelector('#clientNameText');
    if (clientNameText instanceof SVGElement) {
      clientNameText.setAttribute('font-size', '14px');
      clientNameText.setAttribute('transform', 'translate(30.2 85.4)');
    }

    // Update the "Ask" text position
    const askText = svgElement.querySelector('text.st6');
    if (askText instanceof SVGElement) {
      askText.setAttribute('font-size', '14px');
      askText.setAttribute('transform', 'translate(46.2 71.4)');
    }
    
    // Note: Logo placeholder dimensions are now handled by the centralized clientLogoDimensions system
    
    // Add or update the white background rect
    let bgRect = svgElement.querySelector('rect[x="31"][y="35"]');
    if (!bgRect) {
      bgRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      const logoPlaceholder = svgElement.querySelector('#logoPlaceholder');
      if (logoPlaceholder && logoPlaceholder.parentNode) {
        logoPlaceholder.parentNode.insertBefore(bgRect, logoPlaceholder);
      } else {
        svgElement.appendChild(bgRect);
      }
    }
    
    if (bgRect instanceof SVGElement) {
      bgRect.setAttribute('x', '31');
      bgRect.setAttribute('y', '35');
      bgRect.setAttribute('width', '60');
      bgRect.setAttribute('height', '20');
      bgRect.setAttribute('fill', 'white');
      bgRect.setAttribute('rx', '10');
      bgRect.setAttribute('ry', '10');
    }
  }

  // Apply Mount Jackson-specific customizations
  if (clientId === '293') {
    // Mount Jackson customizations are now handled by updateSvgButtonText function
    // Any additional styling can be added here if needed
  }
}

/**
 * Legacy function for backward compatibility
 * @param svgElement The SVG element to modify
 * @param clientId The client ID
 * @deprecated Use applyClientCustomizationsToSvg with full client data instead
 */
export function applyClientColorsToSvg(svgElement: SVGElement, clientId: string | null) {
  if (!svgElement || !clientId) return;

  // Apply only color customizations for backward compatibility
  applyClientSpecificColors(svgElement, clientId);
}

/**
 * Hook to apply client customizations to an SVG element
 * @param svgRef React ref to the SVG element
 * @param client Optional client data for full customizations including logo
 */
export function useClientSvgColors(svgRef: React.RefObject<SVGSVGElement>, client?: ClientInterface | null): void {
  React.useEffect(() => {
    if (svgRef.current) {
      if (client) {
        // Use full client data if available
        updateSvgGradientColors(svgRef.current, client);
        applyClientColorsToSvg(svgRef.current, client.id);
        applyClientCustomizationsToSvg(svgRef.current, client);
      } else {
        // Fallback to legacy behavior for backward compatibility
        const clientId = localStorage.getItem('clientId');
        applyClientColorsToSvg(svgRef.current, clientId);
      }
    }
  }, [svgRef, client]);
}

/**
 * Apply client customizations to all SVGs on the page
 * This is useful for SVGs that are not managed by React components
 * @param client The client data containing logoURL and other customizations
 */
export function applyClientCustomizationsToAllSvgs(client: ClientInterface | null): void {
  if (!client) return;

  // Find all SVG elements on the page
  const allSvgs = document.querySelectorAll('svg');

  allSvgs.forEach(svg => {
    applyClientCustomizationsToSvg(svg, client);
  });
}

/**
 * Manual test function for debugging - can be called from browser console
 * Usage: window.testLogoReplacement()
 */
export function testLogoReplacement(): void {
  console.log('=== Manual Logo Replacement Test ===');

  // Find all SVGs
  const allSvgs = document.querySelectorAll('svg');
  console.log('Found SVGs:', allSvgs.length);

  allSvgs.forEach((svg, index) => {
    console.log(`SVG ${index + 1}:`, svg.id || 'no-id', svg);

    // Look for logo placeholders
    const placeholders = svg.querySelectorAll('#logoPlaceholder, #cityLoaderButtonLogoPlaceholder');
    console.log(`  Found ${placeholders.length} placeholders in SVG ${index + 1}`);

    placeholders.forEach((placeholder, pIndex) => {
      console.log(`    Placeholder ${pIndex + 1}:`, placeholder.id, placeholder.tagName, placeholder);

      if (placeholder.tagName.toLowerCase() === 'rect') {
        console.log('    This is a rect that should be replaced');
        console.log('    Attributes:', {
          x: placeholder.getAttribute('x'),
          y: placeholder.getAttribute('y'),
          width: placeholder.getAttribute('width'),
          height: placeholder.getAttribute('height'),
          fill: placeholder.getAttribute('fill')
        });
      }
    });
  });
}

/**
 * Test function to check current client and logo availability
 */
export function testCurrentClient(): void {
  console.log('=== Current Client Test ===');

  // Check localStorage
  const clientId = localStorage.getItem('clientId');
  console.log('Client ID from localStorage:', clientId);

  // Check URL
  const pathMatch = window.location.pathname.match(/\/([^\/]+)/);
  const clientNameFromUrl = pathMatch ? pathMatch[1].toLowerCase() : null;
  console.log('Client name from URL:', clientNameFromUrl);

  // Check hardcoded mapping
  const hardcodedLogos: Record<string, string> = {
    '283': 'https://files.repd.us/manual/Emporia-Icon-White.png',
    '295': 'https://files.repd.us/manual/Page-Logo-White.png',
    // Add a few more for testing
  };

  if (clientId) {
    const logoURL = hardcodedLogos[clientId] || hardcodedLogos[String(clientId)];
    console.log('Logo URL for current client:', logoURL);
    console.log('Has logo in mapping:', !!logoURL);
  }
}

/**
 * Comprehensive debug function to analyze SVG logo issues
 */
export function debugSvgLogos(): void {
  console.log('=== SVG Logo Debug ===');

  // 1. Check client ID
  const clientId = localStorage.getItem('clientId');
  console.log('Current client ID:', clientId);

  // 2. Check available logo mappings
  console.log('Available logo mappings for this client:',
    clientId && clientLogoDimensions[clientId] ? Object.keys(clientLogoDimensions[clientId]) : 'None');

  // 3. Analyze all SVGs on the page
  const allSvgs = document.querySelectorAll('svg');
  console.log(`Found ${allSvgs.length} SVGs on the page`);

  allSvgs.forEach((svg, index) => {
    // Basic SVG info
    console.group(`SVG ${index + 1}:`);
    console.log('ID:', svg.id || 'none');
    console.log('Classes:', svg.className.baseVal || 'none');

    // Check for logo-related elements
    const logoImages = svg.querySelectorAll('image[data-client-logo="true"], #img1');
    console.log(`Logo images found: ${logoImages.length}`);
    logoImages.forEach((img, i) => {
      if (img instanceof SVGElement) {
        console.log(`  Image ${i + 1}:`, {
          id: img.id,
          href: img.getAttribute('href'),
          dataClientLogo: img.getAttribute('data-client-logo')
        });
      }
    });

    // Check for placeholder elements
    const placeholders = svg.querySelectorAll('#logoPlaceholder, #cityLoaderButtonLogoPlaceholder');
    console.log(`Placeholder elements found: ${placeholders.length}`);
    placeholders.forEach((placeholder, i) => {
      if (placeholder instanceof SVGElement) {
        console.log(`  Placeholder ${i + 1}:`, {
          id: placeholder.id,
          tagName: placeholder.tagName,
          href: placeholder.tagName.toLowerCase() === 'image' ?
            placeholder.getAttribute('href') : 'N/A'
        });
      }
    });

    // Check for specific classes that would trigger our detection
    console.log('Has loader classes:', !!svg.querySelector('.loader-circle, .city-loader'));
    console.log('Has ask button classes:', !!(svg.id === 'AskTheCityButton' || svg.querySelector('#askButtonIcon, #clientNameText')));
    console.log('Has city illustration classes:', !!svg.querySelector('.city-illustration, .skyline'));

    console.groupEnd();
  });

  // 4. Check if our detection would work
  console.log('SVG type detection test:');
  allSvgs.forEach((svg, index) => {
    let detectedType = svg.id || '';
    if (!detectedType) {
      if (svg.querySelector('.loader-circle, .city-loader')) {
        detectedType = 'cityLoaderSvg';
      } else if (svg.id === 'AskTheCityButton' || svg.querySelector('#askButtonIcon, #clientNameText')) {
        detectedType = 'askButtonSvg';
      } else if (svg.querySelector('.city-illustration, .skyline')) {
        detectedType = 'cityIllustrationSvg';
      }
    }
    console.log(`SVG ${index + 1}: Detected as "${detectedType || 'unknown'}"`);
  });
}

/**
 * Add or update logo dimensions for a specific client
 * This is a helper function to make it easy to configure client logo dimensions
 * @param clientId The client ID
 * @param dimensions The logo dimensions configuration
 */
export function setClientLogoDimensions(clientId: string, dimensions: LogoDimensions): void {
  clientLogoDimensions[clientId] = dimensions;
  console.log(`Updated logo dimensions for client ${clientId}:`, dimensions);
}

/**
 * Get the current logo dimensions configuration for a client
 * @param clientId The client ID
 * @returns The logo dimensions configuration or undefined if not set
 */
export function getClientLogoDimensions(clientId: string): LogoDimensions | undefined {
  return clientLogoDimensions[clientId];
}

/**
 * Get all configured client logo dimensions
 * @returns Record of all client logo dimensions
 */
export function getAllClientLogoDimensions(): Record<string, LogoDimensions> {
  return { ...clientLogoDimensions };
}

// Make the test functions available globally for debugging
(window as any).testLogoReplacement = testLogoReplacement;
(window as any).testCurrentClient = testCurrentClient;
(window as any).debugSvgLogos = debugSvgLogos;
(window as any).setClientLogoDimensions = setClientLogoDimensions;
(window as any).getClientLogoDimensions = getClientLogoDimensions;
(window as any).getAllClientLogoDimensions = getAllClientLogoDimensions;
