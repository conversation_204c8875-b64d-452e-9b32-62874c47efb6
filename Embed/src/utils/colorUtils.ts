/**
 * Color utility functions for client customization
 * Shared utilities for converting between color formats and creating dynamic color schemes
 */

/**
 * Convert hex color to HSL
 */
export function hexToHsl(hex: string): { h: number; s: number; l: number } {
  const r = parseInt(hex.slice(1, 3), 16) / 255;
  const g = parseInt(hex.slice(3, 5), 16) / 255;
  const b = parseInt(hex.slice(5, 7), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return { h: h * 360, s: s * 100, l: l * 100 };
}

/**
 * Convert HSL to hex color
 */
export function hslToHex(h: number, s: number, l: number): string {
  h /= 360;
  s /= 100;
  l /= 100;

  const hue2rgb = (p: number, q: number, t: number) => {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1/6) return p + (q - p) * 6 * t;
    if (t < 1/2) return q;
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
    return p;
  };

  let r, g, b;
  if (s === 0) {
    r = g = b = l;
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }

  const toHex = (c: number) => {
    const hex = Math.round(c * 255).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}

/**
 * Convert hex color to RGB values
 */
export function hexToRgb(hex: string): string {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `${r}, ${g}, ${b}`;
}

/**
 * Create gradient colors dynamically based on the input color's characteristics
 * This function creates a natural-looking gradient by adjusting lightness relative to the input color
 * instead of forcing fixed lightness values that only work well for specific colors
 */
export function createGradientColors(clientColor: string): { lightColor: string; darkColor: string } {
  // Get the HSL values from the client color
  const { h, s, l } = hexToHsl(clientColor);

  // Calculate dynamic lightness adjustments based on the input color's lightness
  // For darker colors (l < 30%), create a smaller contrast range
  // For medium colors (30% <= l < 70%), create a moderate contrast range
  // For lighter colors (l >= 70%), create a larger contrast range to maintain visibility

  let lightAdjustment: number;
  let darkAdjustment: number;

  lightAdjustment = 10;
  darkAdjustment = -10;

  // Calculate new lightness values, ensuring they stay within valid bounds (0-100)
  const lightLightness = Math.min(100, Math.max(0, l + lightAdjustment));
  const darkLightness = Math.min(100, Math.max(0, l + darkAdjustment));

  // Create the gradient colors using the same hue and saturation but adjusted lightness
  const lightColor = hslToHex(h, s, lightLightness);
  const darkColor = hslToHex(h, s, darkLightness);

  return { lightColor, darkColor };
}

/**
 * Create skeleton shimmer colors dynamically based on client color
 * Creates a subtle shimmer effect with colors that work well for loading states
 */
export function createSkeletonColors(clientColor: string): { light: string; mid: string; dark: string } {
  // Get the HSL values from the client color
  const { h, s, l } = hexToHsl(clientColor);

  // Create skeleton colors with reduced saturation for a more subtle effect
  // and appropriate lightness adjustments for shimmer animation
  const skeletonSaturation = Math.max(10, s * 0.3); // Reduce saturation significantly

  // Adjust lightness based on the input color to ensure good contrast for shimmer
  let baseLightness: number;
  if (l < 30) {
    baseLightness = l + 15; // Lighten dark colors
  } else if (l > 70) {
    baseLightness = l - 20; // Darken light colors
  } else {
    baseLightness = l; // Medium colors work as-is
  }

  // Create the shimmer colors with small lightness variations
  const dark = hslToHex(h, skeletonSaturation, Math.max(0, baseLightness - 5));
  const mid = hslToHex(h, skeletonSaturation, Math.min(100, baseLightness + 5));

  return { light: mid, mid, dark };
}

/**
 * Create button gradient colors dynamically based on the input color's characteristics
 * This creates a subtle gradient effect suitable for buttons by making small lightness adjustments
 */
export function createButtonGradientColors(clientColor: string): { lightGradientColor: string; darkGradientColor: string } {
  // Convert client color to HSL
  const { h, s, l } = hexToHsl(clientColor);

  // Create a subtle gradient for buttons - smaller adjustments than background gradients
  // This ensures buttons maintain good readability while having a subtle gradient effect
  const lightAdjustment = 8;  // Slightly lighter
  const darkAdjustment = -8;  // Slightly darker

  // Calculate new lightness values, ensuring they stay within valid bounds
  const lightLightness = Math.min(100, Math.max(0, l + lightAdjustment));
  const darkLightness = Math.min(100, Math.max(0, l + darkAdjustment));

  // Create the gradient colors
  const lightGradientColor = hslToHex(h, s, lightLightness);
  const darkGradientColor = hslToHex(h, s, darkLightness);

  return { lightGradientColor, darkGradientColor };
}

/**
 * Lighten or darken a color by a percentage
 * @param color Hex color string
 * @param percent Percentage to lighten (positive) or darken (negative)
 */
export function adjustColorLightness(color: string, percent: number): string {
  const { h, s, l } = hexToHsl(color);
  const newL = Math.max(0, Math.min(100, l + percent));
  return hslToHex(h, s, newL);
}

/**
 * Create a color palette based on a primary color
 * Useful for generating consistent color schemes for clients
 */
export function createColorPalette(primaryColor: string) {
  const { h, s, l } = hexToHsl(primaryColor);
  
  return {
    primary: primaryColor,
    light: hslToHex(h, s, Math.min(100, l + 20)),
    dark: hslToHex(h, s, Math.max(0, l - 20)),
    muted: hslToHex(h, Math.max(0, s - 30), l),
    accent: hslToHex((h + 180) % 360, s, l), // Complementary color
  };
}
