/**
 * Normalizes IDs for comparison by converting to string and trimming
 * @param id The ID to normalize
 * @returns Normalized ID string
 */
export function normalizeId(id: string | number | undefined): string {
  if (id === undefined || id === null) return '';
  return String(id).trim();
}

/**
 * Checks if two IDs match after normalization
 * @param id1 First ID
 * @param id2 Second ID
 * @returns True if IDs match
 */
export function idsMatch(id1: string | number | undefined, id2: string | number | undefined): boolean {
  return normalizeId(id1) === normalizeId(id2);
}