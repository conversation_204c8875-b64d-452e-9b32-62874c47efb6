/**
 * Client Detection Utilities
 * 
 * Utility functions for testing and debugging client detection from referrer URLs
 */

import { extractClientFromReferrer, CLIENT_DOMAIN_MAP, isDomainRegistered } from '../config/clientDomainMapping';
import { clientNameToId } from '../config/clientLogoConfig';

export interface ClientDetectionResult {
  success: boolean;
  clientName: string | null;
  clientId: string | null;
  method: 'path' | 'referrer' | 'parameter' | 'localStorage' | 'default' | 'failed';
  details: string;
}

/**
 * Test client detection for a given embed URL
 * @param embedUrl Full embed URL with parameters
 * @returns Detection result with details
 */
export function testClientDetection(embedUrl: string): ClientDetectionResult {
  try {
    const url = new URL(embedUrl);
    
    // Test path-based detection first
    const pathMatch = url.pathname.match(/\/([^\/]+)/);
    if (pathMatch && pathMatch[1] !== '') {
      const clientName = pathMatch[1].toLowerCase();
      const clientId = clientNameToId[clientName] || null;
      return {
        success: !!clientId,
        clientName,
        clientId,
        method: 'path',
        details: `Client identified from URL path: /${clientName}`
      };
    }

    // Test referrer-based detection
    const referrer = url.searchParams.get('referrer');
    if (referrer) {
      const decodedReferrer = decodeURIComponent(referrer);
      const clientName = extractClientFromReferrer(decodedReferrer);
      if (clientName) {
        const clientId = clientNameToId[clientName] || null;
        return {
          success: !!clientId,
          clientName,
          clientId,
          method: 'referrer',
          details: `Client identified from referrer: ${decodedReferrer} -> ${clientName}`
        };
      } else {
        return {
          success: false,
          clientName: null,
          clientId: null,
          method: 'failed',
          details: `No client mapping found for referrer: ${decodedReferrer}`
        };
      }
    }

    // Test parameter-based detection
    const clientParam = url.searchParams.get('client');
    if (clientParam) {
      const clientId = clientNameToId[clientParam] || null;
      return {
        success: !!clientId,
        clientName: clientParam,
        clientId,
        method: 'parameter',
        details: `Client specified in URL parameter: ${clientParam}`
      };
    }

    return {
      success: false,
      clientName: null,
      clientId: null,
      method: 'failed',
      details: 'No client identification method succeeded'
    };

  } catch (error) {
    return {
      success: false,
      clientName: null,
      clientId: null,
      method: 'failed',
      details: `Error parsing embed URL: ${error}`
    };
  }
}

/**
 * Get all possible embed URLs for a client
 * @param clientName Internal client name
 * @returns Array of possible embed URLs
 */
export function getEmbedUrlsForClient(clientName: string): string[] {
  const baseUrl = 'https://embed.repd.us';
  const urls: string[] = [];

  // Standard path-based URL
  urls.push(`${baseUrl}/${clientName}`);

  // Find client domains for referrer-based URLs
  const clientDomains = Object.entries(CLIENT_DOMAIN_MAP)
    .filter(([_, name]) => name === clientName)
    .map(([domain, _]) => domain);

  // Generate referrer-based URLs
  clientDomains.forEach(domain => {
    const referrerUrl = `https://${domain}`;
    urls.push(`${baseUrl}/?referrer=${encodeURIComponent(referrerUrl)}`);
  });

  // Parameter-based URL
  urls.push(`${baseUrl}/?client=${clientName}`);

  return urls;
}

/**
 * Validate that a client has proper configuration
 * @param clientName Internal client name
 * @returns Validation result
 */
export function validateClientConfiguration(clientName: string): {
  valid: boolean;
  issues: string[];
  suggestions: string[];
} {
  const issues: string[] = [];
  const suggestions: string[] = [];

  // Check if client has ID mapping
  if (!clientNameToId[clientName]) {
    issues.push(`Client '${clientName}' not found in clientNameToId mapping`);
    suggestions.push(`Add '${clientName}': 'CLIENT_ID' to clientNameToId in clientLogoConfig.ts`);
  }

  // Check if client has domain mappings
  const clientDomains = Object.entries(CLIENT_DOMAIN_MAP)
    .filter(([_, name]) => name === clientName)
    .map(([domain, _]) => domain);

  if (clientDomains.length === 0) {
    issues.push(`No domain mappings found for client '${clientName}'`);
    suggestions.push(`Add client domains to CLIENT_DOMAIN_MAP in clientDomainMapping.ts`);
  }

  return {
    valid: issues.length === 0,
    issues,
    suggestions
  };
}

/**
 * Generate embed code with referrer fallback for a client
 * @param clientName Internal client name
 * @param referrerDomain Client's domain
 * @returns Embed code string
 */
export function generateEmbedCodeWithReferrer(clientName: string, referrerDomain?: string): string {
  const baseUrl = 'https://embed.repd.us';
  
  // Use path-based URL as primary, referrer as fallback
  const primaryUrl = `${baseUrl}/${clientName}`;
  const fallbackUrl = referrerDomain 
    ? `${baseUrl}/?referrer=${encodeURIComponent(`https://${referrerDomain}`)}`
    : `${baseUrl}/?client=${clientName}`;

  return `
<script>
(function() {
  const iframe = document.createElement('iframe');
  iframe.id = 'repd-embed';
  
  // Get the current page URL for referrer
  const referral = window.location.href;
  
  // Try primary URL first (path-based)
  let embedUrl = '${primaryUrl}';
  
  // Add referrer parameter
  embedUrl += (embedUrl.includes('?') ? '&' : '?') + 'referrer=' + encodeURIComponent(referral);
  
  iframe.src = embedUrl;
  iframe.style.cssText = \`
    position: fixed;
    bottom: 0rem;
    right: 0rem;
    border: none;
    z-index: 50;
    transition: all 0.3s ease;
    background: transparent;
    width: 10rem;
    height: 10rem;
    max-height: calc(100vh - 40px);
  \`;
  
  // Add error handling for fallback
  iframe.onerror = function() {
    console.log('Primary embed URL failed, trying fallback...');
    iframe.src = '${fallbackUrl}';
  };
  
  document.body.appendChild(iframe);
})();
</script>`.trim();
}

/**
 * Debug current client detection state
 * @returns Current detection state information
 */
export function debugClientDetection(): {
  currentUrl: string;
  detectedClient: ClientDetectionResult;
  localStorage: any;
  availableClients: string[];
  registeredDomains: string[];
} {
  const currentUrl = window.location.href;
  const detectedClient = testClientDetection(currentUrl);
  
  return {
    currentUrl,
    detectedClient,
    localStorage: {
      clientId: localStorage.getItem('clientId'),
      client: localStorage.getItem('client')
    },
    availableClients: Object.keys(clientNameToId),
    registeredDomains: Object.keys(CLIENT_DOMAIN_MAP)
  };
}
