interface Source {
  url: string;
  text?: string;
  score: number;
}

export const formatSourceUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    // Remove 'www.' from hostname
    const hostname = urlObj.hostname.replace(/^www\./, '');
    
    // Get pathname without query parameters and remove common prefixes
    let pathname = urlObj.pathname
      .replace(/^\/files\/assets\//, '')
      .replace(/^\/city\//, '')
      .replace(/^\/v\/\d+\//, '');
    
    // If pathname is too long, try to get just the filename
    if (pathname.length > 50) {
      pathname = pathname.split('/').pop() || pathname;
    }
    
    return `${hostname}${pathname}`;
  } catch {
    // If URL parsing fails, return original
    return url;
  }
};

export const extractSourcesFromAnswer = (answer: string, existingSources?: Source[]): Source[] => {
  if (existingSources && existingSources.length > 0) return existingSources;
  if (!answer) return [];
  
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = answer;
  
  const anchors = tempDiv.getElementsByTagName('a');
  if (anchors.length === 0) {
    // Fallback to regex if no anchor tags found
    const urlRegex = /(http(s|)?:\/\/[^\s<>"]+)/g;
    const matches = answer.match(urlRegex);
    
    if (!matches) return [];
    
    return [...new Set(matches)]
      .filter(url => !url.includes('repd-api-files'))
      .map(url => ({
        url,
        text: url,
        score: 1
      }));
  }
  
  const extractedLinks = Array.from(anchors)
    .filter(anchor => !anchor.href.includes('repd-api-files'))
    .map(anchor => ({
      url: anchor.href,
      text: anchor.textContent || anchor.href,
      score: 1
    }));
  
  return [...new Set(extractedLinks.map(JSON.stringify))].map(str => JSON.parse(str));
};
