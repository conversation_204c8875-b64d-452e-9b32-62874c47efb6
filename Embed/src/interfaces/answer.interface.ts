
import { Language } from '@/translations';

export interface VideoUrls {
  mp4: string;
  webm: string;
  ogv: string;
}

export interface TranscriptionItem {
  type: string;
  start_time?: string;
  end_time?: string;
  alternatives: { content: string }[];
}

export interface AudioSegment {
  start_time: string;
  end_time: string;
  transcript: string;
}

export interface Transcription {
  items: TranscriptionItem[];
  audio_segments?: AudioSegment[];
}

export interface TranscriptionTranslations {
  [language: string]: Transcription | AudioSegment[];
}

export interface User {
  id: string;
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
  clientId: string;
  firstName: string;
  lastName: string;
  imageUrl: string | null;
  email: string;
  zip: string | null;
  ipa: string;
  location: string;
  accessLevel: string;
  telepromptSpeed: number | null;
}

export interface Question {
  id: string;
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
  clientId: string;
  text: string;
  category: string;
  categoryIcon: string;
  votes: number;
  user: User;
  isApproved: boolean;
  isDenied: boolean;
  isAnswered: boolean;
  isShared: boolean | null;
  originalLanguage: Language;
  translations?: Partial<Record<Language, string>>;
}

export interface ApiAnswer {
  id: string;
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
  clientId: number;
  imageUrl: string;
  videoUrl: string;
  videoUrls: VideoUrls;
  videoDuration: number;
  votes: number;
  question: Question;
  isPinned: boolean;
  isDraft: boolean | null;
  isApproved: boolean | null;
  isDenied: boolean | null;
  isShared: boolean | null;
  likes: number;
  liked: boolean;
  comments: any[];
  createdAtDateString: string;
  transcription?: Transcription;
  transcriptionTranslation?: TranscriptionTranslations;
}

export interface Answer {
  isPinned: any;
  id: string;
  videoUrl: string;
  videoUrls: VideoUrls;
  stats: {
    votes: number;
  };
  question: {
    id?: string;
    text: string;
    translations?: Partial<Record<Language, string>>;
    originalLanguage?: Language;
    user?: {
      firstName: string;
      location: string;
    };
  };
  showTranscribedSubtitles?: boolean;
  transcription?: Transcription;
  transcriptionTranslation?: TranscriptionTranslations;
}

export interface ApiResponse<T> {
  message: string;
  page: number;
  limit: boolean;
  data: T[];
}

export interface ApiAnswerResponse extends ApiResponse<ApiAnswer> {}
