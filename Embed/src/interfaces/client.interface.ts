// eslint-disable-next-line @typescript-eslint/no-unused-vars
const supportedLanguages = ['en', 'es', 'ht', 'hi', 'vi', 'zh', 'fa', 'te', 'ar'];

type ClientTranslationType = Record<string, string | any>;
type ClientTranslationsType = Record<typeof supportedLanguages[number], ClientTranslationType>;

export interface ClientInterface {
  id: string;
  firstName?: string;
  lastName?: string;
  name: string;
  email: string;
  categories: string;
  clientType: string;

  subDomainAdmin: string | null;
  link: string;
  logoURL: string;
  websiteURL: string;
  donationURL: string;
  volunteerURL: string;
  backgroundImageURL: string | null;

  topBarColour: string;
  videoLinksColour: string;
  plusAskPillColour: string;
  newQuestionColour: string;
  openQuestionsAndAnswersColour: string;

  ngpVanUsername: string;
  ngpVanApiKey: string;

  subject: string | null;
  topMessage: string | null;
  campaignName: string | null;
  campaignAddress: string | null;

  isPublished: boolean;
  donationDisplaysPermanently: boolean;
  isLocked: boolean;
  isInternal: boolean; // Add this field to mark internal clients

  createdAt: string | Date;
  updatedAt: string | Date;
  enabled: boolean;

  donateText: string | null;
  donateCtaText: string | null;

  headerHomeLinkText: string | null;
  headerDonateLinkText: string | null;
  headerVolunteerLinkText: string | null;
  emailDonateCtaText: string | null;
  postQuestionText: string | null;
  postQuestionBtnText: string | null;
  textOptions: TextOptionsInterface;
  translations: ClientTranslationsType;

  aiQuestionsEnabled: boolean;
  aiAskQuestionAlwaysOn: boolean;
}

export interface TextOptionsInterface {
  postVideoSurveyQuestion?: string;
  postVideoSurveyLessLikely?: string;
  postVideoSurveyNeutral?: string;
  postVideoSurveyMoreLikely?: string;
  questionExpectancy?: string;

  aiPromptDesktop?: string;
  aiPromptMobile?: string;
  aiDefaultQuestion1?: string;
  aiDefaultQuestion2?: string;
  aiDefaultQuestion3?: string;
}
