import { Language } from '@/translations';

export interface NewQuestionData {
  userId?: string;
  clientId: string;
  text: string;
  originalLanguage: Language;
  category?: string;
  categoryIcon?: string;
  votes?: number;
  asked?: boolean;
  name?: string;
  isInternal?: boolean; // Add this field to mark internal questions
}

export interface Question {
  id: string;
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
  clientId: string;
  text: string;
  category: string;
  categoryIcon: string;
  votes: number;
  isApproved: boolean;
  isDenied: boolean;
  isAnswered: boolean;
  isShared: boolean;
  originalLanguage: Language;
  translations?: Partial<Record<Language, string>>;
  user: {
    firstName: string;
    lastName: string;
    location: string;
  };
}
