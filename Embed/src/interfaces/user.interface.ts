import { ClientInterface } from './client.interface';

export interface UserInterface {
  id: string;
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
  clientId: string | null;
  imageUrl: string | null;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  phone: string | null;
  zip: string | null;
  ipa: string | null;
  location: string | null;
  accessLevel: string;
  isMfaRequired: boolean;
  telepromptSpeed: string | null;
  client: {
    id: string | null;
  };
}

export interface SessionResponse {
  message: string;
  totalEntries: number;
  data: SessionDataInterface[];
}

export interface SessionDataInterface {
  id: string;
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
  token: string;
  tokenExpiresAt: string;
  user: UserInterface;
}

export interface UserIpaResponse {
  ip: string;
  hostname: string;
  city: string;
  region: string;
  country: string;
  loc: string;
  org: string;
  postal: string;
  timezone: string;
}
