import { useEffect } from 'react';
import { LanguageProvider } from './context/LanguageContext';
import { Ai<PERSON>rovider } from './context/AiContext';
import { AppContent } from './components/AppContent/AppContent';
import { AllEmbedsGrid } from './pages/AllEmbedsGrid';
import { IntrospectionPage } from './pages/IntrospectionPage';
import { CLIENT_ID } from './config/environment';
import { clientNameToId } from './config/clientLogoConfig';
import { extractClientFromReferrer } from './config/clientDomainMapping';

// Font size options with actual rem values - same as in Footer.tsx
const fontSizeOptions = [
  { value: 'small', label: 'Small', size: '0.875' },
  { value: 'medium', label: 'Medium', size: '1' },
  { value: 'large', label: 'Large', size: '1.25' }
];

function App() {
  // Helper function to determine client ID from URL path or referrer
  const getClientIdFromPath = (pathname: string) => {
    let clientNameFromUrl = null;

    // Check if it's an introspection route: /introspection/clientname
    const introspectionMatch = pathname.match(/^\/introspection\/(.+)$/);
    if (introspectionMatch) {
      clientNameFromUrl = introspectionMatch[1].toLowerCase();
    } else {
      // Check for regular client route: /clientname
      const pathMatch = pathname.match(/\/([^\/]+)/);
      clientNameFromUrl = pathMatch ? pathMatch[1].toLowerCase() : null;
    }

    // If no client found in path, try referrer URL
    if (!clientNameFromUrl) {
      const searchParams = new URLSearchParams(window.location.search);
      const referrer = searchParams.get('referrer');
      if (referrer) {
        clientNameFromUrl = extractClientFromReferrer(decodeURIComponent(referrer));
        if (clientNameFromUrl) {
          console.log(`App.tsx: Client identified from referrer: ${clientNameFromUrl}`);
        }
      }
    }

    // Determine client ID from URL or environment variable
    let clientId = CLIENT_ID;

    // If URL contains a known client name, use its ID
    if (clientNameFromUrl && clientNameToId[clientNameFromUrl]) {
      clientId = clientNameToId[clientNameFromUrl];
    }

    return clientId;
  };



  useEffect(() => {
    // Initialize font size from localStorage or default to medium
    const savedFontSize = localStorage.getItem('fontSizePreference') || 'medium';

    // Find the selected option to get the rem value
    const selectedOption = fontSizeOptions.find(option => option.value === savedFontSize);
    if (selectedOption) {
      // Set the font-size on the :root element
      document.documentElement.style.fontSize = `${selectedOption.size}rem`;
    } else {
      // Fallback to medium if something goes wrong
      document.documentElement.style.fontSize = '1rem';
    }

    // Get client ID using the helper function
    const clientId = getClientIdFromPath(window.location.pathname);

    // Add client-specific class to the root element
    if (clientId) {
      document.documentElement.classList.add(`client-${clientId}`);

      // Load client-specific CSS if needed
      try {
        import(`./styles/client-overrides/${clientId}.css`)
          .catch(() => console.log(`No custom styling for client ${clientId}`));
      } catch (e) {
        // Silently fail if no override exists
      }
    }
  }, []);

  // Check if we're on the /all route
  const isAllEmbedsRoute = window.location.pathname === '/all';

  // Check if we're on the /introspection/:client route
  const introspectionMatch = window.location.pathname.match(/^\/introspection\/(.+)$/);
  const isIntrospectionRoute = !!introspectionMatch;

  // Use the same client ID logic for introspection route
  const introspectionClientId = isIntrospectionRoute ? getClientIdFromPath(window.location.pathname) : null;

  return (
    <LanguageProvider>
      <AiProvider>
        {isAllEmbedsRoute ? (
          <AllEmbedsGrid />
        ) : isIntrospectionRoute ? (
          <IntrospectionPage clientId={introspectionClientId} />
        ) : (
          <AppContent />
        )}
      </AiProvider>
    </LanguageProvider>
  );
}

export default App;
