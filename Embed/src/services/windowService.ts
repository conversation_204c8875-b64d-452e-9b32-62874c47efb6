interface WindowDimensions {
  width: string;
  height: string;
}

export class WindowService {
  static updateParentDimensions(isMinimized: boolean): void {
    if (isMinimized) {
      window.parent.postMessage({
        type: 'resize',
        dimensions: { width: '200px', height: '200px' }
      }, '*');
      return;
    }

    // Calculate actual content height
    const content = document.querySelector('#AppContent');
    const contentHeight = content ? `${content.scrollHeight}px` : '600px';

    window.parent.postMessage({
      type: 'resize',
      dimensions: { 
        width: '480px', 
        height: contentHeight
      }
    }, '*');
  }
}
