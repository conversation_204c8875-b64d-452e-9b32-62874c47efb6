import { API_BASE_URL } from '../config/environment';
import { Answer, ApiAnswerResponse, Transcription, TranscriptionTranslations } from '../interfaces/answer.interface';

export async function getVideoAnswers(clientId: string, userId: string): Promise<Answer[]> {
  const token = localStorage.getItem('token');
  if (!token) return [];

  const queryParams = new URLSearchParams();
  queryParams.append('clientId', clientId);
  queryParams.append('userId', userId);

  const response = await fetch(`${API_BASE_URL}/answers?${queryParams.toString()}`, {
    headers: {
      'Accept': 'application/json',
      'Authorization': `${token}`
    }
  });

  if (!response.ok) return [];

  const apiResponse: ApiAnswerResponse = await response.json();
  
  // Filter for videos that are both enabled and published
  const publishedVideos = apiResponse.data.filter(item => 
    item.enabled && 
    !item.isDenied &&
    !item.isDraft &&
    !item.imageUrl.match(/empty.(jpg|jpeg)/)
    // item.isApproved === true
  );
  
  // Sort videos to show pinned items first
  const sortedVideos = publishedVideos.sort((a, b) => {
    // First sort by isPinned (pinned items first)
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;
    
    // Then sort by creation date (newest first)
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });
  
  return sortedVideos.map(item => ({
    id: item.id, // Ensure this is a string to match repd_answer_id
    thumbnail: item.imageUrl,
    videoUrl: item.videoUrl,
    videoUrls: item.videoUrls, // Add this line to include videoUrls
    enabled: item.enabled,
    title: item.question.text,
    stats: {
      votes: item.question.votes,
      answeredAt: item.createdAt
    },
    question: {
      text: item.question.text,
      translations: item.question.translations,
      originalLanguage: item.question.originalLanguage,
      user: {
        firstName: item.question.user.firstName,
        lastName: item.question.user.lastName,
        location: item.question.user.location
      }
    },
    showTranscribedSubtitles: true,
    transcription: item.transcription || { items: [] },
    transcriptionTranslation: item.transcriptionTranslation || {},
    votes: item.votes,
    liked: item.liked,
    likes: item.likes,
    isPinned: item.isPinned
  }));
}

export async function getVideoTranscription(videoId: string): Promise<{
  transcription: Transcription,
  transcriptionTranslation: TranscriptionTranslations
}> {
  const token = localStorage.getItem('token');
  if (!token) {
    return {
      transcription: { items: [] },
      transcriptionTranslation: {}
    };
  }

  try {
    const response = await fetch(`${API_BASE_URL}/videos/${videoId}/transcription`, {
      headers: {
        'Accept': 'application/json',
        'Authorization': `${token}`
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch transcription');
    }

    const data = await response.json();
    console.log('Raw transcription API response:', data);
    
    // Check the structure of the API response
    const transcription = data.transcription || { items: [] };
    
    // The translations might be nested differently in the API response
    // Try different possible structures
    let translations = {};
    if (data.translations) {
      translations = data.translations;
    } else if (data.transcriptionTranslation) {
      translations = data.transcriptionTranslation;
    } else if (data.transcriptionTranslations) {
      translations = data.transcriptionTranslations;
    }
    
    console.log('Processed transcription data:', {
      transcription,
      translationLanguages: Object.keys(translations)
    });
    
    return {
      transcription,
      transcriptionTranslation: translations
    };
  } catch (error) {
    console.error('Failed to fetch video transcription:', error);
    return {
      transcription: { items: [] },
      transcriptionTranslation: {}
    };
  }
}
