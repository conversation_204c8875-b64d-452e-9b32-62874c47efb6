import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { API_BASE_URL } from '../config/environment';

interface ApiRequestConfig {
  endpoint: string;
  path?: string;
  params?: Record<string, unknown>;
  headers?: Record<string, string>;
}

export class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = API_BASE_URL;
    this.api = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add response interceptor for error handling
    const errorInterceptor = (error: any) => {
      console.error('API request failed:', error);

      // Check for 401 Unauthorized error
      if (error.response && error.response.status === 401) {
        console.log('Authentication expired. Clearing session data...');
        // Clear all localStorage
        localStorage.clear();
        // Reload the page to restart authentication flow
        window.location.reload();
      }

      return Promise.reject(error);
    };

    this.api.interceptors.response.use((response) => response, errorInterceptor);
  }

  async get(config: ApiRequestConfig) {
    const requestConfig: AxiosRequestConfig = {
      params: config.params,
      headers: config.headers,
    };

    const url = `${config.endpoint}${config.path || ''}`;
    return this.api.get(url, requestConfig);
  }

  async post(config: ApiRequestConfig & { data?: unknown }) {
    const url = `${config.endpoint}${config.path || ''}`;
    return this.api.post(url, config.data, {
      params: config.params,
      headers: config.headers,
    });
  }
}
