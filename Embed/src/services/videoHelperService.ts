
import type { Language } from '../translations';

export interface TranscriptionItem {
  type: string;
  start_time?: string;
  end_time?: string;
  alternatives: { content: string }[];
}

export interface AudioSegment {
  start_time: string;
  end_time: string;
  transcript: string;
}

export interface Transcription {
  items: TranscriptionItem[];
  audio_segments?: AudioSegment[];
}

export interface TranscriptionTranslations {
  [language: string]: Transcription | AudioSegment[];
}





// Helper function to split text into lines for embed with improved logic
function splitTextIntoLines(text: string, maxLineLength: number = 45): string[] {
  const words = text.split(' ');
  const lines: string[] = [];
  let currentLine = '';

  console.log(`splitTextIntoLines: ${words.length} words, maxLength=${maxLineLength}`);

  for (const word of words) {
    const testLine = currentLine ? `${currentLine} ${word}` : word;

    if (testLine.length <= maxLineLength) {
      currentLine = testLine;
    } else {
      // If current line has content, save it and start new line
      if (currentLine) {
        lines.push(currentLine);
        console.log(`Added line ${lines.length}: "${currentLine}"`);
        currentLine = word;
      } else {
        // Single word is longer than max length, add it anyway to avoid infinite loop
        lines.push(word);
        console.log(`Added long word as line ${lines.length}: "${word}"`);
        currentLine = '';
      }
    }
  }

  if (currentLine) {
    lines.push(currentLine);
    console.log(`Added final line ${lines.length}: "${currentLine}"`);
  }

  console.log(`splitTextIntoLines result: ${lines.length} lines total`);
  return lines;
}

// Helper function to create smart text display for embed
function createEmbedDisplayText(text: string, isProgressive: boolean = true): string {
  console.log(`createEmbedDisplayText: isProgressive=${isProgressive}, input length=${text.length}`);
  console.log(`Input text: "${text.substring(0, 100)}..."`);

  // For embed, we want to show meaningful chunks of text
  // Use longer line length and allow up to 3 lines for better readability
  const lines = splitTextIntoLines(text, 45);
  console.log(`Split into ${lines.length} lines:`, lines.map(line => `"${line}"`));

  // For progressive text, always show what we have (don't truncate at sentence breaks)
  // For static text (like end-of-segment), we can be smarter about breaks
  if (isProgressive) {
    // Progressive mode: show up to 3 lines, prioritizing the most recent content
    if (lines.length > 3) {
      console.log(`Progressive mode: ${lines.length} lines available, need to fit in 3`);

      // For progressive text, we want to show the most recent content
      // Use the last 3 lines to show the latest words being spoken
      const displayLines = lines.slice(-3);
      console.log(`Progressive mode: showing last 3 lines (most recent content)`);
      console.log(`Selected lines:`, displayLines.map(line => `"${line}"`));

      const result = displayLines.join('\n');
      console.log(`Progressive result: "${result}"`);
      return result;
    } else {
      // 3 or fewer lines, show them all
      console.log(`Progressive mode: showing all ${lines.length} lines`);
      const result = lines.join('\n');
      console.log(`Progressive result: "${result}"`);
      return result;
    }
  } else {
    // Static mode: try to end at natural breaks for complete thoughts
    let displayLines = lines.slice(0, 3);

    // If we have more than 3 lines, try to end at a natural break
    if (lines.length > 3) {
      const combinedText = displayLines.join(' ');

      // Look for natural sentence endings in the first 3 lines
      const sentenceEndings = ['.', '!', '?'];
      let bestBreakPoint = -1;

      for (const ending of sentenceEndings) {
        const lastIndex = combinedText.lastIndexOf(ending);
        if (lastIndex > combinedText.length * 0.7) { // Only if it's reasonably far along
          bestBreakPoint = lastIndex;
          break;
        }
      }

      if (bestBreakPoint > 0) {
        const truncatedText = combinedText.substring(0, bestBreakPoint + 1);
        displayLines = splitTextIntoLines(truncatedText, 45).slice(0, 3);
      }
    }

    return displayLines.join('\n');
  }
}

export function getCurrentSubtitle(
  currentTime: number,
  transcription: Transcription | null,
  transcriptionTranslation: TranscriptionTranslations | null,
  currentLanguage: Language,
  showTranscribedSubtitles: boolean
): string {
  if (!showTranscribedSubtitles) {
    return '';
  }

  console.log('Subtitle Debug - Current time:', currentTime);
  console.log('Subtitle Debug - Translation available languages:',
    transcriptionTranslation ? Object.keys(transcriptionTranslation) : 'none');
  console.log('Subtitle Debug - Current language:', currentLanguage);

  // Determine which transcription data to use
  // For English (or when no translation exists), use original transcription
  // For other languages, use translation if available
  let activeTranscription: Transcription | null = null;
  let activeAudioSegments: AudioSegment[] | null = null;

  const hasTranslationForLanguage =
    currentLanguage !== 'en' &&
    transcriptionTranslation &&
    transcriptionTranslation[currentLanguage] &&
    (Array.isArray(transcriptionTranslation[currentLanguage]) ||
     ((transcriptionTranslation[currentLanguage] as Transcription).items?.length ?? 0) > 0 ||
     ((transcriptionTranslation[currentLanguage] as Transcription).audio_segments?.length ?? 0) > 0);

  if (hasTranslationForLanguage) {
    console.log(`Using translation for language: ${currentLanguage}`);
    const translation = transcriptionTranslation[currentLanguage];
    if (Array.isArray(translation)) {
      // Translation is an array of audio segments
      activeAudioSegments = translation;
    } else {
      // Translation is a Transcription object
      activeTranscription = translation as Transcription;
      activeAudioSegments = activeTranscription.audio_segments || null;
    }
  } else {
    console.log(`Using original transcription for language: ${currentLanguage}`);
    activeTranscription = transcription;
    activeAudioSegments = transcription?.audio_segments || null;
  }

  console.log('Active transcription data:', {
    hasAudioSegments: !!(activeAudioSegments && activeAudioSegments.length > 0),
    audioSegmentsCount: activeAudioSegments?.length || 0,
    hasItems: !!(activeTranscription?.items && activeTranscription.items.length > 0),
    itemsCount: activeTranscription?.items?.length || 0
  });

  // Use audio segments exclusively for subtitle display
  // This ensures consistency with admin panel edits to audio segments
  if (activeAudioSegments && activeAudioSegments.length > 0) {
    return getSubtitleFromAudioSegments(currentTime, activeAudioSegments);
  }

  console.log('No audio segments available');
  return '';
}

function getSubtitleFromAudioSegments(currentTime: number, audioSegments: AudioSegment[]): string {
  console.log(`DEBUG: Using audio segments (${audioSegments.length} segments)`);
  console.log('First segment:', audioSegments[0]);

  // Find which segment we're currently in
  let currentSegment: AudioSegment | null = null;
  let segmentProgress = 0;

  for (let i = 0; i < audioSegments.length; i++) {
    const segment = audioSegments[i];
    const startTime = parseFloat(segment.start_time);
    const endTime = parseFloat(segment.end_time);

    console.log(`Checking segment ${i}: ${startTime}s - ${endTime}s, current: ${currentTime}s`);

    if (currentTime >= startTime && currentTime <= endTime) {
      currentSegment = segment;
      // Calculate progress within this segment (0 to 1)
      segmentProgress = (currentTime - startTime) / (endTime - startTime);
      console.log(`Found current segment ${i}, progress: ${segmentProgress}`);
      break;
    }
  }

  if (currentSegment) {
    // We're in a segment - show progressive text based on timing within the segment
    const transcript = currentSegment.transcript;
    console.log('Current segment transcript:', transcript.substring(0, 100) + '...');
    console.log(`Segment progress: ${(segmentProgress * 100).toFixed(1)}%`);

    // Use audio segment transcript with timing-based progressive display
    // This ensures consistency with admin panel edits to audio segments
    return getProgressiveTextFromTranscript(transcript, segmentProgress);
  } else {
    // Not in any segment - check if we should show context
    const firstSegmentStart = parseFloat(audioSegments[0].start_time);
    const lastSegmentEnd = parseFloat(audioSegments[audioSegments.length - 1].end_time);

    console.log(`Not in any segment. Current: ${currentTime}s, First: ${firstSegmentStart}s, Last: ${lastSegmentEnd}s`);

    if (currentTime < firstSegmentStart) {
      // Before speech starts - show empty
      console.log('Before speech starts');
      return '';
    } else if (currentTime > lastSegmentEnd) {
      // After speech ends - show last segment's final text
      console.log('After speech ends');
      const lastSegment = audioSegments[audioSegments.length - 1];
      const transcript = lastSegment.transcript;
      return createEmbedDisplayText(transcript, false); // Static mode for complete text
    } else {
      // In a gap between segments - show empty
      console.log('In gap between segments');
      return '';
    }
  }
}



function getProgressiveTextFromTranscript(transcript: string, progress: number): string {
  console.log(`getProgressiveTextFromTranscript: progress=${(progress * 100).toFixed(1)}%, transcript length=${transcript.length}`);

  // Show progressive words based on segment progress
  const words = transcript.split(' ');

  // Use a more sophisticated progression that starts showing words earlier
  // and provides smoother progression throughout the segment
  let wordsToShow: number;

  if (progress <= 0.1) {
    // Show first few words immediately when segment starts
    wordsToShow = Math.max(3, Math.floor(words.length * 0.15));
  } else if (progress >= 0.9) {
    // Show all words when near the end of segment
    wordsToShow = words.length;
  } else {
    // Progressive display based on timing
    // Use a curve that shows more words earlier for better readability
    const adjustedProgress = 0.15 + (progress - 0.1) * 0.85 / 0.8;
    wordsToShow = Math.floor(words.length * adjustedProgress);
  }

  // Ensure we don't exceed the total word count
  wordsToShow = Math.min(wordsToShow, words.length);

  const progressiveText = words.slice(0, wordsToShow).join(' ');

  console.log(`Showing ${wordsToShow} out of ${words.length} words (${(wordsToShow/words.length*100).toFixed(1)}%)`);
  console.log(`Progressive text: "${progressiveText.substring(0, 100)}..."`);

  // Format for embed display using improved text handling (progressive mode)
  const displayText = createEmbedDisplayText(progressiveText, true);

  console.log('Generated progressive subtitle from transcript:', displayText);
  return displayText;
}



export function setupSubtitleTracking(
  videoElement: HTMLVideoElement,
  transcription: Transcription | null,
  transcriptionTranslation: TranscriptionTranslations | null,
  currentLanguage: Language,
  showTranscribedSubtitles: boolean,
  onSubtitleUpdate: (subtitle: string) => void
): () => void {
  const updateSubtitle = () => {
    const subtitle = getCurrentSubtitle(
      videoElement.currentTime,
      transcription,
      transcriptionTranslation,
      currentLanguage,
      showTranscribedSubtitles
    );
    onSubtitleUpdate(subtitle);
  };

  videoElement.addEventListener('timeupdate', updateSubtitle);
  // Initial update
  updateSubtitle();

  // Return cleanup function
  return () => {
    videoElement.removeEventListener('timeupdate', updateSubtitle);
  };
}

// Add this function to generate mock transcription data for testing
export function generateMockTranscription(videoDuration: number = 60): Transcription {
  const audio_segments: AudioSegment[] = [];
  // Create audio segments every 5 seconds
  for (let i = 0; i < videoDuration; i += 5) {
    audio_segments.push({
      start_time: i.toString(),
      end_time: (i + 4.9).toString(),
      transcript: `This is a test subtitle at ${i} seconds. This demonstrates how audio segments work for subtitle display in the embed.`
    });
  }

  // Return transcription with audio_segments (items array kept empty since we use audio_segments exclusively)
  return {
    items: [], // Empty items array since we use audio_segments exclusively
    audio_segments
  };
}
