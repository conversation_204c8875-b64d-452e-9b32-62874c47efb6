import { ApiService } from './api.service';
import { ClientInterface } from '../interfaces/client.interface';

export class ClientService {
  private apiService: ApiService;

  constructor() {
    this.apiService = new ApiService();
  }

  async getClientByName(clientName: string): Promise<ClientInterface> {
    try {
      const response = await this.apiService.get({
        endpoint: 'clients',
        path: `/${clientName}`
      });

      if (!response.data?.data?.[0]) {
        throw new Error('Client data not found');
      }

      return response.data.data[0];
    } catch (error) {
      console.error('Failed to fetch client:', error);
      throw error;
    }
  }
}
