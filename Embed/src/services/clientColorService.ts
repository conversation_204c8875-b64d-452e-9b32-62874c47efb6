/**
 * Client Color Service
 * Handles dynamic application of client-specific colors throughout the application
 * Replaces the need for static CSS override files by dynamically setting CSS variables
 */

import type { ClientInterface } from '../interfaces/client.interface';
import {
  hexToRgb,
  createGradientColors,
  createSkeletonColors,
  createButtonGradientColors,
  adjustColorLightness,
  createColorPalette
} from '../utils/colorUtils';

// Clients that should use solid background color instead of gradient
export const noGradientClients = ['273', '281', '300', '301', '302'];

export const clientButtonTexts = {
  '283': 'Ask Emporia',           // Emporia
  '234': 'Ask City Hall',         // Tulare
  '299': 'Staff Assistant',       // Myrtle Beach (Internal)
  '287': 'Ask Roeland Park',      // Roeland Park
  '281': 'Ask Morrisville',       // Town of Morrisville
  '214': 'Ask Broadview',         // Broadview
  '275': 'Ask RBPD',              // Redondo Beach PD
  '296': 'Ask City Hall',         // Richland
  '269': 'Ask The City',          // Traverse City
  '300': 'Ask The Village',       // Pleasantville
  '301': 'Ask The City',          // Tallahassee
  '302': 'Ask The City',          // Toledo
  '297': 'Ask Andover',           // Andover
  '303': 'Ask City Hall',         // Hermiston
  '304': 'Ask The County',        // Burke County
  '306': 'Ask Shockey Consulting',           // Shockey
  '266': 'Ask PRST',              // Myrtle Beach PRST
  '279': ' ',                     // Maple Valley
  '260': 'Ask The City',          // Denton
};

export const clientColors = {
  '306': { // Shockey
    main: "#0a2d54",
    video: "#061e38",
    button: "#0b315b",
    progress: "#ffffff"
  },
  '304': { // Burke County
    main: "#252a51",
    video: "#191d3d",
    button: "#272c54",
    progress: "#ffffff"
  },
  '303': { // Hermiston
    main: "#00954a",
    video: "#00783c",
    button: "#f3533b",
    progress: "#f3533b"
  },
  '297': { // Andover
    main: "#2f6db0",
    video: "#1e4773",
    button: "#306cf7",
    progress: "#ffffff"
  },
  '242': {
    main: "#004d62",
    video: "#022f3b",
    button: "#12347e",
    progress: "#12347e"
  },
  'Bowling Green': {
    main: "#255740",
    video: "#19392b",
    button: "#255740",
    progress: "#ffffff"
  },
  '214': { // Broadview
    main: "#064c82",
    video: "#07355a",
    button: "#064c82",
    progress: "#ffffff"
  },
  '283': { // Emporia
    main: "#06367e",
    video: "#04275c",
    button: "#0d6cfa",
    progress: "#d5ad7f"
  },
  '234': { // Tulare
    main: "#335a8e",
    video: "#254773",
    button: "#4EABE9",
    progress: "#00aeef"
  },
  '302': {
    main: "#006aac",
    video: "#00588f",
    button: "#0d6cfa",
    progress: "#ffffff"
  },
  '301': { // Tallahassee
    main: "#0048a2",
    video: "#003f89",
    button: "#0d6cfa",
    progress: "#ffffff"
  },
  '300': { // Pleasantville
    main: "#004123",
    video: "#00311B",
    button: "#00703E",
    progress: "#00703E"
  },
  '295': { // Page
    main: "#c94726",
    video: "#ab3416",
    button: "#85d0d3",
    progress: "#85d0d3"
  },
  '294': { // York
    main: "#053184",
    video: "#00276e",
    button: "#043896",
    progress: "#258342"
  },
  '293': { // Mount Jackson
    main: "#3867a9",
    video: "#254b80",
    button: "#0d6cfa",
    progress: "#0D9447"
  },
  '292': { // Loudoun County
    main: "#33586b",
    video: "#234252",
    button: "#d22333",
    progress: "#a14053"
  },
  '288': { // Park City
    main: "#1d726f",
    video: "#145957",
    button: "#0d6cfa",
    progress: "#8BBA4C"
  },
  '287': { // Roeland Park
    main: "#2c733c",
    video: "#236332",
    button: "#6c6e6f",
    progress: "#9ac73c"
  },
  '299': { // Myrtle Beach
    main: "#144c71",
    video: "#0e3751",
    button: "#f8d001",
    progress: "#f8d001"
  },
  '282': { // Kirkland
    main: "#3e6185",
    video: "#2e5579",
    button: "#4e7d6d",
    progress: "#4e7d6d"
  },
  '281': { // Town of Morrisville
    main: "#0076bb",
    video: "#254b80",
    button: "#209444",
    progress: "#ffb52b"
  },
  '279': { // City Of Maple Valley
    main: "#256296",
    video: "#154574",
    button: "#f6b434",
    progress: "#f6b434"
  },
  '278': { // City of Lorain
    main: "#3867a9",
    video: "#254b80",
    button: "#a24456",
    progress: "#a24456"
  },
  '276': { // South Bay City
    main: "#3867a9",
    video: "#254b80",
    button: "#0d6cfa",
    progress: "#1b8d5a"
  },
  '275': { // Redondo PD
    main: "#09384f",
    video: "#022340",
    button: "#3b8ea3",
    progress: "#3b8ea3"
  },
  '273': { // Cullman
    main: "#0c7ca5",
    video: "#00668f",
    button: "#e0c387",
    progress: "#e0c387"
  },
  '269': { // City of Traverse City
    main: "#072944",
    video: "#041e33",
    button: "#4d80b9",
    progress: "#4a81b8"
  },
  '263': { // City of Gadsden
    main: "#424081",
    video: "#06345c",
    button: "#e9202c",
    progress: "#e9202c"
  },
  '262': { // City of Leeds
    main: "#187c3d",
    video: "#10632f",
    button: "#6c6c6c",
    progress: "#97c93a"
  },
  '261': { // Suisun City
    main: "#0367ab",
    video: "#045489",
    button: "#f3b534",
    progress: "#f3b534"
  },
  '260': { // Denton
    main: "#d84497",
    video: "#c22f81",
    button: "#238182",
    progress: "#238182"
  },
  '296': { // Richland
    main: "#a9192d",
    video: "#911022",
    button: "#004d62",
    progress: "#ffffff"
  },
}

export class ClientColorService {
  private static instance: ClientColorService;
  private currentClient: ClientInterface | null = null;

  private constructor() {}

  public static getInstance(): ClientColorService {
    if (!ClientColorService.instance) {
      ClientColorService.instance = new ClientColorService();
    }
    return ClientColorService.instance;
  }

  /**
   * Get client-specific colors from the clientColors object
   */
  private getClientSpecificColors(clientId: string): any {
    return clientColors[clientId as keyof typeof clientColors] || null;
  }

  /**
   * Apply all client colors dynamically to the application
   * This replaces the need for static CSS override files
   */
  public applyClientColors(client: ClientInterface): void {
    this.currentClient = client;

    // Check if client has predefined colors in clientColors object
    const clientSpecificColors = this.getClientSpecificColors(client.id);

    // Set basic client color CSS variables
    this.setBasicColorVariables(client, clientSpecificColors);

    // Set dynamic gradient variables
    this.setGradientVariables(client, clientSpecificColors);

    // Set component-specific color variables
    this.setComponentColorVariables(client, clientSpecificColors);

    // Set progress bar colors
    this.setProgressBarColors(client, clientSpecificColors);

    // Set button colors
    this.setButtonColors(client, clientSpecificColors);

    // Apply client-specific overrides for special cases
    this.applyClientSpecificOverrides(client);

    console.log('Applied dynamic colors for client:', client.id, client.name);
  }

  /**
   * Set basic color CSS variables from client data
   */
  private setBasicColorVariables(client: ClientInterface, clientSpecificColors: any): void {
    const root = document.documentElement;

    // Use client-specific colors if available, otherwise fall back to client data
    const primaryColor = clientSpecificColors?.button || client.topBarColour || '#2760CF';

    // Primary client colors
    root.style.setProperty('--primary-color', primaryColor);
    root.style.setProperty('--accent-color', client.videoLinksColour || '#E94E77');
    root.style.setProperty('--new-question-color', client.newQuestionColour || '#29B36E');
    root.style.setProperty('--plus-ask-pill-color', client.plusAskPillColour || '#29B36E');
    root.style.setProperty('--open-qa-color', client.openQuestionsAndAnswersColour || '#0E509B');

    // Create a color palette based on the primary color
    const palette = createColorPalette(primaryColor);
    root.style.setProperty('--primary-light', palette.light);
    root.style.setProperty('--primary-dark', palette.dark);
    root.style.setProperty('--primary-muted', palette.muted);
    root.style.setProperty('--primary-accent', palette.accent);
  }

  /**
   * Set gradient-related CSS variables
   */
  private setGradientVariables(client: ClientInterface, clientSpecificColors: any): void {
    const root = document.documentElement;

    // Use client-specific main color if available, otherwise fall back to client data
    const baseColor = clientSpecificColors?.main || client.topBarColour || '#30466F';
    root.style.setProperty('--background-color', baseColor);
    const { lightColor, darkColor } = createGradientColors(baseColor);
    
    // Check if client should use solid background instead of gradient
    if (noGradientClients.includes(client.id)) {
      root.style.setProperty('--background-gradient', `${baseColor}`);
    } else {
      root.style.setProperty('--background-gradient', `linear-gradient(105deg, ${lightColor} 0.25%, ${darkColor} 100.25%)`);
    }
    
    root.style.setProperty('--background-light', lightColor);
    root.style.setProperty('--background-dark', darkColor);

    // Popular answers gradient - use video color if available, otherwise use background dark
      const videoGradientColor = clientSpecificColors?.video
      ? clientSpecificColors.video
      : darkColor;
    root.style.setProperty('--popular-answers-gradient', `linear-gradient(180deg, rgba(${hexToRgb(videoGradientColor)}, 0.75) 0%, ${videoGradientColor} 100%)`);

    // Scroll fade gradients (use the computed light color)
    root.style.setProperty('--scroll-fade-gradient', `linear-gradient(to bottom, ${lightColor} 0%, rgba(${hexToRgb(lightColor)}, 0) 100%)`);
    root.style.setProperty('--scroll-fade-gradient-bottom', `linear-gradient(to top, ${lightColor} 0%, rgba(${hexToRgb(lightColor)}, 0) 100%)`);

    // Skeleton shimmer gradient (reuse the same base color)
    const { mid: skeletonMid, dark: skeletonDark } = createSkeletonColors(baseColor);
    root.style.setProperty('--skeleton-gradient', `linear-gradient(to right, ${skeletonDark} 8%, ${skeletonMid} 18%, ${skeletonDark} 33%)`);
  }

  /**
   * Set component-specific color variables
   */
  private setComponentColorVariables(client: ClientInterface, clientSpecificColors: any): void {
    const root = document.documentElement;
    const primaryColor = clientSpecificColors?.button || client.topBarColour || '#2760CF';
    const accentColor = client.videoLinksColour || '#E94E77';

    // Error and success colors (derived from accent color)
    root.style.setProperty('--error-color', adjustColorLightness(accentColor, -10));
    root.style.setProperty('--success-color', client.newQuestionColour || '#29B36E');
    root.style.setProperty('--warning-color', adjustColorLightness(accentColor, 20));

    // Interactive element colors
    root.style.setProperty('--hover-color', adjustColorLightness(primaryColor, 10));
    root.style.setProperty('--active-color', adjustColorLightness(primaryColor, -10));
    root.style.setProperty('--focus-color', adjustColorLightness(primaryColor, 15));

    // Text colors based on primary color
    root.style.setProperty('--text-on-primary', '#FFFFFF');
    root.style.setProperty('--text-muted', adjustColorLightness(primaryColor, 40));
  }

  /**
   * Set progress bar colors dynamically
   */
  private setProgressBarColors(client: ClientInterface, clientSpecificColors: any): void {
    const root = document.documentElement;
    const progressColor = clientSpecificColors?.progress || client.videoLinksColour || client.newQuestionColour || '#E94E77';

    root.style.setProperty('--progress-bar-color', progressColor);
    root.style.setProperty('--progress-bar-background', adjustColorLightness(progressColor, -30));
  }

  /**
   * Set button colors dynamically
   */
  private setButtonColors(client: ClientInterface, clientSpecificColors: any): void {
    const root = document.documentElement;
    const buttonColor = clientSpecificColors?.button || client.topBarColour || '#2760CF';

    // Button gradient colors
    const { lightGradientColor, darkGradientColor } = createButtonGradientColors(buttonColor);
    root.style.setProperty('--button-gradient', `linear-gradient(135deg, ${lightGradientColor} 0%, ${darkGradientColor} 100%)`);
    root.style.setProperty('--button-gradient-hover', `linear-gradient(135deg, ${adjustColorLightness(lightGradientColor, 10)} 0%, ${adjustColorLightness(darkGradientColor, 10)} 100%)`);

    // Submit button colors
    root.style.setProperty('--submit-button-color', buttonColor);
    root.style.setProperty('--submit-button-hover', adjustColorLightness(buttonColor, 10));
    root.style.setProperty('--submit-button-active', adjustColorLightness(buttonColor, -10));
  }

  /**
   * Apply client-specific overrides for special cases
   * This handles clients that need specific color combinations
   */
  private applyClientSpecificOverrides(client: ClientInterface): void {
    const root = document.documentElement;

    // Add client-specific class to body for CSS targeting
    document.body.className = document.body.className.replace(/client-\d+/g, '');
    document.body.classList.add(`client-${client.id}`);

    // Special handling for specific clients
    switch (client.id) {
      case '283': // Emporia
        // Emporia uses blue instead of the default pink and orange progress bar
        root.style.setProperty('--accent-color', '#0E4EA0');
        root.style.setProperty('--progress-bar-color', '#fca127AA');
        root.style.setProperty('--error-color', '#0E4EA0');
        break;

      case '295': // Page
        // Page has specific teal color requirements
        root.style.setProperty('--accent-color', '#85d0d3');
        root.style.setProperty('--progress-bar-color', '#85d0d3');
        root.style.setProperty('--new-question-color', '#85d0d3');
        break;

      case '221': // Salisbury
        root.style.setProperty('--accent-color', '#fcb12e');
        root.style.setProperty('--progress-bar-color', '#fcb12e');
        break;

      case '280': // City of Ourlington
        root.style.setProperty('--accent-color', '#5881e8');
        root.style.setProperty('--progress-bar-color', '#5881e8');
        break;

      case '273': // Cullman
        root.style.setProperty('--accent-color', '#e0c386');
        root.style.setProperty('--progress-bar-color', '#e0c386');
        break;

      case '234': // Tulare
        root.style.setProperty('--accent-color', '#1b75bc');
        root.style.setProperty('--progress-bar-color', '#1b75bc');
        break;

      // Add more client-specific overrides as needed
      default:
        // Use default dynamic colors based on client data
        break;
    }
  }

  /**
   * Get current client
   */
  public getCurrentClient(): ClientInterface | null {
    return this.currentClient;
  }

  /**
   * Reset colors to default
   */
  public resetToDefaults(): void {
    const root = document.documentElement;

    // Reset to default values from global.css
    root.style.setProperty('--primary-color', '#2760CF');
    root.style.setProperty('--accent-color', '#E94E77');
    root.style.setProperty('--background-gradient', 'linear-gradient(105deg, #30466F 0.25%, #1E293D 100.25%)');
    root.style.setProperty('--new-question-color', '#29B36E');
    root.style.setProperty('--plus-ask-pill-color', '#29B36E');
    root.style.setProperty('--open-qa-color', '#0E509B');

    // Remove client-specific classes
    document.body.className = document.body.className.replace(/client-\d+/g, '');

    this.currentClient = null;
  }

  /**
   * Update specific color property
   */
  public updateColorProperty(property: string, value: string): void {
    document.documentElement.style.setProperty(property, value);
  }

  /**
   * Get computed color value
   */
  public getColorProperty(property: string): string {
    return getComputedStyle(document.documentElement).getPropertyValue(property).trim();
  }

  /**
   * Get client-specific button text
   */
  public static getClientButtonText(clientId: string): string | null {
    return clientButtonTexts[clientId as keyof typeof clientButtonTexts] || null;
  }
}
