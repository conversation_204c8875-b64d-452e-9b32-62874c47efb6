import { ApiService } from './api.service';

interface StatEventProperties {
  event_action: string;
  event_category: string;
  event_label?: string;
  answer_id?: string | number;
  question_id?: string | number;
  questionId?: string;
  clientId?: string;
  screenName?: string;
  ipa?: string;
  questionText?: string;
  category?: string;
  searchTerm?: string;
  videoFeedbackAmount?: string;
  exportJobDate?: string;
  exportJobId?: string;
  savedListId?: string;
  aiSearchText?: string;
  aiResponse?: any;
  accessLevel?: string;
  original?: any;
  referrer?: string;
  mobile?: boolean;
  browser?: string;
  userId?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  zip?: string;
}

export class TrackerService extends ApiService {
  private id: string;
  private cachedIPA: string | null = null; // Cache for the IP address

  constructor() {
    super();
    this.id = 'TrackerService';
  }

  async sendStats(data: Partial<StatEventProperties>) {
    // Skip tracking on admin pages
    const currentPath = window.location.pathname;

    // Check if we're in an iframe and get the parent URL if possible
    let parentPath = currentPath;
    try {
      if (window.parent !== window && window.parent.location) {
        parentPath = window.parent.location.pathname;
      }
    } catch (e) {
      // Cross-origin iframe, can't access parent location
      // In this case, we'll assume we're in an admin page iframe if the current path looks like a client path
      if (currentPath.match(/^(introspection|all)$/) !== null) {
        return Promise.resolve(null);
      }
    }

    if (currentPath === '/all' || currentPath.startsWith('/introspection/') ||
        parentPath === '/all' || parentPath.startsWith('/introspection/')) {
      return Promise.resolve(null);
    }

    const token = localStorage.getItem('token');
    if (!token) {
      console.error('No authentication token found');
      return Promise.resolve(null);
    }

    // Retrieve user data from localStorage if not provided
    const localStorageUser = localStorage.getItem('user');
    const user = localStorageUser ? JSON.parse(localStorageUser) : {};

    const endpoint = '/stats';
    const payload = {
      eventAction: data.event_action,
      eventCategory: data.event_category,
      eventLabel: '(Marvin Embed) ' + data.event_label ? data.event_label : `aiSearchText=${data.aiSearchText || ''}, , clientId=${localStorage.getItem('clientId') || ''}, userId=${user.id || ''}`,
      origin: window.location.origin,
      host: window.location.host,
      ipa: await this.getIPA(),
      // Map other fields with fallback to localStorage.user
      answerId: data.answer_id,
      questionId: data.question_id,
      clientId: localStorage.getItem('clientId'),
      referrer: data.referrer || '',
      mobile: data.mobile || /Mobile|Android|iOS/.test(navigator.userAgent),
      browser: data.browser || navigator.userAgent,
      userId: data.userId || user.id,
      firstName: data.firstName || user.firstName,
      lastName: data.lastName || user.lastName,
      email: data.email || user.email,
      phone: data.phone || user.phone,
      zip: data.zip || user.zip,
      // Add missing fields from WebApp payload
      accessLevel: data.accessLevel || user.accessLevel,
      aiSearchText: data.aiSearchText,
      aiResponse: data.aiResponse,
      original: data.original || data,
      exportJobId: data.exportJobId,
      savedListId: data.savedListId
    };

    return this.post({ 
      endpoint, 
      data: payload,
      headers: {
        'Authorization': `${token}`
      }
    }).catch(error => {
      console.error('Failed to send stats:', error);
      return null;
    });
  }

  private async getIPA(): Promise<string> {
    if (this.cachedIPA) {
      return this.cachedIPA; // Return cached IP if available
    }

    try {
      const response = await fetch('https://ipinfo.io/json');
      const data = await response.json();
      this.cachedIPA = data.ip; // Cache the IP address
      return data.ip;
    } catch (error) {
      console.error('Failed to get IP address:', error);
      return '0.0.0.0';
    }
  }
}
