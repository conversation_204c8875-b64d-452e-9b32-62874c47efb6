import type { Language, TranslationKey } from '../translations';
import { translations } from '../translations';

interface TranslatableContent {
  text: string;
  translations?: Partial<Record<Language, string>>;
  originalLanguage?: Language;
}

export class TranslationService {
  private static clientName: string = 'the City';

  static setClientName(name: string) {
    this.clientName = name || 'the City';
  }

  static getClientName(): string {
    return this.clientName;
  }

  static getTranslatedContent(
    content: TranslatableContent | undefined,
    currentLanguage: Language
  ): string {
    if (!content || typeof content.text !== 'string') {
      return '';  // or some default value
    }
    
    const { text, translations = {}, originalLanguage = 'en' } = content;
    
    if (!translations || currentLanguage === originalLanguage) {
      return this.replaceClientName(text);
    }
    
    return this.replaceClientName(translations[currentLanguage] || text);
  }

  static translate(key: TranslationKey, language: Language): string {
    const translation = translations[language][key];
    return this.replaceClientName(translation);
  }

  private static replaceClientName(text: string): string {
    return text.replace(/\[CLIENT_NAME\]/g, this.clientName);
  }
}
