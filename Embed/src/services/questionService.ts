import { ApiService } from './api.service';
import { NewQuestionData, Question } from '../interfaces/question.interface';

export interface QuestionError {
  code: 'ERROR_QUESTION_DUPLICATED' | 'ERROR_INAPPROPRIATE_LANGUAGE' | 'ERROR_SESSION_DISABLED';
  message: string;
}

export class QuestionService {
  private apiService: ApiService;

  constructor() {
    this.apiService = new ApiService();
  }

  private getErrorMessage(code: QuestionError['code']): string {
    switch (code) {
      case 'ERROR_QUESTION_DUPLICATED':
        return 'Please make sure your question is original';
      case 'ERROR_INAPPROPRIATE_LANGUAGE':
        return 'We\'ve detected inappropriate language in your question. Please rephrase and resubmit';
      case 'ERROR_SESSION_DISABLED':
        return 'We are not able to associate this email with your activity';
      default:
        return 'An error occurred while submitting your question';
    }
  }

  async createQuestion(questionData: NewQuestionData): Promise<any> {
    try {
      const response = await this.apiService.post({
        endpoint: 'questions',
        data: questionData
      });

      if (response.data.code) {
        throw new Error(this.getErrorMessage(response.data.code));
      }

      return response.data;
    } catch (error) {
      console.error('Failed to create question:', error);
      throw error;
    }
  }

  async getQuestions(clientId: string): Promise<Question[]> {
    try {
      const response = await this.apiService.get({
        endpoint: 'questions',
        params: { clientId }
      });

      return response.data.data || [];
    } catch (error) {
      console.error('Failed to fetch questions:', error);
      throw error;
    }
  }

  async getQuestion(questionId: string): Promise<Question> {
    try {
      const response = await this.apiService.get({
        endpoint: 'questions',
        path: `/${questionId}`
      });

      if (!response.data) {
        throw new Error('Question not found');
      }

      return response.data;
    } catch (error) {
      console.error('Failed to fetch question:', error);
      throw error;
    }
  }
}
