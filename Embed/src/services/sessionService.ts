
import { ApiService } from './api.service';
import { ClientService } from './clientService';
import { UserService } from './userService';
import { SessionDataInterface, SessionResponse, UserInterface } from '../interfaces/user.interface';
import { ClientInterface } from '../interfaces/client.interface';
import axios from 'axios';
import { API_BASE_URL } from '../config/environment';
import { TrackerService } from './tracker.service';
import { extractClientFromReferrer } from '../config/clientDomainMapping';

export interface SessionState {
  client: ClientInterface;
  user: UserInterface;
  token: string;          // Add token
  tokenExpiresAt: string; // Add expiration
  sessionId: string;      // Add session ID
}

export class SessionService {
  private apiService: ApiService;
  private clientService: ClientService;
  private userService: UserService;
  private axiosInstance;

  constructor() {
    this.apiService = new ApiService();
    this.clientService = new ClientService();
    this.userService = new UserService();
    this.axiosInstance = axios.create({
      baseURL: API_BASE_URL || '',
      headers: {
        'Content-Type': 'application/json',
        'accepts': 'application/json'
      }
    });
  }

  async getCurrentUser(): Promise<SessionDataInterface> {
    try {
      const localToken = localStorage.getItem('token');
      const headers: Record<string, string> = {
        'accepts': 'application/json',
        'origin': window.location.origin
      };

      if (localToken) {
        headers['Authorization'] = `${localToken}`;
      }

      const response = await this.axiosInstance.get<SessionResponse>('/sessions/current', { headers });
      return response.data.data[0];
    } catch (error) {
      console.error('Failed to get current user:', error);
      throw error;
    }
  }

  async initialize(): Promise<SessionState> {
    try {
      const clientName = this.getClientNameFromUrl() || process.env.REACT_APP_DEFAULT_CLIENT;
      if (!clientName) {
        throw new Error('No client name provided');
      }

      const [clientData, sessionData] = await Promise.all([
        this.clientService.getClientByName(clientName),
        this.getCurrentUser().catch(error => {
          // Handle 401 error during getCurrentUser
          if (error.response && error.response.status === 401) {
            console.log('Authentication expired. Clearing session data...');
            localStorage.clear();
            window.location.reload();
            // This will prevent further execution
            throw new Error('Session expired');
          }
          throw error;
        })
      ]);

      // Store necessary values in localStorage
      localStorage.setItem('clientId', clientData.id);
      localStorage.setItem('client', JSON.stringify(clientData));
      localStorage.setItem('userId', sessionData.user.id);
      localStorage.setItem('user', JSON.stringify(sessionData.user));
      localStorage.setItem('token', sessionData.token); // Save the session token

      // Update user at /me endpoint as soon as we have clientId
      try {
        await this.userService.updateCurrentUserSession({
          clientId: clientData.id
        });
      } catch (error) {
        console.error('Failed to update user at /me endpoint:', error);
        // Continue with initialization even if this fails
      }

      // Track user session
      const trackerService = new TrackerService();
      const userProperties = {
        event_category: "Auth",
        userId: sessionData.user.id,
        firstName: sessionData.user.firstName || "",
        lastName: sessionData.user.lastName || "",
        email: sessionData.user.email || "",
        phone: sessionData.user.phone || "",
        zip: sessionData.user.zip || ""
      };
      trackerService.sendStats({ ...userProperties, event_action: "setUser" });

      return {
        client: clientData,
        user: sessionData.user,
        token: sessionData.token,
        tokenExpiresAt: sessionData.tokenExpiresAt,
        sessionId: sessionData.id
      };
    } catch (error) {
      console.error('Session initialization failed:', error);

      // Check if this is a 401 error
      if (error.response && error.response.status === 401) {
        console.log('Authentication expired. Clearing session data...');
        localStorage.clear();
        window.location.reload();
      }

      throw error;
    }
  }

  private getClientNameFromUrl(): string | null {
    // Get client name from URL path or search params
    const pathMatch = window.location.pathname.match(/\/([^\/]+)/);
    if (pathMatch) return pathMatch[1];

    const searchParams = new URLSearchParams(window.location.search);
    const referrer = searchParams.get('referrer');
    if (referrer) {
      // Track referrer in stats
      const trackerService = new TrackerService();
      trackerService.sendStats({
        event_action: 'screen_view',
        event_category: 'PageView',
        event_label: 'Marvin Embed View',
        screenName: 'Marvin Embed View',
        referrer: decodeURIComponent(referrer)
      });

      // Try to extract client from referrer URL
      const clientFromReferrer = extractClientFromReferrer(decodeURIComponent(referrer));
      if (clientFromReferrer) {
        console.log(`Client identified from referrer: ${clientFromReferrer}`);
        return clientFromReferrer;
      }
    }

    return searchParams.get('client');
  }
}
