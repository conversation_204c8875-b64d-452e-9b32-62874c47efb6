import axios from 'axios';
import { API_BASE_URL } from '../config/environment';
import { UserInterface, SessionDataInterface } from '../interfaces/user.interface';

interface IPAResponse {
  ip: string;
  hostname: string;
  city: string;
  region: string;
  country: string;
  loc: string;
  org: string;
  postal: string;
  timezone: string;
}

export class UserService {
  private axiosInstance;
  private base: string;

  constructor() {
    this.base = API_BASE_URL || '';
    this.axiosInstance = axios.create({
      baseURL: this.base,
      headers: {
        'Content-Type': 'application/json',
        'accepts': 'application/json'
      }
    });
  }

  async getUserByEmail(clientId: string, email: string) {
    try {
      const response = await this.axiosInstance.get(`/users?email=${email}&clientId=${clientId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting user by email:', error);
      return { data: null };
    }
  }

  async createUser(userData: {
    email: string;
    clientId: string;
    firstName?: string;
    lastName?: string;
    zip?: string;
  }) {
    try {
      const response = await this.axiosInstance.post('/users', userData);
      return response.data;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  storeUserAndSessionData(user: UserInterface, token: string): void {
    localStorage.setItem('token', token);
    localStorage.setItem('user', JSON.stringify(user));
  }

  async sendMagicLink(email: string, clientId: string) {
    try {
      return await this.axiosInstance.post(`/users/magic`, {
        email,
        clientId
      });
    } catch (error) {
      console.error('Error sending magic link:', error);
      return {};
    }
  }

  async updateCurrentUserSession(data: {
    email?: string;
    firstName?: string;
    lastName?: string;
    zip?: string;
    clientId?: string;
    location?: string;
    ipa?: string;
  }): Promise<SessionDataInterface> {
    try {
      const token = localStorage.getItem('token');
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'accepts': 'application/json'
      };

      if (token) {
        headers['Authorization'] = `${token}`;
      }

      // Fix the URL path - ensure it doesn't have duplicated segments
      const response = await this.axiosInstance.put('/users/me', data, { headers });

      if (response.data && response.data.data && response.data.data.length > 0) {
        return response.data.data[0];
      }

      throw new Error('Invalid response from server');
    } catch (error) {
      console.error('Error updating user session:', error);
      return {} as SessionDataInterface;
    }
  }

  async getIPA(): Promise<IPAResponse> {
    if (window.location.hostname === 'localhost') {
      return new Promise(resolve => {
        resolve({
          ip: '************',
          hostname: 'cpe-74-64-48-102.nyc.res.rr.com',
          city: "Hell's Kitchen",
          region: 'New York',
          country: 'US',
          loc: '40.7571,-73.9657',
          org: 'AS12271 Charter Communications Inc',
          postal: '10022',
          timezone: 'America/New_York'
        });
      });
    } else {
      return this.axiosInstance.get('https://ipinfo.io/json')
    }
  }
}
