/**
 * Client Domain Mapping Configuration
 * 
 * This file contains mappings between client domains and their internal client names.
 * Used for identifying clients from referrer URLs when the embed path is empty.
 */

export interface ClientDomainMapping {
  [domain: string]: string;
}

/**
 * Map of client domains to their internal client names
 * Add new client domains here as they are onboarded
 */
export const CLIENT_DOMAIN_MAP: ClientDomainMapping = {
  // Government domains
  'emporia.gov': 'emporia',
  'arlington.gov': 'arlington',
  'yorkcounty.gov': 'york',
  'mountjacksonva.gov': 'mountjackson',
  'loudoun.gov': 'loudoun',
  'traversecitymi.gov': 'traversecity',
  'salisburymd.gov': 'salisbury',
  'myrtlebeachprst.com': 'myrtlebeachprst',
  'www.maplevalleywa.gov': 'maplevalley',
  'maplevalleywa.gov': 'maplevalley',
  
  // City/Municipal domains
  'cityofpage.org': 'page',
  'parkcity.org': 'parkcity',
  'cityofmyrtlebeach.com': 'myrtlebeach',
  'townofmorrisville.org': 'morrisville',
  'cityoftulare.com': 'tulare',
  
  // Other domains
  'roelandpark.net': 'roelandpark',
  
  // Add more client domains here as needed
  // Format: 'client-domain.com': 'clientname'
};

/**
 * Extract client name from a referrer URL
 * @param referrerUrl The full referrer URL
 * @returns Client name if found, null otherwise
 */
export function extractClientFromReferrer(referrerUrl: string): string | null {
  try {
    const url = new URL(referrerUrl);
    const hostname = url.hostname.toLowerCase();
    
    // Direct domain match
    if (CLIENT_DOMAIN_MAP[hostname]) {
      return CLIENT_DOMAIN_MAP[hostname];
    }

    // Check for subdomain patterns (e.g., www.emporia.gov)
    const domainParts = hostname.split('.');
    if (domainParts.length > 2) {
      const baseDomain = domainParts.slice(-2).join('.');
      if (CLIENT_DOMAIN_MAP[baseDomain]) {
        return CLIENT_DOMAIN_MAP[baseDomain];
      }
    }

    // Check for client name in subdomain or path
    for (const [domain, clientName] of Object.entries(CLIENT_DOMAIN_MAP)) {
      if (hostname.includes(domain.split('.')[0]) || 
          url.pathname.toLowerCase().includes(clientName)) {
        return clientName;
      }
    }

    console.log(`No client mapping found for referrer: ${hostname}`);
    return null;
  } catch (error) {
    console.error('Error parsing referrer URL:', error);
    return null;
  }
}

/**
 * Add a new client domain mapping
 * @param domain The client's domain
 * @param clientName The internal client name
 */
export function addClientDomainMapping(domain: string, clientName: string): void {
  CLIENT_DOMAIN_MAP[domain.toLowerCase()] = clientName.toLowerCase();
}

/**
 * Get all registered client domains
 * @returns Array of all registered domains
 */
export function getRegisteredDomains(): string[] {
  return Object.keys(CLIENT_DOMAIN_MAP);
}

/**
 * Check if a domain is registered
 * @param domain Domain to check
 * @returns True if domain is registered
 */
export function isDomainRegistered(domain: string): boolean {
  return domain.toLowerCase() in CLIENT_DOMAIN_MAP;
}
