import type { CSSProperties } from 'react';

// Import static logo assets
import bridgeportLogo from '../assets/clients/logo-overrides/bridgeport.png';
import broadviewLogo from '../assets/clients/logo-overrides/broadview.png';
import cullmanLogo from '../assets/clients/logo-overrides/cullman.png';
import kirklandLogo from '../assets/clients/logo-overrides/kirkland.png';
import mapleValleyLogo from '../assets/clients/logo-overrides/maple-valley.png';
import morrisvilleLogo from '../assets/clients/logo-overrides/morrisville.png';
import redondoPdLogo from '../assets/clients/logo-overrides/redondo-pd.png';
import roelandParkLogo from '../assets/clients/logo-overrides/roeland-park.png';
import salisburyLogo from '../assets/clients/logo-overrides/salisbury.png';
import tulareLogo from '../assets/clients/logo-overrides/tulare.png';
import richlandLogo from '../assets/clients/logo-overrides/richland.png';
import leedsLogo from '../assets/clients/logo-overrides/leeds.png';
import lorainLogo from '../assets/clients/logo-overrides/lorain.png';
import pleasantvilleLogo from '../assets/clients/logo-overrides/pleasantville.png';
import tallahasseeSmallLogo from '../assets/clients/logo-overrides/tallahassee-small.png';
import tallahasseeLogo from '../assets/clients/logo-overrides/tallahassee.png';
import toledoLogo from '../assets/clients/logo-overrides/toledo.png';
import andoverLogo from '../assets/clients/logo-overrides/andover.png';
import andoverSmallLogo from '../assets/clients/logo-overrides/andover-small.png';
import hermistonLogo from '../assets/clients/logo-overrides/hermiston.png';
import loudounLogo from '../assets/clients/logo-overrides/loudoun.png';
import burkeLogo from '../assets/clients/logo-overrides/burke.png';
import burkeSmallLogo from '../assets/clients/logo-overrides/burke-small.png';
import shockeyLogo from '../assets/clients/logo-overrides/shockey.png';

// Map client names to their IDs (complete mapping from AllEmbedsGrid)
export const clientNameToId: Record<string, string> = {
  'andreas': '268',
  'arlington': '211',
  'broadview': '214',
  'bridgeport': '242',
  'clayton': '236',
  'cullman': '273',
  'davecavell': '15',
  'denton': '260',
  'emporia': '283',
  'englewood': '277',
  'gadsden': '263',
  'grandrapids': '285',
  'janellekellman': '274',
  'jeffersoncounty': '291',
  'kirkland': '282',
  'leeds': '262',
  'lexydoherty': '272',
  'lorain': '278',
  'loudoun': '292',
  'maplevalley': '279',
  'marlaforsenate': '271',
  'mountjackson': '293',
  'morrisville': '281',
  'myrtlebeach': '299',
  'myrtlebeachprst': '266',
  'ourlington': '280',
  'page': '295',
  'parkcity': '288',
  'pleasantville': '300',
  'purcellville': '270',
  'redondo': '275',
  'richland': '296',
  'roelandpark': '287',
  'salisbury': '221',
  'southbay': '276',
  'springfield': '286',
  'suisun': '261',
  'tallahassee': '301',
  'toledo': '302',
  'traverse': '269',
  'traversecity': '269',
  'tulare': '234',
  'westerly': '265',
  'whitesalmon': '209',
  'winston': '267',
  'york': '294',
  'andover': '297',
  'hermiston': '303',
  'burke': '304',
  'shockey': '306'
};

// Reverse mapping: client IDs to their names
export const clientIdToName: Record<string, string> = Object.fromEntries(
  Object.entries(clientNameToId).map(([name, id]) => [id, name])
);

// Map client IDs to static logo assets (local overrides)
export const clientStaticLogoOverrides: Record<string, string> = {
  '242': bridgeportLogo, // Bridgeport
  '214': broadviewLogo, // Broadview
  '273': cullmanLogo, // Cullman
  '282': kirklandLogo, // Kirkland
  '279': mapleValleyLogo, // Maple Valley
  '281': morrisvilleLogo, // Morrisville
  '275': redondoPdLogo, // Redondo PD
  '287': roelandParkLogo, // Roeland Park
  '221': salisburyLogo, // Salisbury
  '234': tulareLogo, // Tulare
  '296': richlandLogo, // Richland
  '262': leedsLogo, // Leeds
  '278': lorainLogo, // Lorain
  '300': pleasantvilleLogo, // Pleasantville
  '301': tallahasseeLogo, // Tallahassee
  '302': toledoLogo, // Toledo
  '297': andoverLogo, // Andover
  '303': hermistonLogo, // Hermiston
  '304': burkeLogo, // Burke County
  '306': shockeyLogo, // Shockey
  '292': loudounLogo // Loudoun County
};

// Map client IDs to their logo URLs (maintain existing mappings)
export const clientLogoUrls: Record<string, string> = {
  '283': 'https://files.repd.us/manual/Emporia-Icon-White.png', // Emporia
  '295': 'https://files.repd.us/manual/Page-Logo-White.png', // Page
  '294': 'https://files.repd.us/manual/York-Logo.png', // York
  // '281': 'https://files.repd.us/manual/Town-of-Morrisville-Logo.png', // Morrisville - now using static override
  '211': 'https://files.repd.us/manual/City-of-Arlington-Logo.png', // Arlington
  '291': 'https://files.repd.us/manual/Jefferson-County-Logo.png', // Jefferson County
  '15': 'https://files.repd.us/manual/Dave-Cavell-Logo.png', // Dave Cavell
  // '221': 'https://files.repd.us/manual/Salisbury-Logo.png', // Salisbury - now using static override
  // '214': 'https://files.repd.us/manual/Broadview-Logo.png', // Broadview - now using static override
  '271': 'https://files.repd.us/manual/Marla-for-Senate-Logo.png', // Marla
  '261': 'https://files.repd.us/manual/Suisun-City-Logo.png', // Suisun City
  // '234': 'https://files.repd.us/manual/Tulare-Logo.png', // Tulare - now using static override
  '269': 'https://files.repd.us/manual/Traverse-City-Logo.png', // Traverse City
  // '287': 'https://files.repd.us/manual/Roeland-Park-Logo.png', // Roeland Park - now using static override
  '299': 'https://files.repd.us/manual/Myrtle-Beach-Logo.png', // Myrtle Beach
  '293': 'https://files.repd.us/manual/Mount-Jackson-Logo.png', // Mount Jackson
  '292': 'https://files.repd.us/manual/Loudoun-County-Logo.png', // Loudoun County
  '288': 'https://files.repd.us/manual/Park-City-Logo.png' // Park City
};

// Client-specific logo styling configuration
export const clientLogoStyles: Record<string, CSSProperties> = {
  '211': { // Arlington
    width: "300px"
  },
  '221': { // Salisbury
    borderRadius: "20px",
    width: "300px"
  },
  '214': { // Broadview
    width: "165px"
  },
  '242': { // Bridgeport
    width: "165px"
  },
  '260': { // Denton
    width: "300px"
  },
  '261': { // Suisun City
    width: "165px"
  },
  '262': { // City of Leeds
    width: "330px"
  },
  '263': { // City of Gadsden
    width: "300px"
  },
  '266': { // Myrtle Beach PRST
    width: "220px"
  },
  '269': { // Traverse City
    width: "300px"
  },
  '270': { // Purcellville
    width: "300px"
  },
  '273': { // Cullman
    width: "260px"
  },
  '276': { // South Bay
    width: "350px"
  },
  '278': { // Lorain
    width: "350px"
  },
  '279': { // Maple Valley
    width: "260px",
    marginBottom: "25px",
    borderRadius: "20px"
  },
  '281': { // Morrisville
    width: "180px",
  },
  '282': { // Kirkland
    width: "165px"
  },
  '287': { // Roeland Park
    width: "300px"
  },
  '299': { // Myrtle Beach
    width: "165px"
  },
  '293': { // Mount Jackson
    width: "230px",
    background: "white",
    borderRadius: "20px",
    padding: "20px"
  },
  '292': { // Loudoun County
    width: "390px"
  },
  '288': { // Park City
    width: "350px",
    background: "white",
    borderRadius: "20px",
    padding: "20px"
  },
  '295': { // Page
    width: "300px"
  },
  '296': { // Richland
    width: "250px"
  },
  '300': { // Pleasantville
    width: "250px"
  },
  '301': { // Tallahassee
    width: "300px"
  },
  '302': { // Toledo
    width: "250px"
  },
  '297': { // Andover
    width: "330px"
  },
  '303': { // Hermiston
    width: "180px"
  },
  '304': { // Burke County
    width: "380px"
  },
  '306': { // Shockey
    width: "350px"
  }
};

// Client-specific logo URL overrides (for special cases)
export const clientLogoUrlOverrides: Record<string, string> = {
  '283': 'https://files.repd.us/manual/Emporia-Logo-White.png', // Emporia override
  '260': 'https://files.repd.us/manual/denton logo.png', // Denton
  '278': 'https://files.repd.us/manual/Lorain logo.png', // Lorain
  '294': 'https://files.repd.us/manual/York_Logo-removebg-preview.png' // York override
};

// MinimizedButton-specific logo overrides (separate from main logo system)
export const minimizedButtonLogoOverrides: Record<string, string> = {
  '301': tallahasseeSmallLogo, // Tallahassee - use small version for MinimizedButton
  '297': andoverSmallLogo, // Andover
  '304': burkeSmallLogo // Burke County
};

/**
 * Get the appropriate logo URL for a client
 * Priority order:
 * 1. Check for static logo overrides (local assets)
 * 2. Use client prop's logoURL if available
 * 3. Check for client-specific URL overrides
 * 4. Fall back to hardcoded clientLogoUrls mapping
 * 5. Default to null (will use SvgIcon)
 */
export function getClientLogoUrl(clientId: string | null, clientLogoUrl?: string): string | null {
  // First check for static logo overrides
  if (clientId && clientStaticLogoOverrides[clientId]) {
    return clientStaticLogoOverrides[clientId];
  }

  if (clientId && clientLogoUrlOverrides[clientId]) {
    return clientLogoUrlOverrides[clientId];
  }

  if (clientLogoUrl) {
    return clientLogoUrl;
  }

  if (clientId && clientLogoUrls[clientId]) {
    return clientLogoUrls[clientId];
  }

  return null;
}

/**
 * Get the appropriate logo URL specifically for MinimizedButton
 * This allows MinimizedButton to have different logos than the main CityIllustration
 * Priority order:
 * 1. Check for MinimizedButton-specific logo overrides
 * 2. Fall back to the standard getClientLogoUrl logic
 */
export function getMinimizedButtonLogoUrl(clientId: string | null, clientLogoUrl?: string): string | null {
  // First check for MinimizedButton-specific overrides
  if (clientId && minimizedButtonLogoOverrides[clientId]) {
    return minimizedButtonLogoOverrides[clientId];
  }

  // Fall back to standard logo resolution
  return getClientLogoUrl(clientId, clientLogoUrl);
}

/**
 * Get client-specific logo styles
 */
export function getClientLogoStyles(clientId: string | null): CSSProperties | undefined {
  return clientId ? clientLogoStyles[clientId] : undefined;
}

/**
 * Get current client ID from localStorage or URL
 */
export function getCurrentClientId(client?: { id?: string } | null): string | null {
  // Get client ID from localStorage or URL for backward compatibility
  const clientId = localStorage.getItem('clientId');
  const pathMatch = window.location.pathname.match(/\/([^\/]+)/);
  const clientNameFromUrl = pathMatch ? pathMatch[1].toLowerCase() : null;

  // Determine client ID from URL or localStorage
  let currentClientId = clientId;
  if (clientNameFromUrl && clientNameToId[clientNameFromUrl]) {
    currentClientId = clientNameToId[clientNameFromUrl];
  }

  // Use client prop's ID if available
  if (client?.id) {
    currentClientId = client.id;
  }

  return currentClientId;
}
