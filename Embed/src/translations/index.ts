export const translations = {
  en: {
    poweredBy: "Powered by Rep'd",
    askCity: "Ask [CLIENT_NAME]",
    whatQuestion: "What's your question?",
    askAgain: "Ask Again",
    loading: "Loading popular answers...",
    minimizeChat: "Minimize chat",
    closeChat: "Close chat",
    askTheCity: "Ask [CLIENT_NAME]",
    seeMoreVideos: "See more videos",
    popularAnswers: "Popular Answers",
    loadingAnswers: "Loading answers...",
    noAnswersAvailable: "No answers available",
    missingClientOrUserId: "Missing client or user ID",
    notAuthenticated: "Not authenticated",
    failedToLoadAnswers: "Failed to load answers",
    relatedAnswers: "Related Answers",
    networkError: "Network connection error",
    authTokenMissing: "Authentication required",
    genericError: "An error occurred",
    invalidResponse: "Invalid response from server",
    sources: "Sources",
    didntFindAnswer: "Didn't get an answer to your question?",
    expand: "Expand",
    collapse: "Collapse",
    aiDisclaimer: "DISCLAIMER: AI may produce inaccurate information about people, places or facts.",
    submitQuestionExplanation: "You can expect a response within 3-5 business days.",
    yourQuestion: "Your Question",
    submitQuestion: "Submit Question",
    submitting: "Submitting...",
    questionSubmitted: "Question submitted successfully",
    errorSubmittingQuestion: "Error submitting question",
    pleaseInputQuestion: "Please input your question",
    questionTooLong: "Question is too long",
    pleaseSelectCategory: "Please select a category",
    pleaseEnterEmail: "Please enter valid email",
    pleaseAddName: "Please add your name",
    pleaseAddZipCode: "Please add zip code",
    selectCategory: "Select a category",
    loadingCategories: "Loading categories...",
    zipCode: "Zip Code",
    required: "required",
    displayedNextToQuestion: "Displayed next to your question",
    notDisplayedToOtherUsers: "This will not be displayed to other users",
    failedLoadCategories: "Failed to load categories",
    email: "Email",
    firstName: "First Name",
    lastName: "Last Name",
    displayedWithQuestion: "Displayed next to your question",
    notDisplayed: "This will not be displayed to other users",
    charactersLeft: "characters left",
    category: "Category",
    welcomeBack: "Welcome back",
    bestVideoMatch: "Best Video Match",
    watch: "Watch"
  },
  es: {
    poweredBy: "Desarrollado por Rep'd",
    askCity: "Hazle una pregunta a [CLIENT_NAME]",
    whatQuestion: "¿Cuál es tu pregunta?",
    askAgain: "Preguntar de nuevo",
    loading: "Cargando respuestas populares...",
    minimizeChat: "Minimizar chat",
    closeChat: "Cerrar chat",
    askTheCity: "Pregunta a [CLIENT_NAME]",
    seeMoreVideos: "Ver más videos",
    popularAnswers: "Respuestas Populares",
    loadingAnswers: "Cargando respuestas...",
    noAnswersAvailable: "No hay respuestas disponibles",
    missingClientOrUserId: "Falta el ID del cliente o usuario",
    notAuthenticated: "No autenticado",
    failedToLoadAnswers: "Error al cargar las respuestas",
    relatedAnswers: "Respuestas Relacionadas",
    networkError: "Error de conexión de red",
    authTokenMissing: "Autenticación requerida",
    genericError: "Ocurrió un error",
    invalidResponse: "Respuesta inválida del servidor",
    sources: "Fuentes",
    didntFindAnswer: "¿No encontraste lo que buscabas?",
    expand: "Expandir",
    collapse: "Contraer",
    aiDisclaimer: "AVISO: La IA puede producir información inexacta sobre personas, lugares o hechos.",
    submitQuestionExplanation: "Envía tu pregunta y nos aseguraremos de que sea respondida por la persona adecuada.",
    yourQuestion: "Tu Pregunta",
    submitQuestion: "Enviar Pregunta",
    submitting: "Enviando...",
    questionSubmitted: "Pregunta enviada exitosamente",
    errorSubmittingQuestion: "Error al enviar la pregunta",
    pleaseInputQuestion: "Por favor ingrese su pregunta",
    questionTooLong: "La pregunta es demasiado larga",
    pleaseSelectCategory: "Por favor seleccione una categoría",
    pleaseEnterEmail: "Por favor ingrese un email válido",
    pleaseAddName: "Por favor agregue su nombre",
    pleaseAddZipCode: "Por favor agregue su código postal",
    selectCategory: "Seleccione una categoría",
    loadingCategories: "Cargando categorías...",
    zipCode: "Código Postal",
    required: "requerido",
    displayedNextToQuestion: "Se mostrará junto a su pregunta",
    notDisplayedToOtherUsers: "Esto no se mostrará a otros usuarios",
    failedLoadCategories: "Error al cargar categorías",
    email: "Correo electrónico",
    firstName: "Nombre",
    lastName: "Apellido",
    displayedWithQuestion: "Se mostrará junto a su pregunta",
    notDisplayed: "Esto no se mostrará a otros usuarios",
    charactersLeft: "caracteres restantes",
    category: "Categoría",
    welcomeBack: "Bienvenido de nuevo",
    watch: "Ver"
  },
  ht: {
    poweredBy: "Alimante pa Rep'd",
    askCity: "Poze [CLIENT_NAME] yon kesyon",
    whatQuestion: "Ki kesyon ou genyen?",
    askAgain: "Mande ankò",
    loading: "Chajman repons popilè yo...",
    minimizeChat: "Minimize chat",
    closeChat: "Fèmen chat",
    askTheCity: "Poze [CLIENT_NAME] yon kesyon",
    seeMoreVideos: "Gade plis videyo",
    popularAnswers: "Repons Popilè yo",
    loadingAnswers: "Chajman repons yo...",
    noAnswersAvailable: "Pa gen repons disponib",
    missingClientOrUserId: "ID kliyan oswa itilizatè manke",
    notAuthenticated: "Pa otantifye",
    failedToLoadAnswers: "Echèk nan chajman repons yo",
    relatedAnswers: "Repons Ki Gen Rapò",
    networkError: "Erè koneksyon rezo",
    authTokenMissing: "Otantifikasyon obligatwa",
    genericError: "Yon erè te pase",
    invalidResponse: "Repons envalid nan sèvè a",
    sources: "Sous",
    didntFindAnswer: "Ou pa jwenn sa w ap chèche a?",
    expand: "Elaji",
    collapse: "Redwi",
    aiDisclaimer: "AVÈTISMAN: IA ka pwodwi enfòmasyon ki pa egzat sou moun, kote oswa reyalite.",
    submitQuestionExplanation: "Soumèt kesyon ou an epi nou pral asire ke moun ki apwopriye a reponn li.",
    yourQuestion: "Kesyon Ou",
    submitQuestion: "Soumèt Kesyon",
    submitting: "Ap soumèt...",
    questionSubmitted: "Kesyon soumèt avèk siksè",
    errorSubmittingQuestion: "Erè nan soumisyon kesyon an",
    pleaseInputQuestion: "Tanpri antre kesyon ou",
    questionTooLong: "Kesyon an twò long",
    pleaseSelectCategory: "Tanpri chwazi yon kategori",
    pleaseEnterEmail: "Tanpri antre yon imèl valid",
    pleaseAddName: "Tanpri ajoute non ou",
    selectCategory: "Chwazi yon kategori",
    loadingCategories: "Ap chaje kategori yo...",
    failedLoadCategories: "Echèk nan chajman kategori yo",
    email: "Imèl",
    firstName: "Prenon",
    lastName: "Non fanmi",
    displayedWithQuestion: "Ap parèt akote kesyon ou",
    notDisplayed: "Sa pa pral parèt pou lòt itilizatè yo",
    charactersLeft: "karaktè ki rete",
    category: "Kategori",
    welcomeBack: "Bonvenn! Ou gen deja kont! ",
    bestVideoMatch: "Pi Bon Match Videyo",
    watch: "Gade"
  },
  hi: {
    poweredBy: "Rep'd द्वारा संचालित",
    askCity: "[CLIENT_NAME] से एक प्रश्न पूछें",
    whatQuestion: "आपका प्रश्न क्या है?",
    askAgain: "फिर से पूछें",
    loading: "लोकप्रिय उत्तर लोड हो रहे हैं...",
    minimizeChat: "चैट को छोटा करें",
    closeChat: "चैट बंद करें",
    askTheCity: "[CLIENT_NAME] से पूछें",
    seeMoreVideos: "और वीडियो देखें",
    popularAnswers: "लोकप्रिय उत्तर",
    loadingAnswers: "उत्तर लोड हो रहे हैं...",
    noAnswersAvailable: "कोई उत्तर उपलब्ध नहीं",
    missingClientOrUserId: "क्लाइंट या यूजर आईडी गायब है",
    notAuthenticated: "प्रमाणित नहीं",
    failedToLoadAnswers: "उत्तर लोड करने में विफल",
    relatedAnswers: "संबंधित उत्तर",
    networkError: "नेटवर्क कनेक्शन त्रुटि",
    authTokenMissing: "प्रमाणीकरण आवश्यक",
    genericError: "एक त्रुटि हुई",
    invalidResponse: "सर्वर से अमान्य प्रतिक्रिया",
    sources: "स्रोत",
    didntFindAnswer: "आपको जो चाहिए वह नहीं मिला?",
    expand: "विस्तार करें",
    collapse: "संकुचित करें",
    aiDisclaimer: "अस्वीकरण: AI लोगों, स्थानों या तथ्यों के बारे में गलत जानकारी उत्पन्न कर सकता है।",
    submitQuestionExplanation: "अपना प्रश्न जमा करें और हम सुनिश्चित करेंगे कि सही व्यक्ति द्वारा इसका उत्तर दिया जाए।",
    yourQuestion: "आपका प्रश्न",
    submitQuestion: "प्रश्न जमा करें",
    submitting: "जमा कर रहे हैं...",
    questionSubmitted: "प्रश्न सफलतापूर्वक जमा किया गया",
    errorSubmittingQuestion: "प्रश्न जमा करने में त्रुटि",
    pleaseInputQuestion: "कृपया अपना प्रश्न दर्ज करें",
    questionTooLong: "प्रश्न बहुत लंबा है",
    pleaseSelectCategory: "कृपया एक श्रेणी चुनें",
    pleaseEnterEmail: "कृपया वैध ईमेल दर्ज करें",
    pleaseAddName: "कृपया अपना नाम जोड़ें",
    selectCategory: "श्रेणी चुनें",
    loadingCategories: "श्रेणियां लोड हो रही हैं...",
    failedLoadCategories: "श्रेणियां लोड करने में विफल",
    email: "ईमेल",
    firstName: "पहला नाम",
    lastName: "अंतिम नाम",
    displayedWithQuestion: "आपके प्रश्न के साथ दिखाया जाएगा",
    notDisplayed: "यह अन्य उपयोगकर्ताओं को नहीं दिखाया जाएगा",
    charactersLeft: "अक्षर शेष",
    category: "श्रेणी",
    welcomeBack: "स्वागत आप! आपका पहला अनुभव! ",
    bestVideoMatch: "सबसे अच्छा वीडियो मिलान",
    watch: "देखें"
  },
  vi: {
    poweredBy: "Được cung cấp bởi Rep'd",
    askCity: "Đặt câu hỏi cho [CLIENT_NAME]",
    whatQuestion: "Câu hỏi của bạn là gì?",
    askAgain: "Hỏi lại",
    loading: "Đang tải câu trả lời phổ biến...",
    minimizeChat: "Thu nhỏ chat",
    closeChat: "Đóng chat",
    askTheCity: "Hỏi [CLIENT_NAME]",
    seeMoreVideos: "Xem thêm video",
    popularAnswers: "Câu Trả Lời Phổ Biến",
    loadingAnswers: "Đang tải câu trả lời...",
    noAnswersAvailable: "Không có câu trả lời",
    missingClientOrUserId: "Thiếu ID khách hàng hoặc người dùng",
    notAuthenticated: "Chưa xác thực",
    failedToLoadAnswers: "Không thể tải câu trả lời",
    relatedAnswers: "Câu Trả Lời Liên Quan",
    networkError: "Lỗi kết nối mạng",
    authTokenMissing: "Yêu cầu xác thực",
    genericError: "Đã xảy ra lỗi",
    invalidResponse: "Phản hồi không hợp lệ từ máy chủ",
    sources: "Nguồn",
    didntFindAnswer: "Không tìm thấy điều bạn đang tìm kiếm?",
    expand: "Mở rộng",
    collapse: "Thu gọn",
    aiDisclaimer: "TUYÊN BỐ MIỄN TRỪ TRÁCH NHIỆM: AI có thể tạo ra thông tin không chính xác về người, địa điểm hoặc sự kiện.",
    submitQuestionExplanation: "Gửi câu hỏi của bạn và chúng tôi sẽ đảm bảo nó được trả lời bởi người phù hợp.",
    yourQuestion: "Câu Hỏi Của Bạn",
    submitQuestion: "Gửi Câu Hỏi",
    submitting: "Đang gửi...",
    questionSubmitted: "Câu hỏi đã được gửi thành công",
    errorSubmittingQuestion: "Lỗi khi gửi câu hỏi",
    pleaseInputQuestion: "Vui lòng nhập câu hỏi của bạn",
    questionTooLong: "Câu hỏi quá dài",
    pleaseSelectCategory: "Vui lòng chọn một danh mục",
    pleaseEnterEmail: "Vui lòng nhập email hợp lệ",
    pleaseAddName: "Vui lòng thêm tên của bạn",
    selectCategory: "Chọn danh mục",
    loadingCategories: "Đang tải danh mục...",
    failedLoadCategories: "Không thể tải danh mục",
    email: "Email",
    firstName: "Tên",
    lastName: "Họ",
    displayedWithQuestion: "Sẽ hiển thị cùng với câu hỏi của bạn",
    notDisplayed: "Điều này sẽ không hiển thị cho người dùng khác",
    charactersLeft: "ký tự còn lại",
    category: "Danh mục",
    welcomeBack: "Chào mừng trở lại! ",
    bestVideoMatch: "Video Phù Hợp Nhất",
    watch: "Xem"
  },
  zh: {
    poweredBy: "由 Rep'd 提供支持",
    askCity: "向[CLIENT_NAME]提问",
    whatQuestion: "您的问题是什么？",
    askAgain: "重新提问",
    loading: "正在加载热门回答...",
    minimizeChat: "最小化聊天",
    closeChat: "关闭聊天",
    askTheCity: "问[CLIENT_NAME]",
    seeMoreVideos: "查看更多视频",
    popularAnswers: "热门回答",
    loadingAnswers: "正在加载回答...",
    noAnswersAvailable: "暂无回答",
    missingClientOrUserId: "缺少客户端或用户ID",
    notAuthenticated: "未经认证",
    failedToLoadAnswers: "加载回答失败",
    relatedAnswers: "相关回答",
    networkError: "网络连接错误",
    authTokenMissing: "需要认证",
    genericError: "发生错误",
    invalidResponse: "服务器响应无效",
    sources: "来源",
    didntFindAnswer: "没有找到您要找的内容？",
    expand: "展开",
    collapse: "收起",
    aiDisclaimer: "免责声明：AI可能会产生关于人物、地点或事实的不准确信息。",
    submitQuestionExplanation: "提交您的问题，我们会确保由合适的人来回答。",
    yourQuestion: "您的问题",
    submitQuestion: "提交问题",
    submitting: "正在提交...",
    questionSubmitted: "问题提交成功",
    errorSubmittingQuestion: "提交问题时出错",
    pleaseInputQuestion: "请输入您的问题",
    questionTooLong: "问题太长",
    pleaseSelectCategory: "请选择一个类别",
    pleaseEnterEmail: "请输入有效的电子邮件",
    pleaseAddName: "请添加您的姓名",
    selectCategory: "选择类别",
    loadingCategories: "正在加载类别...",
    failedLoadCategories: "加载类别失败",
    email: "电子邮件",
    firstName: "名字",
    lastName: "姓氏",
    displayedWithQuestion: "将与您的问题一起显示",
    notDisplayed: "这不会显示给其他用户",
    charactersLeft: "剩余字符",
    category: "类别",
    welcomeBack: "欢迎回来! ",
    bestVideoMatch: "最佳视频匹配",
    watch: "观看"
  },
  fa: {
    poweredBy: "قدرت گرفته از Rep'd",
    askCity: "از [CLIENT_NAME] بپرسید",
    whatQuestion: "سوال شما چیست؟",
    askAgain: "دوباره بپرسید",
    loading: "در حال بارگذاری پاسخ‌های محبوب...",
    minimizeChat: "کوچک کردن چت",
    closeChat: "بستن چت",
    askTheCity: "از [CLIENT_NAME] بپرسید",
    seeMoreVideos: "مشاهده ویدیوهای بیشتر",
    popularAnswers: "پاسخ‌های محبوب",
    loadingAnswers: "در حال بارگذاری پاسخ‌ها...",
    noAnswersAvailable: "پاسخی در دسترس نیست",
    missingClientOrUserId: "شناسه مشتری یا کاربر موجود نیست",
    notAuthenticated: "احراز هویت نشده",
    failedToLoadAnswers: "بارگذاری پاسخ‌ها ناموفق بود",
    relatedAnswers: "پاسخ‌های مرتبط",
    networkError: "خطای اتصال شبکه",
    authTokenMissing: "احراز هویت مورد نیاز است",
    genericError: "خطایی رخ داده است",
    invalidResponse: "پاسخ سرور نامعتبر است",
    sources: "منابع",
    didntFindAnswer: "آنچه دنبالش بودید پیدا نکردید؟",
    expand: "گسترش",
    collapse: "جمع کردن",
    aiDisclaimer: "تذکر: هوش مصنوعی ممکن است اطلاعات نادرستی درباره افراد، مکان‌ها یا حقایق تولید کند.",
    submitQuestionExplanation: "سوال خود را ارسال کنید و ما اطمینان حاصل می‌کنیم که فرد مناسب پاسخ دهد.",
    yourQuestion: "سوال شما",
    submitQuestion: "ارسال سوال",
    submitting: "در حال ارسال...",
    questionSubmitted: "سوال با موفقیت ارسال شد",
    errorSubmittingQuestion: "خطا در ارسال سوال",
    pleaseInputQuestion: "لطفاً سوال خود را وارد کنید",
    questionTooLong: "سوال خیلی طولانی است",
    pleaseSelectCategory: "لطفاً یک دسته انتخاب کنید",
    pleaseEnterEmail: "لطفاً ایمیل معتبر وارد کنید",
    pleaseAddName: "لطفاً نام خود را اضافه کنید",
    pleaseAddZipCode: "لطفاً کد پستی اضافه کنید",
    selectCategory: "انتخاب دسته",
    loadingCategories: "در حال بارگذاری دسته‌ها...",
    zipCode: "کد پستی",
    required: "الزامی",
    displayedNextToQuestion: "در کنار سوال شما نمایش داده می‌شود",
    notDisplayedToOtherUsers: "این برای سایر کاربران نمایش داده نمی‌شود",
    failedLoadCategories: "بارگذاری دسته‌ها ناموفق بود",
    email: "ایمیل",
    firstName: "نام",
    lastName: "نام خانوادگی",
    displayedWithQuestion: "در کنار سوال شما نمایش داده می‌شود",
    notDisplayed: "این برای سایر کاربران نمایش داده نمی‌شود",
    charactersLeft: "کاراکتر باقی‌مانده",
    category: "دسته",
    welcomeBack: "خوش آمدید!",
    bestVideoMatch: "بهترین تطبیق ویدیو",
    watch: "تماشا"
  },
  te: {
    poweredBy: "Rep'd ద్వారా శక్తిని పొందింది",
    askCity: "[CLIENT_NAME]ని అడగండి",
    whatQuestion: "మీ ప్రశ్న ఏమిటి?",
    askAgain: "మళ్లీ అడగండి",
    loading: "ప్రసిద్ధ సమాధానాలను లోడ్ చేస్తోంది...",
    minimizeChat: "చాట్‌ను కుచించండి",
    closeChat: "చాట్‌ను మూసివేయండి",
    askTheCity: "[CLIENT_NAME]ని అడగండి",
    seeMoreVideos: "మరిన్ని వీడియోలను చూడండి",
    popularAnswers: "ప్రసిద్ధ సమాధానాలు",
    loadingAnswers: "సమాధానాలను లోడ్ చేస్తోంది...",
    noAnswersAvailable: "సమాధానాలు అందుబాటులో లేవు",
    missingClientOrUserId: "క్లయింట్ లేదా వినియోగదారు ID లేదు",
    notAuthenticated: "ప్రమాణీకరించబడలేదు",
    failedToLoadAnswers: "సమాధానాలను లోడ్ చేయడంలో విఫలమైంది",
    relatedAnswers: "సంబంధిత సమాధానాలు",
    networkError: "నెట్‌వర్క్ కనెక్షన్ లోపం",
    authTokenMissing: "ప్రమాణీకరణ అవసరం",
    genericError: "లోపం సంభవించింది",
    invalidResponse: "సర్వర్ ప్రతిస్పందన చెల్లదు",
    sources: "మూలాలు",
    didntFindAnswer: "మీరు వెతుకుతున్నది కనుగొనలేకపోయారా?",
    expand: "విస్తరించండి",
    collapse: "కుదించండి",
    aiDisclaimer: "నిరాకరణ: AI వ్యక్తులు, స్థలాలు లేదా వాస్తవాల గురించి తప్పుడు సమాచారాన్ని ఉత్పత్తి చేయవచ్చు.",
    submitQuestionExplanation: "మీ ప్రశ్నను సమర్పించండి మరియు సరైన వ్యక్తి సమాధానం ఇవ్వడానికి మేము నిర్ధారిస్తాము.",
    yourQuestion: "మీ ప్రశ్న",
    submitQuestion: "ప్రశ్నను సమర్పించండి",
    submitting: "సమర్పిస్తోంది...",
    questionSubmitted: "ప్రశ్న విజయవంతంగా సమర్పించబడింది",
    errorSubmittingQuestion: "ప్రశ్నను సమర్పించడంలో లోపం",
    pleaseInputQuestion: "దయచేసి మీ ప్రశ్నను నమోదు చేయండి",
    questionTooLong: "ప్రశ్న చాలా పొడవుగా ఉంది",
    pleaseSelectCategory: "దయచేసి ఒక వర్గాన్ని ఎంచుకోండి",
    pleaseEnterEmail: "దయచేసి చెల్లుబాటు అయ్యే ఇమెయిల్‌ను నమోదు చేయండి",
    pleaseAddName: "దయచేసి మీ పేరును జోడించండి",
    pleaseAddZipCode: "దయచేసి జిప్ కోడ్‌ను జోడించండి",
    selectCategory: "వర్గాన్ని ఎంచుకోండి",
    loadingCategories: "వర్గాలను లోడ్ చేస్తోంది...",
    zipCode: "జిప్ కోడ్",
    required: "అవసరం",
    displayedNextToQuestion: "మీ ప్రశ్న పక్కన ప్రదర్శించబడుతుంది",
    notDisplayedToOtherUsers: "ఇది ఇతర వినియోగదారులకు ప్రదర్శించబడదు",
    failedLoadCategories: "వర్గాలను లోడ్ చేయడంలో విఫలమైంది",
    email: "ఇమెయిల్",
    firstName: "మొదటి పేరు",
    lastName: "చివరి పేరు",
    displayedWithQuestion: "మీ ప్రశ్నతో పాటు ప్రదర్శించబడుతుంది",
    notDisplayed: "ఇది ఇతర వినియోగదారులకు ప్రదర్శించబడదు",
    charactersLeft: "అక్షరాలు మిగిలాయి",
    category: "వర్గం",
    welcomeBack: "తిరిగి స్వాగతం!",
    bestVideoMatch: "ఉత్తమ వీడియో మ్యాచ్",
    watch: "చూడండి"
  },
  ar: {
    poweredBy: "مدعوم من Rep'd",
    askCity: "اسأل [CLIENT_NAME]",
    whatQuestion: "ما هو سؤالك؟",
    askAgain: "اسأل مرة أخرى",
    loading: "جاري تحميل الإجابات الشائعة...",
    minimizeChat: "تصغير المحادثة",
    closeChat: "إغلاق المحادثة",
    askTheCity: "اسأل [CLIENT_NAME]",
    seeMoreVideos: "مشاهدة المزيد من الفيديوهات",
    popularAnswers: "الإجابات الشائعة",
    loadingAnswers: "جاري تحميل الإجابات...",
    noAnswersAvailable: "لا توجد إجابات متاحة",
    missingClientOrUserId: "معرف العميل أو المستخدم مفقود",
    notAuthenticated: "غير مصادق عليه",
    failedToLoadAnswers: "فشل في تحميل الإجابات",
    relatedAnswers: "الإجابات ذات الصلة",
    networkError: "خطأ في اتصال الشبكة",
    authTokenMissing: "المصادقة مطلوبة",
    genericError: "حدث خطأ",
    invalidResponse: "استجابة الخادم غير صالحة",
    sources: "المصادر",
    didntFindAnswer: "لم تجد ما تبحث عنه؟",
    expand: "توسيع",
    collapse: "طي",
    aiDisclaimer: "إخلاء مسؤولية: قد ينتج الذكاء الاصطناعي معلومات غير دقيقة حول الأشخاص أو الأماكن أو الحقائق.",
    submitQuestionExplanation: "أرسل سؤالك وسنتأكد من أن الشخص المناسب يجيب عليه.",
    yourQuestion: "سؤالك",
    submitQuestion: "إرسال السؤال",
    submitting: "جاري الإرسال...",
    questionSubmitted: "تم إرسال السؤال بنجاح",
    errorSubmittingQuestion: "خطأ في إرسال السؤال",
    pleaseInputQuestion: "يرجى إدخال سؤالك",
    questionTooLong: "السؤال طويل جداً",
    pleaseSelectCategory: "يرجى اختيار فئة",
    pleaseEnterEmail: "يرجى إدخال بريد إلكتروني صالح",
    pleaseAddName: "يرجى إضافة اسمك",
    pleaseAddZipCode: "يرجى إضافة الرمز البريدي",
    selectCategory: "اختر فئة",
    loadingCategories: "جاري تحميل الفئات...",
    zipCode: "الرمز البريدي",
    required: "مطلوب",
    displayedNextToQuestion: "سيتم عرضه بجانب سؤالك",
    notDisplayedToOtherUsers: "لن يتم عرض هذا للمستخدمين الآخرين",
    failedLoadCategories: "فشل في تحميل الفئات",
    email: "البريد الإلكتروني",
    firstName: "الاسم الأول",
    lastName: "اسم العائلة",
    displayedWithQuestion: "سيتم عرضه مع سؤالك",
    notDisplayed: "لن يتم عرض هذا للمستخدمين الآخرين",
    charactersLeft: "حرف متبقي",
    category: "الفئة",
    welcomeBack: "مرحباً بعودتك!",
    bestVideoMatch: "أفضل مطابقة فيديو",
    watch: "شاهد"
  }
};

export type Language = keyof typeof translations;
export type TranslationKey = keyof typeof translations.en;
