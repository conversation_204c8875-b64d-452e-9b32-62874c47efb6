<?xml version="1.0" encoding="UTF-8"?>
<svg id="AskTheCityButton" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 122 124">
  <!-- Generator: Adobe Illustrator 29.1.0, SVG Export Plug-In . SVG Version: 2.1.0 Build 142)  -->
  <defs>
    <style>
      .st0, .st1, .st2, .st3, .st4 {
        fill: #fff;
      }

      .st0, .st2, .st5 {
        fill-rule: evenodd;
      }

      .st1, .st6 {
        font-family: Nunito-ExtraBold, Nunito, 'Nunito Sans';
        font-weight: 700;
        font-size: 12px;
        fill: #fff;
      }

      .st2 {
        isolation: isolate;
        opacity: .8;
      }

      .st3 {
        stroke: #e73067;
        stroke-width: 2.2px;
      }

      .st7 {
        stroke: #2760cf;
        stroke-opacity: .1;
        stroke-width: 2px;
      }

      .st7, .st8 {
        fill: none;
      }

      .st5 {
        fill: var(--background-color);
      }

      .st8 {
        stroke: #e9effa;
      }
    </style>
    <linearGradient id="linear-gradient" x1="61" y1="109" x2="61" y2="23" gradientTransform="translate(0 126) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ff3974"/>
      <stop offset="1" stop-color="#e73067"/>
    </linearGradient>
    <pattern id="img1" patternUnits="userSpaceOnUse" width="40" height="80">
      <image x="0" y="23" width="40" height="35" href="" data-client-logo="true"></image>
    </pattern>
  </defs>
  <g>
    <path class="st4" d="M61,10h0c27.6,0,50,22.4,50,50h0c0,27.6-22.4,50-50,50h0c-27.6,0-50-22.4-50-50h0c0-27.6,22.4-50,50-50Z"/>
    <path class="st8" d="M61,9.5h0c27.9,0,50.5,22.6,50.5,50.5h0c0,27.9-22.6,50.5-50.5,50.5h0c-27.9,0-50.5-22.6-50.5-50.5h0c0-27.9,22.6-50.5,50.5-50.5Z"/>
    <circle class="st7" cx="61" cy="60" r="46"/>
    <path class="st5" d="M61,103c23.75,0,43-19.25,43-43S84.75,17,61,17,18,36.25,18,60c0,8.52,2.48,16.47,6.76,23.15,1.01,1.58,1.38,3.51,0.86,5.32l-1.18,4.04c-0.2,0.7,0.39,1.38,1.11,1.27l4.78-0.71c1.72-0.25,3.45,0.27,4.84,1.31,7.19,5.41,16.14,8.62,25.84,8.62Z"/>
    <g id="askButtonIcon">
      <path class="st2" d="M60.9,28.3c0-1.7-1.8-2.8-3.3-2l-3.4,1.7c-.8.4-1.2,1.2-1.2,2v6.1h-2.2c-1.2,0-2.2,1-2.2,2.2v5.6h-.6c-1.2,0-2.2,1-2.2,2.2v10.1h-.6c-.6,0-1.1.5-1.1,1.1s.5,1.1,1.1,1.1h31.5c.6,0,1.1-.5,1.1-1.1s-.5-1.1-1.1-1.1h-1.1v-12.4c0-1.2-1-2.2-2.2-2.2h-2.2v-5.2c0-.6-.2-1.2-.7-1.6l-1.7-1.7c-.9-.9-2.3-.9-3.2,0l-1.7,1.7c-.4.4-.7,1-.7,1.6v2.9h2.2v-2.9l1.7-1.7,1.7,1.7v3.9c1,.8,1.7,2.1,1.7,3.5v4.5c0,1.4-.7,2.7-1.7,3.5v4.4h-2.5c-.6.7-1.5,1.1-2.5,1.1s-1.9-.4-2.5-1.1h-.3v-.4c-.4-.5-.6-1.2-.6-1.9v-3.4c0-.2,0-.4,0-.6-.2,0-.4,0-.6,0-.4,0-.8,0-1.1-.2v6.4h-2.2v-18c0-.8-.5-1.6-1.1-1.9v-6.4l3.4-1.7v11.7c.7-.4,1.4-.6,2.2-.6v-11.1ZM60.9,41.6c-1.2,0-2.2,1-2.2,2.2v2.8c0,.6.5,1.1,1.1,1.1s1.1-.5,1.1-1.1v-5.1ZM68.2,48.4v-2.2h-1.1c-.6,0-1.1.5-1.1,1.1s.5,1.1,1.1,1.1h1.1ZM65.4,41.6h-2.2v1.1c0,.6.5,1.1,1.1,1.1s1.1-.5,1.1-1.1v-1.1ZM71,56.2v-12.4h2.2v12.4h-2.2ZM54.1,38.2v18h-1.7v-10.1c0-1-.7-1.9-1.7-2.2v-5.7h3.4ZM47.9,46.1h2.2v10.1h-2.2v-10.1Z"/>
      <circle class="st3" cx="63.7" cy="57.4" r="2.2"/>
      <path class="st0" d="M58.6,43.9c0-1.2,1-2.2,2.2-2.2h5.1c1.2,0,2.2,1,2.2,2.2v4.5c0,1.2-1,2.2-2.2,2.2s-1.1.5-1.1,1.1v2.2c0,.6-.5,1.1-1.1,1.1s-1.1-.5-1.1-1.1v-3.4c0-1.2,1-2.2,2.2-2.2s1.1-.5,1.1-1.1v-2.2c0-.6-.5-1.1-1.1-1.1h-2.8c-.6,0-1.1.5-1.1,1.1v1.7c0,.6-.5,1.1-1.1,1.1s-1.1-.5-1.1-1.1v-2.8Z"/>
    </g>
    <rect id="logoPlaceholder" x="40" y="23" width="40" height="40" fill="transparent" />
  </g>
  <text class="st6" transform="translate(48.2 73.4)"><tspan x="0" y="0">Ask</tspan></text>
  <text class="st1" transform="translate(37.2 85.4)" id="clientNameText"><tspan x="0" y="0">The City</tspan></text>
</svg>
