<svg width="168" height="169" viewBox="0 0 168 169" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter id="ask-icon-loader-gradient" x="0" y="0.8" width="168" height="168" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4.8"/>
      <feGaussianBlur stdDeviation="12"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9_871"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9_871" result="shape"/>
    </filter>
    <linearGradient id="paint0_linear_9_871" x1="83.9999" y1="28.4" x2="83.9999" y2="131.6" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FF3974"/>
      <stop offset="1" stop-color="#E73067"/>
    </linearGradient>
    <!-- Add pattern for client logo -->
    <pattern id="img1" patternUnits="userSpaceOnUse" width="168" height="168">
      <image x="0" y="0" width="168" height="168" href="" data-client-logo="true" preserveAspectRatio="xMidYMid meet"></image>
    </pattern>
  </defs>
  
  <g filter="url(#ask-icon-loader-gradient)" id="cityLoaderButtonIcon">
    <!-- Background circle -->
    <rect x="24" y="20" width="120" height="120" rx="60" fill="white"/>
    
    <!-- Chat bubble background -->
    <path fill-rule="evenodd" clip-rule="evenodd" d="M83.9999 131.6C112.498 131.6 135.6 108.498 135.6 80C135.6 51.5021 112.498 28.4 83.9999 28.4C55.502 28.4 32.3999 51.5021 32.3999 80C32.3999 90.6546 35.6291 100.555 41.1623 108.776C42.0012 110.022 42.292 111.57 41.8713 113.013L40.1243 119.003C39.8793 119.843 40.5866 120.654 41.4521 120.526L48.272 119.515C49.6474 119.312 51.0359 119.724 52.1288 120.583C60.9034 127.484 71.971 131.6 83.9999 131.6Z" fill="url(#paint0_linear_9_871)"/>
    
    <!-- Question icon (detailed version) -->
    <g id="questionIcon">
      <path opacity="0.75" fill-rule="evenodd" clip-rule="evenodd" d="M84.25 54.1048C84.25 51.3543 81.3555 49.5654 78.8953 50.7954L73.3453 53.5704C72.0918 54.1971 71.3 55.4783 71.3 56.8798V66.8365H67.6C65.5565 66.8365 63.9 68.493 63.9 70.5365V79.7865H62.975C60.9315 79.7865 59.275 81.443 59.275 83.4865V100.136H58.35C57.3283 100.136 56.5 100.965 56.5 101.986C56.5 103.008 57.3283 103.836 58.35 103.836H110.15C111.172 103.836 112 103.008 112 101.986C112 100.965 111.172 100.136 110.15 100.136H108.3V79.7865C108.3 77.743 106.643 76.0865 104.6 76.0865H100.9V67.6028C100.9 66.6215 100.51 65.6804 99.8163 64.9865L97.0413 62.2115C95.5964 60.7665 93.2537 60.7665 91.8087 62.2115L89.0337 64.9865C88.3398 65.6803 87.95 66.6215 87.95 67.6028V72.3864H91.65V67.6028L94.425 64.8278L97.2 67.6028V74.0095C98.8918 75.3656 99.975 77.4495 99.975 79.7864V87.1864C99.975 89.5233 98.8918 91.6072 97.2 92.9634V100.136H93.0118C91.9955 101.272 90.5187 101.986 88.875 101.986C87.2313 101.986 85.7545 101.272 84.7382 100.136H84.25V99.5054C83.6655 98.6264 83.325 97.5712 83.325 96.4364V90.8864C83.325 90.5425 83.3485 90.2041 83.3939 89.8727C83.0714 89.931 82.7393 89.9614 82.4 89.9614C81.7513 89.9614 81.1286 89.8501 80.55 89.6456V100.136L76.85 100.136V84.4114V79.7864V70.5365C76.85 69.1669 76.1059 67.9712 75 67.3314V56.8798L80.55 54.1048V73.3764C81.6384 72.7468 82.9021 72.3864 84.25 72.3864V54.1048ZM84.25 76.0864C82.2065 76.0864 80.55 77.743 80.55 79.7864V84.4114C80.55 85.4332 81.3783 86.2614 82.4 86.2614C83.4217 86.2614 84.25 85.4332 84.25 84.4114V79.7864V76.0864ZM89.812 79.7864H89.788C88.7718 79.78 87.95 78.9542 87.95 77.9365V76.0864H91.65V77.9365C91.65 78.9542 90.8282 79.78 89.812 79.7864ZM96.275 87.1864L94.425 87.1865C93.4033 87.1865 92.575 86.3582 92.575 85.3364C92.575 84.3147 93.4033 83.4865 94.425 83.4865H96.275V87.1864ZM100.9 100.136V85.3364V79.7865H104.6V100.136L100.9 100.136ZM73.15 70.5365V100.136H70.375V83.4865C70.375 81.7624 69.1958 80.3138 67.6 79.903V70.5365H73.15ZM62.975 83.4865H65.75H66.675V100.136H62.975V83.4865Z" fill="white"/>
    </g>

    <!-- Question mark circle -->
    <circle id="questionDot" cx="88.8749" cy="101.986" r="3.7" fill="white" stroke="#E73067" stroke-width="3.7"/>
    
    <!-- Simple question mark -->
    <path id="questionMark" fill-rule="evenodd" clip-rule="evenodd" d="M80.55 79.7864C80.55 77.743 82.2066 76.0864 84.25 76.0864H92.575C94.6185 76.0864 96.275 77.743 96.275 79.7864V87.1864C96.275 89.2299 94.6185 90.8864 92.575 90.8864C91.5533 90.8864 90.725 91.7147 90.725 92.7364V96.4364C90.725 97.4582 89.8968 98.2864 88.875 98.2864C87.8533 98.2864 87.025 97.4582 87.025 96.4364V90.8864C87.025 88.843 88.6816 87.1864 90.725 87.1864C91.7468 87.1864 92.575 86.3582 92.575 85.3364V81.6364C92.575 80.6147 91.7468 79.7864 90.725 79.7864H86.1C85.0783 79.7864 84.25 80.6147 84.25 81.6364V84.4114C84.25 85.4332 83.4218 86.2614 82.4 86.2614C81.3783 86.2614 80.55 85.4332 80.55 84.4114V79.7864Z" fill="white"/>
  </g>
  <rect id="cityLoaderButtonLogoPlaceholder" x="0" y="0" width="168" height="168" fill="transparent" />
</svg>
