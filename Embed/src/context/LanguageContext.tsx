import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { translations, Language, TranslationKey } from '../translations';
import { TranslationService } from '../services/translationService.ts';
import { ClientService } from '../services/clientService';

interface TranslatableContent {
  text: string;
  translations?: Partial<Record<Language, string>>;
  originalLanguage?: Language;
}

interface LanguageContextType {
  currentLanguage: Language;
  setLanguage: (lang: Language) => void;
  t: (key: TranslationKey) => string;
  translateContent: (content: TranslatableContent) => string;
  clientName: string;
}

// Export the client name mapping
export const clientNameMap: Record<string, string> = {
  'emporia': 'Emporia',
  'page': '  the City',
  'york': '  City Hall',
  'mountjackson': 'the Town',
  'loudoun': 'Loudoun',
  'parkcity': 'Park City',
  'roelandpark': 'the City',
  'myrtlebeach':  '  Myrtle',
  'morrisville': 'Morrisville',
  'traversecity': 'the City',
  'myrtlebeachprst': '  Myrtle',
  'tulare': 'City Hall',
  'salisbury': 'Salisbury'
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [currentLanguage, setCurrentLanguage] = useState<Language>('en');
  const [clientName, setClientName] = useState<string>('the City');

  // Initialize client name
  useEffect(() => {
    const initClientName = async () => {
      try {
        // First check URL path for client name
        const pathMatch = window.location.pathname.match(/\/([^\/]+)/);
        const clientNameFromUrl = pathMatch ? pathMatch[1].toLowerCase() : null;
        
        if (clientNameFromUrl && clientNameMap[clientNameFromUrl]) {
          // Use mapped name from URL
          const name = clientNameMap[clientNameFromUrl];
          setClientName(name);
          TranslationService.setClientName(name);
        } else {
          // Default
          setClientName('the City');
          TranslationService.setClientName('the City');
        }
      } catch (error) {
        console.error('Error setting client name:', error);
        setClientName('the City');
        TranslationService.setClientName('the City');
      }
    };

    initClientName();
  }, []);

  const t = (key: TranslationKey): string => {
    return TranslationService.translate(key, currentLanguage);
  };

  const translateContent = (content: TranslatableContent): string => {
    return TranslationService.getTranslatedContent(content, currentLanguage);
  };

  return (
    <LanguageContext.Provider 
      value={{ 
        currentLanguage, 
        setLanguage: setCurrentLanguage,
        t,
        translateContent,
        clientName
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage(): LanguageContextType {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
