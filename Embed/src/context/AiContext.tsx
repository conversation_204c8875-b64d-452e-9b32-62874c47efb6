import React, { createContext, useContext, useState, useCallback } from 'react';
import { getAnswerFromAI } from '../services/aiService';
import { TrackerService } from '../services/tracker.service';
import type { Answer } from '@/types/answer';

interface Source {
  url: string;
  score: number;
}

interface AiAnswer {
  response_text: string;
  sources?: Source[];
}

interface MatchingAnswer {
  repd_answer_id: string;
  // Add any other fields that might be in the response
}

interface ApiResponse {
  response_text: string;
  sources: Source[];
  answer: {
    answer: string;
    answerTokenUsage: number;
    searchTime: number;
    answeringTime: number;
    sources: Source[];
  };
  matchingAnswers: MatchingAnswer[]; // Updated to use the correct structure
}

interface AiContextState {
  answer: AiAnswer;
  matchingAnswers: MatchingAnswer[]; // Updated to use the correct structure
  isLoading: boolean;
  error: string | null;
  fetchAnswer: (question: string, clientId: string, userId: string, isInternal?: boolean) => Promise<void>;
}

export const AiContext = createContext<AiContextState>({} as AiContextState);

export function AiProvider({ children }: { children: React.ReactNode }) {
  const [answer, setAnswer] = useState<AiAnswer>({ response_text: '' });
  const [matchingAnswers, setMatchingAnswers] = useState<MatchingAnswer[]>([]); // Updated to use the correct structure
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const trackerService = new TrackerService();

  const fetchAnswer = useCallback(async (question: string, clientId: string, userId: string, isInternal?: boolean) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getAnswerFromAI(question, clientId, userId, isInternal);
      
      if (response) {
        setAnswer({
          response_text: response.answer.answer,
          sources: response.sources?.filter(source => source.url.startsWith('http'))
        });
        setMatchingAnswers(response.matchingAnswers || []); // Store the matching answer objects

        // Track AI query event with specified format
        trackerService.sendStats({
          event_action: 'repd_morpheus_ai',
          event_category: 'search_query',
          aiSearchText: question,
          aiResponse: response
        });
      } else {
        setAnswer({ response_text: 'No answer available' });
        setMatchingAnswers([]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Failed to get answer:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return (
    <AiContext.Provider value={{ answer, matchingAnswers, isLoading, error, fetchAnswer }}>
      {children}
    </AiContext.Provider>
  );
}

export function useAi() {
  const context = useContext(AiContext);
  if (context === undefined) {
    throw new Error('useAi must be used within an AiProvider');
  }
  return context;
}
