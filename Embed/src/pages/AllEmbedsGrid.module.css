.container {
  padding: 20px;
  max-width: 100%;
}

.title {
  text-align: center;
  margin-top: 40px;
  margin-bottom: 20px;
  font-size: 24px;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.gridItem {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.gridItem:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.clientHeader {
  padding: 10px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.clientHeader::after {
  content: '→';
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #666;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.gridItem:hover .clientHeader::after {
  opacity: 1;
}

.clientHeader h2 {
  margin: 0;
  font-size: 18px;
}

.clientId {
  font-size: 12px;
  color: #666;
  margin-right: 20px;
}

.embedFrame {
  width: 100%;
  height: 600px;
  border: none;
  pointer-events: auto;
}

@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }
}