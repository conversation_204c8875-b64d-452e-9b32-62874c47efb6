import { useEffect, useState } from 'react';
import styles from './IntrospectionPage.module.css';
import { ApiService } from '../services/api.service';
import { clientIdToName, clientNameToId } from '../config/clientLogoConfig';
import { PasswordProtection } from '../components/PasswordProtection/PasswordProtection';
import DOMPurify from 'dompurify';

interface IntrospectionPageProps {
  clientId: string;
}

// Helper function to format dates
const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleString();
  } catch {
    return dateString;
  }
};

// Get client name from clientId using centralized mapping
const getClientName = (clientId: string) => {
  return clientIdToName[clientId] || clientId.toLowerCase();
};

// Get client ID from client name using centralized mapping
const getClientId = (clientIdOrName: string) => {
  // If it's already a number, return it
  if (/^\d+$/.test(clientIdOrName)) {
    return clientIdOrName;
  }

  // Otherwise, map client name to ID
  return clientNameToId[clientIdOrName.toLowerCase()] || clientIdOrName;
};

// Helper function to calculate site link statistics for a site
const calculateSiteStats = (siteId: number, siteLinks: any[]) => {
  // Try both possible field names for site_id (camelCase and snake_case)
  const siteLinksList = siteLinks.filter(link =>
    link.site_id === siteId || link.siteId === siteId
  );
  const total = siteLinksList.length;

  if (total === 0) {
    return {
      percentScraped: '0%',
      completed: 0,
      failed: 0,
      pending: 0,
      ignored: 0
    };
  }

  const completed = siteLinksList.filter(link => link.status === 'scraped').length;
  const failed = siteLinksList.filter(link => link.status === 'failed').length;
  const pending = siteLinksList.filter(link => link.status === 'pending').length;
  const ignored = siteLinksList.filter(link => link.status === 'ignored').length;

  const percentScraped = Math.round((completed / total) * 100);

  return {
    percentScraped: `${percentScraped}%`,
    completed,
    failed,
    pending,
    ignored
  };
};

export function IntrospectionPage({ clientId }: IntrospectionPageProps) {
  const clientName = getClientName(clientId);
  const numericClientId = getClientId(clientId); // Ensure we have the numeric ID
  const [questionEmbeddings, setQuestionEmbeddings] = useState<any[]>([]);
  const [sites, setSites] = useState<any[]>([]);
  const [siteLinks, setSiteLinks] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Pagination state
  const [questionEmbeddingsLimit, setQuestionEmbeddingsLimit] = useState(20);
  const [sitesLimit, setSitesLimit] = useState(20);
  const [siteLinksLimit, setSiteLinksLimit] = useState(20);

  // Load more functions
  const loadMoreQuestionEmbeddings = () => {
    setQuestionEmbeddingsLimit(prev => prev + 50);
  };

  const loadMoreSites = () => {
    setSitesLimit(prev => prev + 50);
  };

  const loadMoreSiteLinks = () => {
    setSiteLinksLimit(prev => prev + 50);
  };

  useEffect(() => {
    const fetchData = async () => {
      const apiService = new ApiService();

      try {
        setLoading(true);

        // Fetch question embeddings
        const questionEmbeddingsResponse = await apiService.get({
          endpoint: 'marvin',
          path: `/question-embeddings/${clientId}`
        });
        setQuestionEmbeddings(questionEmbeddingsResponse.data.data || []);

        // Fetch sites
        const sitesResponse = await apiService.get({
          endpoint: 'marvin',
          path: `/sites/${clientId}`
        });
        setSites(sitesResponse.data.data || []);

        // Fetch site links
        const siteLinksResponse = await apiService.get({
          endpoint: 'marvin',
          path: `/site-links/${clientId}`
        });
        setSiteLinks(siteLinksResponse.data.data || []);

      } catch (error) {
        console.error('Error fetching introspection data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [numericClientId]);

  if (loading) {
    return (
      <PasswordProtection storageKey="adminPassword">
        <div className={styles.container}>
          <div className={styles.header}>
            <h1 className={styles.title}>Loading Introspection Data...</h1>
          </div>
        </div>
      </PasswordProtection>
    );
  }

  return (
    <PasswordProtection storageKey="adminPassword">
      <div className={styles.container}>
      <div className={styles.header}>
        <h1 className={styles.title}>Introspection - {clientName}</h1>
        <p className={styles.subtitle}>Client ID: {clientId}</p>
      </div>

      <div className={styles.content}>
        {/* Embed Section */}
        <div className={styles.embedSection}>
          <h2 className={styles.sectionTitle}>Live Embed</h2>
          <div className={styles.embedContainer}>
            <iframe
              src={`/${clientName}`}
              title={`${clientName} Embed`}
              className={styles.embedFrame}
            />
          </div>
        </div>

        {/* Sites Table */}
        <div className={styles.tableSection}>
          <h2 className={styles.sectionTitle}>Sites</h2>
          <div className={styles.tableContainer}>
            <table className={styles.table}>
              <thead className={styles.tableHeader}>
                <tr>
                  <th className={styles.tableHeaderCell}>Name</th>
                  <th className={styles.tableHeaderCell}>URL</th>
                  <th className={styles.tableHeaderCell}>Last Scraped</th>
                  <th className={styles.tableHeaderCell}>% Scraped</th>
                  <th className={styles.tableHeaderCell}># Completed</th>
                  <th className={styles.tableHeaderCell}># Failed</th>
                  <th className={styles.tableHeaderCell}># Pending</th>
                  <th className={styles.tableHeaderCell}># Ignored</th>
                </tr>
              </thead>
              <tbody>
                {sites.slice(0, sitesLimit).map((site: any) => {
                  const stats = calculateSiteStats(site.id, siteLinks);
                  return (
                    <tr key={site.id} className={styles.tableRow}>
                      <td className={styles.tableCell}>{site.name || 'N/A'}</td>
                      <td className={styles.tableCell}>
                        <a href={site.url} target="_blank" rel="noopener noreferrer" className={styles.link}>
                          {site.url}
                        </a>
                      </td>
                      <td className={styles.tableCell}>{formatDate(site.last_scraped_at)}</td>
                      <td className={styles.tableCell}>{stats.percentScraped}</td>
                      <td className={styles.tableCell}>{stats.completed}</td>
                      <td className={styles.tableCell}>{stats.failed}</td>
                      <td className={styles.tableCell}>{stats.pending}</td>
                      <td className={styles.tableCell}>{stats.ignored}</td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
            {sites.length > sitesLimit && (
              <div className={styles.loadMoreContainer}>
                <button
                  onClick={loadMoreSites}
                  className={styles.loadMoreButton}
                >
                  Load More ({sites.length - sitesLimit} remaining)
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Questions & Answers Table */}
        <div className={styles.tableSection}>
          <h2 className={styles.sectionTitle}>Questions & Answers</h2>
          <div className={styles.tableContainer}>
            <table className={styles.table}>
              <thead className={styles.tableHeader}>
                <tr>
                  <th className={styles.tableHeaderCell}>Created At</th>
                  <th className={styles.tableHeaderCell}>Question</th>
                  <th className={styles.tableHeaderCell}>Answer</th>
                  <th className={styles.tableHeaderCell}>Search Time (ms)</th>
                </tr>
              </thead>
              <tbody>
                {questionEmbeddings.slice(0, questionEmbeddingsLimit).map((item: any) => (
                  <tr key={item.id} className={styles.tableRow}>
                    <td className={styles.tableCell}>{formatDate(item.created_at)}</td>
                    <td className={styles.tableCell}>{item.question || 'N/A'}</td>
                    <td className={styles.tableCell}>
                      {item.answer ? (
                        <div dangerouslySetInnerHTML={{ 
                          __html: DOMPurify.sanitize(item.answer) 
                        }} />
                      ) : 'N/A'}
                    </td>
                    <td className={styles.tableCell}>{item.search_time || 'N/A'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
            {questionEmbeddings.length > questionEmbeddingsLimit && (
              <div className={styles.loadMoreContainer}>
                <button
                  onClick={loadMoreQuestionEmbeddings}
                  className={styles.loadMoreButton}
                >
                  Load More ({questionEmbeddings.length - questionEmbeddingsLimit} remaining)
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Site Links Table */}
        <div className={styles.tableSection}>
          <h2 className={styles.sectionTitle}>Site Links</h2>
          <div className={styles.tableContainer}>
            <table className={styles.table}>
              <thead className={styles.tableHeader}>
                <tr>
                  <th className={styles.tableHeaderCell}>URL</th>
                  <th className={styles.tableHeaderCell}>Status</th>
                  <th className={styles.tableHeaderCell}>Last Scraped</th>
                </tr>
              </thead>
              <tbody>
                {siteLinks.slice(0, siteLinksLimit).map((link: any) => (
                  <tr key={link.id} className={styles.tableRow}>
                    <td className={styles.tableCell}>
                      <a href={link.url} target="_blank" rel="noopener noreferrer" className={styles.link}>
                        {link.url}
                      </a>
                    </td>
                    <td className={styles.tableCell}>
                      <span className={`${styles.statusBadge} ${
                        link.status === 'scraped' ? styles.statusActive :
                        link.status === 'pending' ? styles.statusPending :
                        styles.statusInactive
                      }`}>
                        {link.status}
                      </span>
                    </td>
                    <td className={styles.tableCell}>{formatDate(link.last_scraped_at)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
            {siteLinks.length > siteLinksLimit && (
              <div className={styles.loadMoreContainer}>
                <button
                  onClick={loadMoreSiteLinks}
                  className={styles.loadMoreButton}
                >
                  Load More ({siteLinks.length - siteLinksLimit} remaining)
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
    </PasswordProtection>
  );
}
