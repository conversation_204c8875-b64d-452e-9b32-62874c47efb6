.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  font-family: 'Plus Jakarta Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Embed Section */
.embedSection {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.embedContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.embedFrame {
  width: 510px;
  height: 720px;
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sectionTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1.5rem 0;
  border-bottom: 2px solid #667eea;
  padding-bottom: 0.5rem;
}

/* Table Sections */
.tableSection {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.tableContainer {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  max-height: 700px;
  overflow: scroll;
}

.table {
  position: relative;
  width: 100%;
  border-collapse: collapse;
  background: white;

}

.tableHeader {
  position: sticky;
  top: 0;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.tableHeaderCell {
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.tableRow {
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s;
}

.tableRow:hover {
  background: #f8fafc;
}

.tableCell {
  padding: 12px 16px;
  color: #374151;
  font-size: 0.875rem;
  vertical-align: top;
  max-width: 600px;
}

.link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  word-break: break-all;
}

.link:hover {
  text-decoration: underline;
}

.statusBadge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.statusActive {
  background: #dcfce7;
  color: #166534;
}

.statusInactive {
  background: #fee2e2;
  color: #991b1b;
}

.statusPending {
  background: #fef3c7;
  color: #92400e;
}

/* Load More Button */
.loadMoreContainer {
  display: flex;
  justify-content: center;
  padding: 1rem;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.loadMoreButton {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  font-family: inherit;
}

.loadMoreButton:hover {
  background: #5a67d8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.loadMoreButton:active {
  transform: translateY(0);
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .title {
    font-size: 2rem;
  }

  .content {
    max-width: 100%;
  }

  .embedSection,
  .tableSection {
    padding: 1.5rem;
  }

  .embedFrame {
    width: 100%;
    max-width: 350px;
    height: 500px;
  }

  .tableHeaderCell,
  .tableCell {
    padding: 8px 12px;
    font-size: 0.8rem;
  }

  .link {
    word-break: break-all;
    font-size: 0.75rem;
  }

  .loadMoreButton {
    padding: 0.625rem 1.25rem;
    font-size: 0.8rem;
  }
}
