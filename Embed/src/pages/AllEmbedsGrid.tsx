import { useEffect, useState } from 'react';
import styles from './AllEmbedsGrid.module.css';
import introspectionStyles from './IntrospectionPage.module.css';
import { clientNameToId, clientIdToName } from '../config/clientLogoConfig';
import { PasswordProtection } from '../components/PasswordProtection/PasswordProtection';
import { ApiService } from '../services/api.service';

interface ClientInfo {
  id: string;
  name: string;
  subdomain: string;
}

// Helper function to format dates
const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleString();
  } catch {
    return dateString;
  }
};

// Helper function to calculate site link statistics for a site
const calculateSiteStats = (siteId: number, siteLinksMap: Record<number, any[]>) => {
  const siteLinksList = siteLinksMap[siteId] || [];
  const total = siteLinksList.length;

  if (total === 0) {
    return {
      percentScraped: '0%',
      completed: 0,
      failed: 0,
      pending: 0,
      ignored: 0
    };
  }

  const completed = siteLinksList.filter(link => link.status === 'scraped').length;
  const failed = siteLinksList.filter(link => link.status === 'failed').length;
  const pending = siteLinksList.filter(link => link.status === 'pending').length;
  const ignored = siteLinksList.filter(link => link.status === 'ignored').length;

  const percentScraped = Math.round((completed / total) * 100);

  return {
    percentScraped: `${percentScraped}%`,
    completed,
    failed,
    pending,
    ignored
  };
};

export function AllEmbedsGrid() {
  const [clients, setClients] = useState<ClientInfo[]>([]);
  const [sites, setSites] = useState<any[]>([]);
  const [siteLinksMap, setSiteLinksMap] = useState<Record<number, any[]>>({});
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  // Pagination state for sites
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreSites, setHasMoreSites] = useState(true);
  const sitesPerPage = 5;

  // Load more function for sites
  const loadMoreSites = async () => {
    if (loadingMore || !hasMoreSites) return;

    setLoadingMore(true);
    const apiService = new ApiService();

    try {
      const nextPage = currentPage + 1;

      // Fetch next page of sites
      const sitesResponse = await apiService.get({
        endpoint: 'marvin',
        path: `/sites?page=${nextPage}&limit=${sitesPerPage}`
      });

      const newSites = sitesResponse.data.data || [];
      const pagination = sitesResponse.data.pagination;

      if (newSites.length > 0) {
        // Fetch site links for each new site
        const newSiteLinksMap: Record<number, any[]> = {};

        for (const site of newSites) {
          try {
            const siteLinksResponse = await apiService.get({
              endpoint: 'marvin',
              path: `/site-links?site_id=${site.id}`
            });
            newSiteLinksMap[site.id] = siteLinksResponse.data.data || [];
          } catch (error) {
            console.error(`Error fetching site links for site ${site.id}:`, error);
            newSiteLinksMap[site.id] = [];
          }
        }

        // Update state
        setSites(prev => [...prev, ...newSites]);
        setSiteLinksMap(prev => ({ ...prev, ...newSiteLinksMap }));
        setCurrentPage(nextPage);
        setHasMoreSites(pagination.hasNextPage);
      } else {
        setHasMoreSites(false);
      }
    } catch (error) {
      console.error('Error loading more sites:', error);
    } finally {
      setLoadingMore(false);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      const apiService = new ApiService();

      try {
        setLoading(true);

        // Convert centralized mapping to array of client info objects
        const clientsArray = Object.entries(clientNameToId).map(([subdomain, id]) => ({
          id,
          name: subdomain.charAt(0).toUpperCase() + subdomain.slice(1),
          subdomain
        }));
        setClients(clientsArray);

        // Fetch first page of sites (5 sites)
        const sitesResponse = await apiService.get({
          endpoint: 'marvin',
          path: `/sites?page=1&limit=${sitesPerPage}`
        });

        const initialSites = sitesResponse.data.data || [];
        const pagination = sitesResponse.data.pagination;

        setSites(initialSites);
        setHasMoreSites(pagination.hasNextPage);

        // Fetch site links for each initial site
        const initialSiteLinksMap: Record<number, any[]> = {};

        for (const site of initialSites) {
          try {
            const siteLinksResponse = await apiService.get({
              endpoint: 'marvin',
              path: `/site-links?site_id=${site.id}`
            });
            initialSiteLinksMap[site.id] = siteLinksResponse.data.data || [];
          } catch (error) {
            console.error(`Error fetching site links for site ${site.id}:`, error);
            initialSiteLinksMap[site.id] = [];
          }
        }

        setSiteLinksMap(initialSiteLinksMap);

      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleGridItemClick = (client: ClientInfo, event: React.MouseEvent) => {
    // Don't navigate if the click was on the iframe
    if ((event.target as HTMLElement).tagName === 'IFRAME') {
      return;
    }

    // Navigate to introspection page
    window.location.href = `/introspection/${client.subdomain}`;
  };

  if (loading) {
    return (
      <PasswordProtection storageKey="adminPassword">
        <div className={styles.container}>
          <h1 className={styles.title}>Loading...</h1>
        </div>
      </PasswordProtection>
    );
  }

  return (
    <PasswordProtection storageKey="adminPassword">
      <div className={styles.container}>
        {/* Sites Table */}
        <div className={introspectionStyles.tableSection} style={{ marginBottom: '2rem' }}>
          <h2 className={introspectionStyles.sectionTitle}>All Sites</h2>
          <div className={introspectionStyles.tableContainer}>
            <table className={introspectionStyles.table}>
              <thead className={introspectionStyles.tableHeader}>
                <tr>
                  <th className={introspectionStyles.tableHeaderCell}>Client ID</th>
                  <th className={introspectionStyles.tableHeaderCell}>Name</th>
                  <th className={introspectionStyles.tableHeaderCell}>URL</th>
                  <th className={introspectionStyles.tableHeaderCell}>Last Scraped</th>
                  <th className={introspectionStyles.tableHeaderCell}>% Scraped</th>
                  <th className={introspectionStyles.tableHeaderCell}># Completed</th>
                  <th className={introspectionStyles.tableHeaderCell}># Failed</th>
                  <th className={introspectionStyles.tableHeaderCell}># Pending</th>
                  <th className={introspectionStyles.tableHeaderCell}># Ignored</th>
                </tr>
              </thead>
              <tbody>
                {sites.map((site: any) => {
                  const stats = calculateSiteStats(site.id, siteLinksMap);
                  const clientName = clientIdToName[site.client_id] || `Client ${site.client_id}`;
                  return (
                    <tr key={site.id} className={introspectionStyles.tableRow}>
                      <td className={introspectionStyles.tableCell}>
                        <span style={{ fontWeight: 'bold' }}>{site.client_id}</span>
                        <br />
                        <span style={{ fontSize: '0.75rem', color: '#666' }}>{clientName}</span>
                      </td>
                      <td className={introspectionStyles.tableCell}>{site.name || 'N/A'}</td>
                      <td className={introspectionStyles.tableCell}>
                        <a href={site.url} target="_blank" rel="noopener noreferrer" className={introspectionStyles.link}>
                          {site.url}
                        </a>
                      </td>
                      <td className={introspectionStyles.tableCell}>{formatDate(site.last_scraped_at)}</td>
                      <td className={introspectionStyles.tableCell}>{stats.percentScraped}</td>
                      <td className={introspectionStyles.tableCell}>{stats.completed}</td>
                      <td className={introspectionStyles.tableCell}>{stats.failed}</td>
                      <td className={introspectionStyles.tableCell}>{stats.pending}</td>
                      <td className={introspectionStyles.tableCell}>{stats.ignored}</td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
            {hasMoreSites && (
              <div className={introspectionStyles.loadMoreContainer}>
                <button
                  onClick={loadMoreSites}
                  className={introspectionStyles.loadMoreButton}
                  disabled={loadingMore}
                >
                  {loadingMore ? 'Loading...' : `Load 5 More Sites`}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Client Embeds Grid */}
        <div className={styles.grid}>
          {clients.map(client => (
            <div
              key={client.id}
              className={styles.gridItem}
              onClick={(e) => handleGridItemClick(client, e)}
            >
              <div className={styles.clientHeader}>
                <h2>{client.name}</h2>
                <span className={styles.clientId}>ID: {client.id}</span>
              </div>
              <iframe
                src={`/${client.subdomain}`}
                title={`${client.name} Embed`}
                className={styles.embedFrame}
              />
            </div>
          ))}
        </div>
      </div>
    </PasswordProtection>
  );
}

export default AllEmbedsGrid;
