/**
 * Dynamic Colors CSS
 * This file contains global styles that use CSS variables for dynamic client colors
 * These styles replace the need for static client override CSS files
 */

/* Progress Bar Styles - Dynamic */
.newProgressBar,
.QuestionInput-module__newProgressBar__XXXXX,
.loadingContainer .newProgressBar,
div[class*="newProgressBar"] {
  background-color: var(--progress-bar-color, #e94e77) !important;
}

/* Progress Animation - Dynamic */
@keyframes progressAnimation {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
    background-color: var(--progress-bar-color, #e94e77) !important;
  }
}

/* Background Container Styles - Dynamic */
.innerContainer,
.container {
  background: var(--background-gradient, linear-gradient(105deg, #30466F 0.25%, #1E293D 100.25%)) !important;
}

/* Button Styles - Dynamic */
.submitButton,
button[type="submit"],
.primaryButton {
  background-color: var(--submit-button-color, #2760CF) !important;
  color: var(--text-on-primary, #FFFFFF) !important;
}

.submitButton:hover,
button[type="submit"]:hover,
.primaryButton:hover {
  background-color: var(--submit-button-hover, #3A70DF) !important;
}

.submitButton:active,
button[type="submit"]:active,
.primaryButton:active {
  background-color: var(--submit-button-active, #1E50BF) !important;
}

/* Interactive Element Styles - Dynamic */
.interactive:hover,
.clickable:hover,
.button:hover {
  background-color: var(--hover-color, #3A70DF) !important;
}

.interactive:active,
.clickable:active,
.button:active {
  background-color: var(--active-color, #1E50BF) !important;
}

.interactive:focus,
.clickable:focus,
.button:focus,
input:focus,
textarea:focus {
  box-shadow: 0 0 0 2px var(--focus-color, #4F85EF) !important;
}

/* Error and Success States - Dynamic */
.error,
.errorText,
.errorMessage {
  color: var(--error-color, #DC3545) !important;
}

.success,
.successText,
.successMessage {
  color: var(--success-color, #29B36E) !important;
}

.warning,
.warningText,
.warningMessage {
  color: var(--warning-color, #FFC107) !important;
}

/* Text Colors - Dynamic */
.textOnPrimary {
  color: var(--text-on-primary, #FFFFFF) !important;
}

.textMuted {
  color: var(--text-muted, #8FA4C7) !important;
}

/* Icon Colors - Dynamic */
.primaryIcon {
  color: var(--primary-color, #2760CF) !important;
}

.accentIcon {
  color: var(--accent-color, #E94E77) !important;
}

/* Link Colors - Dynamic */
a,
.link {
  color: var(--primary-color, #2760CF) !important;
}

a:hover,
.link:hover {
  color: var(--hover-color, #3A70DF) !important;
}

/* Badge and Pill Colors - Dynamic */
.badge,
.pill {
  background-color: var(--accent-color, #E94E77) !important;
  color: var(--text-on-primary, #FFFFFF) !important;
}

.newQuestionBadge {
  background-color: var(--new-question-color, #29B36E) !important;
  color: var(--text-on-primary, #FFFFFF) !important;
}

.plusAskPill {
  background-color: var(--plus-ask-pill-color, #29B36E) !important;
  color: var(--text-on-primary, #FFFFFF) !important;
}

.openQaBadge {
  background-color: var(--open-qa-color, #0E509B) !important;
  color: var(--text-on-primary, #FFFFFF) !important;
}

/* Gradient Backgrounds - Dynamic */
.gradientBackground {
  background: var(--background-gradient, linear-gradient(105deg, #30466F 0.25%, #1E293D 100.25%)) !important;
}

.popularAnswersGradient {
  background: var(--popular-answers-gradient, linear-gradient(180deg, rgba(30, 41, 61, 0.75) 0%, #1E293D 100%)) !important;
}

/* Skeleton Loading - Dynamic */
.skeleton,
.skeletonCard {
  background: var(--skeleton-gradient, linear-gradient(to right, #2a3a5a 8%, #3a4a6a 18%, #2a3a5a 33%)) !important;
}

/* Border Colors - Dynamic */
.primaryBorder {
  border-color: var(--primary-color, #2760CF) !important;
}

.accentBorder {
  border-color: var(--accent-color, #E94E77) !important;
}

/* Shadow Colors - Dynamic */
.primaryShadow {
  box-shadow: 0 4px 6px var(--primary-color, rgba(39, 96, 207, 0.1)) !important;
}

.accentShadow {
  box-shadow: 0 4px 6px var(--accent-color, rgba(233, 78, 119, 0.1)) !important;
}

/* Client-specific overrides can still be applied via body classes */
/* These will override the CSS variables for specific clients */

/* Example: Emporia specific overrides */
body.client-283 {
  --accent-color: #0E4EA0;
  --progress-bar-color: #fca127AA;
}

/* Example: Page specific overrides */
body.client-295 {
  --accent-color: #85d0d3;
  --progress-bar-color: #85d0d3;
}

/* Add more client-specific overrides as needed */
/* The ClientColorService will handle setting these dynamically */
