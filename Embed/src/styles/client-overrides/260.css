/* Client override for <PERSON> (ID: 260) */

/* Change background gradient */
.innerContainer,
.container {
  background: linear-gradient(105deg, #248384 0.25%, #248384 100.25%) !important;
}

/* Change accent color */
:root {
  --accent-color: #372f54 !important;
}

/* Override progress bar color */
.newProgressBar {
  background-color: #372f54 !important;
}

/* Target the loading bar more specifically */
.QuestionInput-module__newProgressBar__XXXXX,
.loadingContainer .newProgressBar,
div[class*="newProgressBar"] {
  background-color: #372f54 !important;
}

/* Ensure any animation gradients also use the correct color */
@keyframes progressAnimation {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
    background-color: #372f54 !important;
  }
}

/* Override the city question icon - use client logo with transparent background */
.CityIllustration-module__icon__lf8Nv,
img[alt="City Question Icon"],
img[alt="Denton Logo"],
#cityQuestionIcon {
  width: 250px !important;
  height: auto !important;
  max-width: 350px !important;
  max-height: 100px !important;
  background-color: transparent !important;
  border-radius: 10px !important;
}

/* Custom logo for question input */
.sendButton {
  background-image: url('https://files.repd.us/manual/Denton-Logo.png') !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  max-width: 30px !important;
  max-height: 30px !important;
}

/* Hide the default send icon */
.sendIcon {
  display: none !important;
}

/* Hide the white circle background for logo */
#logoBackground {
  display: none !important;
}

/* For the floating icon - same ratio but no circle */
.client-260 .QuestionInput-module__headerContainer__6-Q59 img {
  background-color: transparent !important;
  border-radius: 0 !important;
}

/* Make skyline white */
.CityIllustration-module__skyline__KoPCU,
img[alt="City Skyline"] {
  filter: brightness(0) invert(1) !important;
  opacity: 0 !important;
}

/* Comprehensive SVG color overrides */
/* Target all common pink hex codes used in SVGs */
svg path[fill="#E94E77"],
svg path[fill="#EA3269"],
svg path[fill="#E83168"],
svg path[fill="#E73067"],
svg path[fill="#FF3974"],
svg path[fill="#F64C87"],
svg path[fill="#FF4081"],
svg path[fill="#FF1493"],
svg path[fill="#FF69B4"] {
  fill: #372f54 !important;
}

svg path[stroke="#E94E77"],
svg path[stroke="#EA3269"],
svg path[stroke="#E83168"],
svg path[stroke="#E73067"],
svg path[stroke="#FF3974"],
svg path[stroke="#F64C87"],
svg path[stroke="#FF4081"],
svg path[stroke="#FF1493"],
svg path[stroke="#FF69B4"] {
  stroke: #372f54 !important;
}

/* Target any elements with style attribute containing pink colors */
svg [style*="#E94E77"],
svg [style*="#EA3269"],
svg [style*="#E83168"],
svg [style*="#E73067"],
svg [style*="rgb(233, 78, 119)"],
svg [style*="rgb(234, 50, 105)"],
svg [style*="rgb(232, 49, 104)"],
svg [style*="rgb(231, 48, 103)"] {
  fill: #372f54 !important;
  stroke: #372f54 !important;
}

/* Target gradient stops in SVGs */
svg stop[stop-color="#FF3974"],
svg stop[stop-color="#E73067"],
svg stop[stop-color="#E94E77"],
svg stop[stop-color="#EA3269"],
svg stop[stop-color="#E83168"] {
  stop-color: #372f54 !important;
}

/* Target the ask-the-city-button.svg specifically */
#AskTheCityButton linearGradient stop[offset="0"],
#AskTheCityButton stop[stop-color="#ff3974"] {
  stop-color: #248384 !important;
}

#AskTheCityButton linearGradient stop[offset="1"],
#AskTheCityButton stop[stop-color="#e73067"] {
  stop-color: #1a6566 !important; /* Darker shade of the main color */
}

#AskTheCityButton .st3 {
  stroke: #372f54 !important;
}

/* Override focus outline color */
.input:focus {
  box-shadow: 0 0 0 2px #372f54 !important;
}

/* "See More Videos" button styling */
.seeMoreButton,
#seeMoreButton {
  background-color: #417F81 !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 8px 16px !important;
  font-weight: 500 !important;
  transition: background-color 0.2s ease !important;
}

/* Hover state for See More button */
#seeMoreButton:hover,
button[class*="seeMoreButton"]:hover {
  background-color: #4a8e90 !important; /* Slightly lighter on hover */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* Active state for See More button */
#seeMoreButton:active,
button[class*="seeMoreButton"]:active {
  background-color: #376b6d !important; /* Slightly darker when clicked */
}

/* Ensure the Ask text is properly styled for accessibility */
#AskTheCityButton text.st6 tspan {
  fill: #ffffff !important;
  font-size: 14px !important;
}

/* Ensure the "The City" text has good contrast */
#AskTheCityButton text.st1 tspan {
  fill: #ffffff !important;
  font-weight: bold !important;
}

/* Show client logo and hide default icon for Denton client */
#askButtonIcon,
#cityLoaderButtonIcon {
  display: none;
}

/* SVG Logo Definition for Denton */
svg defs {
  /* This will be populated dynamically */
}

/* Update SVG logo placeholder to show actual logo */
#logoPlaceholder,
#cityLoaderButtonLogoPlaceholder {
  display: block !important;
  background-image: url('https://files.repd.us/manual/Denton-Logo.png') !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  fill: transparent !important;
}

/* Alternative approach for SVG image elements */
svg image[data-client-logo="true"],
#logoPlaceholder image,
#cityLoaderButtonLogoPlaceholder image {
  href: 'https://files.repd.us/manual/Denton-Logo.png' !important;
  width: 40 !important;
  height: 40 !important;
}

/* Hide the minimized button for Denton client */
.client-260 .QuestionInput-module__headerContainer__6-Q59 {
  display: none !important;
}

/* Minified button customizations */
.MinimizedButton-module__button,
.client-260 .MinimizedButton-module__button {
  background-color: #417F81 !important; /* New button color */
  width: 10rem !important;
  height: 10rem !important;
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  border-radius: 50% !important; /* Ensure it's circular */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important; /* Add shadow for depth */
}

.MinimizedButton-module__button img,
.MinimizedButton-module__image,
.client-260 .MinimizedButton-module__button img {
  width: 80% !important;
  height: auto !important;
  margin-top: 15px !important; /* Move down */
  object-fit: contain !important;
}

/* Add hover effect to minified button */
.MinimizedButton-module__button:hover,
.client-260 .MinimizedButton-module__button:hover {
  background-color: #4a8e90 !important; /* Slightly lighter on hover */
  transform: translateY(-5px) !important; /* Move up slightly on hover */
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3) !important; /* Enhanced shadow on hover */
}
