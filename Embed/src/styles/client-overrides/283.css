/* Client override for Emporia (ID: 283) */

/* Change background gradient */
.innerContainer,
.container {
  background: linear-gradient(105deg, #042454 0.25%, #042454 100.25%) !important;
}

/* Change accent color from pink to orange */
:root {
  --accent-color: #0E4EA0 !important;
}

/* Override progress bar color */
.newProgressBar {
  background-color: #fca127AA !important;
}

/* Target the loading bar more specifically */
.QuestionInput-module__newProgressBar__XXXXX,
.loadingContainer .newProgressBar,
div[class*="newProgressBar"] {
  background-color: #fca127AA !important;
}

/* Ensure any animation gradients also use the correct color */
@keyframes progressAnimation {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
    background-color: #fca127AA !important;
  }
}

/* Override focus outline color */
.input:focus {
  box-shadow: 0 0 0 2px #fca127 !important;
}

/* Custom logo for question input */
.sendButton {
  background-image: url('https://files.repd.us/manual/Emporia-Logo-White.png') !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  max-width: 30px !important;
  max-height: 30px !important;
}

/* Hide the default send icon */
.sendIcon {
  display: none !important;
}

/* Override the city question icon - use white Emporia logo with transparent background */
.CityIllustration-module__icon__lf8Nv,
img[alt="City Question Icon"],
img[alt="Emporia Logo"],
#cityQuestionIcon {
  filter: brightness(0) invert(1) !important; /* Make the logo white */
  width: auto !important;
  height: auto !important;
  max-width: 350px !important;
  max-height: 100px !important;
}

/* Make skyline white */
.CityIllustration-module__skyline__KoPCU,
img[alt="City Skyline"] {
  filter: brightness(0) invert(1) !important;
  /* opacity: 0.7 !important; */
  opacity: 0 !important;
}

/* Override SVG colors in the city question icon */
svg circle[fill="white"][stroke="#E73067"],
svg path[fill="#E73067"],
svg path[stroke="#E73067"] {
  stroke: #fca127 !important;
  fill: #fca127 !important;
}

/* Override ask-the-city-button.svg colors for Emporia */
#AskTheCityButton linearGradient stop {
  stop-color: #042454 !important;
}

#AskTheCityButton .st3 {
  stroke: #fca127 !important;
}

/* Ensure the Ask text is properly styled for accessibility */
#AskTheCityButton text.st6 tspan {
  fill: #ffffff !important;
  font-size: 14px !important;
}

/* Ensure the "The City" text has good contrast */
#AskTheCityButton text.st1 tspan {
  fill: #ffffff !important;
  font-weight: bold !important;
}

/* Comprehensive SVG color overrides for Emporia */
/* Target all common pink hex codes used in SVGs */
svg path[fill="#E94E77"],
svg path[fill="#EA3269"],
svg path[fill="#E83168"],
svg path[fill="#E73067"],
svg path[fill="#FF3974"],
svg path[fill="#F64C87"],
svg path[fill="#FF4081"],
svg path[fill="#FF1493"],
svg path[fill="#FF69B4"],
svg path[fill="#fca127"] {
  fill: #0E4EA0 !important; /* Emporia blue */
}

svg path[stroke="#E94E77"],
svg path[stroke="#EA3269"],
svg path[stroke="#E83168"],
svg path[stroke="#E73067"],
svg path[stroke="#FF3974"],
svg path[stroke="#F64C87"],
svg path[stroke="#FF4081"],
svg path[stroke="#FF1493"],
svg path[stroke="#FF69B4"],
svg path[stroke="#fca127"] {
  stroke: #0E4EA0 !important;
}

/* Target all SVG elements with opacity 0.2 and pink fill */
svg path[fill="#EA3269"][fill-opacity="0.2"],
svg path[fill-opacity="0.2"][fill="#EA3269"],
svg path[fill="#fca127"][fill-opacity="0.2"],
svg path[fill-opacity="0.2"][fill="#fca127"] {
  fill: #0E4EA0 !important;
  fill-opacity: 0.2 !important;
}

/* Target specific elements in the skyline SVG */
.svg-icon.CityIllustration-module__skyline__KoPCU path[fill="#E83168"],
.svg-icon.CityIllustration-module__skyline__KoPCU path[fill="#fca127"] {
  fill: #0E4EA0 !important;
}

/* Target gradient stops in SVGs */
svg stop[stop-color="#FF3974"],
svg stop[stop-color="#E73067"],
svg stop[stop-color="#E94E77"],
svg stop[stop-color="#EA3269"],
svg stop[stop-color="#E83168"],
svg stop[stop-color="#fca127"],
svg stop[stop-color="#e89016"] {
  stop-color: #0E4EA0 !important;
}

/* Target any elements with class names that might indicate pink */
svg .pink,
svg .accent,
svg .highlight {
  fill: #0E4EA0 !important;
  stroke: #0E4EA0 !important;
}

/* Target any remaining pink-ish RGB values */
svg [fill="rgb(233, 78, 119)"],
svg [fill="rgb(234, 50, 105)"],
svg [fill="rgb(232, 49, 104)"],
svg [fill="rgb(231, 48, 103)"],
svg [fill="rgb(252, 161, 39)"] {
  fill: #0E4EA0 !important;
}

svg [stroke="rgb(233, 78, 119)"],
svg [stroke="rgb(234, 50, 105)"],
svg [stroke="rgb(232, 49, 104)"],
svg [stroke="rgb(231, 48, 103)"],
svg [stroke="rgb(252, 161, 39)"] {
  stroke: #0E4EA0 !important;
}

/* Target any elements with style attribute containing pink colors */
svg [style*="#E94E77"],
svg [style*="#EA3269"],
svg [style*="#E83168"],
svg [style*="#E73067"],
svg [style*="#fca127"],
svg [style*="#e89016"],
svg [style*="rgb(233, 78, 119)"],
svg [style*="rgb(234, 50, 105)"],
svg [style*="rgb(232, 49, 104)"],
svg [style*="rgb(231, 48, 103)"],
svg [style*="rgb(252, 161, 39)"] {
  fill: #0E4EA0 !important;
  stroke: #0E4EA0 !important;
}

/* Target the ask-the-city-button.svg specifically */
#AskTheCityButton linearGradient stop[offset="0"],
#AskTheCityButton stop[stop-color="#ff3974"],
#AskTheCityButton stop[stop-color="#fca127"] {
  stop-color: #0E4EA0 !important;
}

#AskTheCityButton linearGradient stop[offset="1"],
#AskTheCityButton stop[stop-color="#e73067"],
#AskTheCityButton stop[stop-color="#e89016"] {
  stop-color: #0A3D80 !important;
}

#AskTheCityButton .st3 {
  stroke: #0E4EA0 !important;
}

/* Show client logo and hide default icon for Emporia client */
#askButtonIcon,
#cityLoaderButtonIcon {
  display: none;
}

#logoPlaceholder,
#cityLoaderButtonLogoPlaceholder {
  display: block !important;
  fill: url(#img1);
  href: url(#img1);
}

/* Hide the minimized button for Emporia client */
.client-283 .QuestionInput-module__headerContainer__6-Q59 {
  display: none !important;
}
