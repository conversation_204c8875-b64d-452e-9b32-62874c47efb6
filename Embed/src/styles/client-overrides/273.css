/* Client override for <PERSON><PERSON><PERSON> (ID: 273) */

/* Change background gradient */
.innerContainer,
.container {
  background: linear-gradient(105deg, #484848 0.25%, #484848 100.25%) !important;
}

/* Change accent color */
:root {
  --accent-color: #e0c386 !important;
}

/* Override progress bar color */
.newProgressBar {
  background-color: #e0c386 !important;
}

/* Target the loading bar more specifically */
.QuestionInput-module__newProgressBar__XXXXX,
.loadingContainer .newProgressBar,
div[class*="newProgressBar"] {
  background-color: #e0c386 !important;
}

/* Ensure any animation gradients also use the correct color */
@keyframes progressAnimation {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
    background-color: #e0c386 !important;
  }
}

/* Override the city question icon - use client logo with transparent background */
.CityIllustration-module__icon__lf8Nv,
img[alt="City Question Icon"],
img[alt="Cullman Logo"],
#cityQuestionIcon {
  width: auto !important;
  height: auto !important;
  max-width: 350px !important;
  max-height: 100px !important;
}

/* Make skyline white */
.CityIllustration-module__skyline__KoPCU,
img[alt="City Skyline"] {
  filter: brightness(0) invert(1) !important;
  opacity: 0 !important;
}

/* Comprehensive SVG color overrides */
/* Target all common pink hex codes used in SVGs */
svg path[fill="#E94E77"],
svg path[fill="#EA3269"],
svg path[fill="#E83168"],
svg path[fill="#E73067"],
svg path[fill="#FF3974"],
svg path[fill="#F64C87"],
svg path[fill="#FF4081"],
svg path[fill="#FF1493"],
svg path[fill="#FF69B4"] {
  fill: #e0c386 !important;
}

svg path[stroke="#E94E77"],
svg path[stroke="#EA3269"],
svg path[stroke="#E83168"],
svg path[stroke="#E73067"],
svg path[stroke="#FF3974"],
svg path[stroke="#F64C87"],
svg path[stroke="#FF4081"],
svg path[stroke="#FF1493"],
svg path[stroke="#FF69B4"] {
  stroke: #e0c386 !important;
}

/* Target any elements with style attribute containing pink colors */
svg [style*="#E94E77"],
svg [style*="#EA3269"],
svg [style*="#E83168"],
svg [style*="#E73067"],
svg [style*="rgb(233, 78, 119)"],
svg [style*="rgb(234, 50, 105)"],
svg [style*="rgb(232, 49, 104)"],
svg [style*="rgb(231, 48, 103)"] {
  fill: #e0c386 !important;
  stroke: #e0c386 !important;
}

/* Target gradient stops in SVGs */
svg stop[stop-color="#FF3974"],
svg stop[stop-color="#E73067"],
svg stop[stop-color="#E94E77"],
svg stop[stop-color="#EA3269"],
svg stop[stop-color="#E83168"] {
  stop-color: #e0c386 !important;
}

/* Target the ask-the-city-button.svg specifically */
#AskTheCityButton linearGradient stop[offset="0"],
#AskTheCityButton stop[stop-color="#ff3974"] {
  stop-color: #484848 !important;
}

#AskTheCityButton linearGradient stop[offset="1"],
#AskTheCityButton stop[stop-color="#e73067"] {
  stop-color: #3a3a3a !important; /* Slightly darker shade of the main color */
}

#AskTheCityButton .st3 {
  stroke: #e0c386 !important;
}

/* Override focus outline color */
.input:focus {
  box-shadow: 0 0 0 2px #e0c386 !important;
}

/* "See More Videos" button styling */
.seeMoreButton,
button[class*="seeMoreButton"] {
  background-color: #e0c386 !important;
  color: black !important; /* Use black text for better contrast on light color */
}

/* Ensure the Ask text is properly styled for accessibility */
#AskTheCityButton text.st6 tspan {
  fill: #ffffff !important;
  font-size: 14px !important;
}

/* Ensure the "The City" text has good contrast */
#AskTheCityButton text.st1 tspan {
  fill: #ffffff !important;
  font-weight: bold !important;
}

/* Show client logo and hide default icon for Cullman client */
#askButtonIcon,
#cityLoaderButtonIcon {
  display: none;
}

/* SVG Logo Definition for Cullman */
svg defs {
  /* This will be populated dynamically */
}

/* Update SVG logo placeholder to show actual logo */
#logoPlaceholder,
#cityLoaderButtonLogoPlaceholder {
  display: block !important;
  background-image: url('https://files.repd.us/manual/Cullman-Logo.png') !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  fill: transparent !important;
}

/* Alternative approach for SVG image elements */
svg image[data-client-logo="true"],
#logoPlaceholder image,
#cityLoaderButtonLogoPlaceholder image {
  href: 'https://files.repd.us/manual/Cullman-Logo.png' !important;
  width: 40 !important;
  height: 40 !important;
}

/* Hide the minimized button for Cullman client */
.client-273 .QuestionInput-module__headerContainer__6-Q59 {
  display: none !important;
}
