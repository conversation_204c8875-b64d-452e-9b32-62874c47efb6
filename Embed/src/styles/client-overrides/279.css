/* Client override for City Of Maple Valley (ID: 279) */

/* Change background gradient - using fourth color since first three are empty */
.innerContainer,
.container {
  background: linear-gradient(105deg, #809cbc 0.25%, #809cbc 100.25%) !important;
}

/* Change accent color */
:root {
  --accent-color: #f6b334 !important;
}

/* Override progress bar color */
.newProgressBar {
  background-color: #f6b334 !important;
}

/* Target the loading bar more specifically */
.QuestionInput-module__newProgressBar__XXXXX,
.loadingContainer .newProgressBar,
div[class*="newProgressBar"] {
  background-color: #f6b334 !important;
}

/* Ensure any animation gradients also use the correct color */
@keyframes progressAnimation {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
    background-color: #f6b334 !important;
  }
}

/* Override the city question icon - use client logo with transparent background */
.CityIllustration-module__icon__lf8Nv,
img[alt="City Question Icon"],
img[alt="City Of Maple Valley Logo"],
#cityQuestionIcon {
  width: auto !important;
  height: auto !important;
  max-width: 350px !important;
  max-height: 160px !important;
}

/* Make skyline white */
.CityIllustration-module__skyline__KoPCU,
img[alt="City Skyline"] {
  filter: brightness(0) invert(1) !important;
  opacity: 0 !important;
}

/* Comprehensive SVG color overrides */
/* Target all common pink hex codes used in SVGs */
svg path[fill="#E94E77"],
svg path[fill="#EA3269"],
svg path[fill="#E83168"],
svg path[fill="#E73067"],
svg path[fill="#FF3974"],
svg path[fill="#F64C87"],
svg path[fill="#FF4081"],
svg path[fill="#FF1493"],
svg path[fill="#FF69B4"] {
  fill: #f6b334 !important;
}

svg path[stroke="#E94E77"],
svg path[stroke="#EA3269"],
svg path[stroke="#E83168"],
svg path[stroke="#E73067"],
svg path[stroke="#FF3974"],
svg path[stroke="#F64C87"],
svg path[stroke="#FF4081"],
svg path[stroke="#FF1493"],
svg path[stroke="#FF69B4"] {
  stroke: #f6b334 !important;
}

/* Target any elements with style attribute containing pink colors */
svg [style*="#E94E77"],
svg [style*="#EA3269"],
svg [style*="#E83168"],
svg [style*="#E73067"],
svg [style*="rgb(233, 78, 119)"],
svg [style*="rgb(234, 50, 105)"],
svg [style*="rgb(232, 49, 104)"],
svg [style*="rgb(231, 48, 103)"] {
  fill: #f6b334 !important;
  stroke: #f6b334 !important;
}

/* Target gradient stops in SVGs */
svg stop[stop-color="#FF3974"],
svg stop[stop-color="#E73067"],
svg stop[stop-color="#E94E77"],
svg stop[stop-color="#EA3269"],
svg stop[stop-color="#E83168"] {
  stop-color: #f6b334 !important;
}

/* Target the ask-the-city-button.svg specifically */
#AskTheCityButton linearGradient stop[offset="0"],
#AskTheCityButton stop[stop-color="#ff3974"] {
  stop-color: #809cbc !important;
}

#AskTheCityButton linearGradient stop[offset="1"],
#AskTheCityButton stop[stop-color="#e73067"] {
  stop-color: #6a83a0 !important; /* Slightly darker shade of the main color */
}

#AskTheCityButton .st3 {
  stroke: #f6b334 !important;
}

/* Override focus outline color */
.input:focus {
  box-shadow: 0 0 0 2px #f6b334 !important;
}

/* "See More Videos" button styling */
.seeMoreButton,
button[class*="seeMoreButton"] {
  background-color: #f6b334 !important;
  color: black !important; /* Use black text for better contrast on yellow */
}

/* Ensure the Ask text is properly styled for accessibility */
#AskTheCityButton text.st6 tspan {
  fill: #ffffff !important;
  font-size: 14px !important;
}

/* Ensure the "The City" text has good contrast */
#AskTheCityButton text.st1 tspan {
  fill: #ffffff !important;
  font-weight: bold !important;
}

/* Show client logo and hide default icon for City Of Maple Valley client */
#askButtonIcon,
#cityLoaderButtonIcon {
  display: none;
}

/* SVG Logo Definition for City Of Maple Valley */
svg defs {
  /* This will be populated dynamically */
}

/* Update SVG logo placeholder to show actual logo */
#logoPlaceholder,
#cityLoaderButtonLogoPlaceholder {
  display: block !important;
  background-image: url('https://files.repd.us/manual/City-Of-Maple-Valley-Logo.png') !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  fill: transparent !important;
}

/* Alternative approach for SVG image elements */
svg image[data-client-logo="true"],
#logoPlaceholder image,
#cityLoaderButtonLogoPlaceholder image {
  href: 'https://files.repd.us/manual/City-Of-Maple-Valley-Logo.png' !important;
  width: 40 !important;
  height: 40 !important;
}

/* Hide the minimized button for City Of Maple Valley client */
.client-279 .QuestionInput-module__headerContainer__6-Q59 {
  display: none !important;
}
