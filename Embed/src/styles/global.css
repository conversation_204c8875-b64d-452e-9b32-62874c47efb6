@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Primary client colors - dynamically set by ClientColorService */
  --primary-color: #2760CF;
  --accent-color: #E94E77;
  --new-question-color: #29B36E;
  --plus-ask-pill-color: #29B36E;
  --open-qa-color: #0E509B;

  /* Background colors and gradients - dynamically set */
  --background-color: #1E293D;
  --background-light: #30466F;
  --background-dark: #1E293D;
  --background-gradient: linear-gradient(105deg, #30466F 0.25%, #1E293D 100.25%);
  --popular-answers-gradient: linear-gradient(180deg, rgba(30, 41, 61, 0.75) 0%, #1E293D 100%);
  --scroll-fade-gradient: linear-gradient(to bottom, #30466F 0%, rgba(48, 70, 111, 0) 100%);
  --scroll-fade-gradient-bottom: linear-gradient(to top, #30466F 0%, rgba(48, 70, 111, 0) 100%);
  --skeleton-gradient: linear-gradient(to right, #2a3a5a 8%, #3a4a6a 18%, #2a3a5a 33%);

  /* Extended color palette - dynamically set */
  --primary-light: #4A7FE7;
  --primary-dark: #1A4A9F;
  --primary-muted: #6B8DD6;
  --primary-accent: #CF6027;

  /* Interactive element colors - dynamically set */
  --hover-color: #3A70DF;
  --active-color: #1E50BF;
  --focus-color: #4F85EF;
  --error-color: #DC3545;
  --success-color: #29B36E;
  --warning-color: #FFC107;

  /* Progress bar colors - dynamically set */
  --progress-bar-color: #E94E77;
  --progress-bar-background: #A73456;

  /* Button colors - dynamically set */
  --button-gradient: linear-gradient(135deg, #3A70DF 0%, #1E50BF 100%);
  --button-gradient-hover: linear-gradient(135deg, #4A80EF 0%, #2E60CF 100%);
  --submit-button-color: #2760CF;
  --submit-button-hover: #3A70DF;
  --submit-button-active: #1E50BF;

  /* Text colors - dynamically set */
  --text-on-primary: #FFFFFF;
  --text-muted: #8FA4C7;

  /* Layout */
  --border-radius: 1rem;

  /* Font size */
  font-size: 16px; /* Base font size - 1rem = 16px by default */
}

body {
  margin: 0;
  font-family: 'Plus Jakarta Sans', system-ui, -apple-system, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Use relative units for font sizes throughout the app */
  font-size: 1rem;
  line-height: 1.5;
}

/* Use rem units for all text elements to ensure they scale with the root font size */
h1, h2, h3, h4, h5, h6, p, span, button, input, textarea, select {
  font-size: inherit;
}

/* Ensure the app container adjusts its height based on font size changes */
#AppContent {
  transition: height 0.3s ease;
}

* {
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}
