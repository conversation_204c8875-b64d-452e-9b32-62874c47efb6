import React, { useState, useEffect, useMemo } from 'react';
import { useLanguage } from '../../context/LanguageContext';
import { QuestionService } from '../../services/questionService';
import type { NewQuestionData } from '../../interfaces/question.interface';
import styles from './PostNewQuestionExtension.module.css';
import { Field, Form, FormikProvider, useFormik } from 'formik';
import * as Yup from 'yup';
import { TrackerService } from '../../services/tracker.service';
import { UserService } from '../../services/userService';
import { Icons } from '../../shared/Icons';
import cn from 'classnames';
import { ClientInterface } from '@/interfaces/client.interface';
import { faCaretDown, faTimes } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

interface PostNewQuestionExtensionProps {
  question: string;
  clientId: string;
  userId?: string;
  onClose?: () => void;
  categories: string[];
  client: ClientInterface;
  onToggleExpand?: (isExpanded: boolean) => void;
}

export function PostNewQuestionExtension({
  question: initialQuestion,
  clientId,
  userId,
  onClose,
  categories,
  client,
  onToggleExpand
}: PostNewQuestionExtensionProps) {
  const { t, currentLanguage } = useLanguage();
  const [isExpanded, setIsExpanded] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCompletionMessage, setShowCompletionMessage] = useState(false);
  const [showAdditionalFields, setShowAdditionalFields] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [isExistingUser, setIsExistingUser] = useState(false);

  const hasCategories = categories.length > 0;
  const allCategories = ['General Inquiry', ...categories];

  const validationSchema = Yup.object().shape({
    question: Yup.string()
      .required(t('pleaseInputQuestion'))
      .max(250, t('questionTooLong')),
    category: hasCategories ? Yup.string().required(t('pleaseSelectCategory')) : Yup.string(),
    email: Yup.string()
      .email(t('pleaseEnterEmail'))
      .required(t('pleaseEnterEmail')),
    firstName: showAdditionalFields ? Yup.string().required(t('pleaseAddName')) : Yup.string(),
    zip: showAdditionalFields ? Yup.string().required(t('pleaseAddZipCode')) : Yup.string(),
    lastName: Yup.string(),
  });

  const questionService = useMemo(() => new QuestionService(), []);
  const trackerService = useMemo(() => new TrackerService(), []);
  const userService = useMemo(() => new UserService(), []);

  // Initialize form with stored user data if available
  const storedUser = useMemo(() => {
    const userJson = localStorage.getItem('user');
    return userJson ? JSON.parse(userJson) : null;
  }, []);

  const formik = useFormik({
    initialValues: {
      question: initialQuestion,
      category: 'General Inquiry',
      email: storedUser?.email || '',
      firstName: storedUser?.firstName || '',
      lastName: storedUser?.lastName || '',
      zip: storedUser?.zip || '',
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        setIsSubmitting(true);
        setFormError(null); // Clear any previous errors

        // If not an existing user, check if user exists
        if (!isExistingUser) {
          const userResponse = await userService.getUserByEmail(clientId, values.email);
          
          // Always update user data with the /me endpoint
          await userService.updateCurrentUserSession({
            email: values.email,
            firstName: values.firstName,
            lastName: values.lastName,
            zip: values.zip,
            clientId
          });
          
          // If user exists and we have token data, store it and send magic link
          if (userResponse.data && userResponse.data.user && userResponse.data.token) {
            userService.storeUserAndSessionData(userResponse.data.user, userResponse.data.token);
            await userService.sendMagicLink(values.email, clientId);
          }
        }

        // Ensure clientId is included in the question data
        const questionData: NewQuestionData = {
          text: values.question,
          clientId,
          userId,
          originalLanguage: currentLanguage,
          category: values.category,
          asked: true,
          votes: 1,
          name: values.firstName
        };

        const response = await questionService.createQuestion(questionData);

        // Track question creation
        if (response && response.data && response.data.length > 0) {
          await trackerService.sendStats({
            event_category: 'Questions',
            event_action: 'sendQuestion',
            question_id: response.data[0].id,
            questionId: response.data[0].id,
            questionText: values.question,
            category: values.category,
            userId: isExistingUser ? userId : undefined,
            firstName: values.firstName,
            lastName: values.lastName,
            email: values.email,
            zip: values.zip,
            clientId
          });
        }

        setShowCompletionMessage(true);
        setTimeout(() => {
          setIsExpanded(false);
          onClose?.();
        }, 3000);
      } catch (err) {
        console.error('Error submitting question:', err);
        setFormError(err instanceof Error ? err.message : t('errorSubmittingQuestion'));
        
        formik.setErrors({
          question: err instanceof Error ? err.message : t('errorSubmittingQuestion')
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  });

  const { errors, touched, values, handleBlur, setFieldValue } = formik;

  // Check if we have a stored user with complete information
  useEffect(() => {
    if (storedUser && storedUser.email && storedUser.firstName && storedUser.zip) {
      setIsExistingUser(true);
      setShowAdditionalFields(false);
    } else {
      setIsExistingUser(false);
    }
  }, [storedUser]);

  // Check if email is entered to show additional fields (only for non-existing users)
  useEffect(() => {
    if (!isExistingUser && values.email && values.email.includes('@') && values.email.includes('.')) {
      // Check if user exists
      const checkUser = async () => {
        try {
          const userResponse = await userService.getUserByEmail(clientId, values.email);
          if (!userResponse.data || !userResponse.data.user) {
            setShowAdditionalFields(true);
          } else {
            // If user exists but we need to update their info
            const user = userResponse.data.user;
            if (!user.firstName || !user.zip) {
              setShowAdditionalFields(true);

              // Pre-fill any existing data
              if (user.firstName) setFieldValue('firstName', user.firstName);
              if (user.lastName) setFieldValue('lastName', user.lastName);
              if (user.zip) setFieldValue('zip', user.zip);
            } else {
              // User exists with complete info
              setIsExistingUser(true);
              setShowAdditionalFields(false);
            }
          }
        } catch (error) {
          console.error('Error checking user:', error);
          setShowAdditionalFields(true);
        }
      };

      checkUser();
    }
  }, [values.email, clientId, setFieldValue, userService, isExistingUser]);

  useEffect(() => {
    // Notify parent component about expansion state change
    onToggleExpand?.(isExpanded);
    
    // Handle body scroll when popup is open
    if (isExpanded) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [isExpanded, onToggleExpand]);

  const handleToggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className={styles.container}>
      <div 
        className={styles.header}
        onClick={handleToggleExpand}
      >
        <h4>{t('didntFindAnswer')}</h4>
        <FontAwesomeIcon
          icon={isExpanded ? faTimes : faCaretDown}
          className={cn(styles.icon, { [styles.caretExpanded]: isExpanded })}
        />
      </div>

      <div className={cn(styles.popupOverlay, {[styles.popupOverlayVisible]: isExpanded })} onClick={() => setIsExpanded(false)}>
        <div 
          className={styles.popupContent}
          onClick={(e) => e.stopPropagation()}
        >
          <div className={styles.popupHeader}>
            <h3>{t('submitQuestionExplanation')}</h3>
            <button 
              className={styles.closeButton}
              onClick={() => setIsExpanded(false)}
            >
              <FontAwesomeIcon icon={faTimes} />
            </button>
          </div>
          
          <FormikProvider value={formik}>
            <Form>
              <input
                type="hidden"
                name="question"
                value={values.question}
              />

              {hasCategories && (
                <div className={styles.inputGroup}>
                  <label htmlFor="category">{t('category')}</label>
                  <div className={styles.categorySelect}>
                    <label htmlFor="category" className={styles.inset}>
                      <Icons iconType={values.category} />
                    </label>
                    <select
                      id="category"
                      name="category"
                      value={values.category}
                      onChange={(e) => setFieldValue('category', e.target.value)}
                      onBlur={handleBlur}
                      className={cn(
                        styles.outset,
                        errors.category && touched.category ? styles.hasError : ''
                      )}
                    >
                      <option value=""></option>
                      {allCategories.map(category => (
                        <option value={category} key={category}>
                          {category}
                        </option>
                      ))}
                    </select>
                  </div>
                  {errors.category && touched.category && (
                    <div className={styles.errorText}>{errors.category}</div>
                  )}
                </div>
              )}

              {!isExistingUser && (
                <>
                  <div className={styles.inputGroup}>
                    <label htmlFor="email">{t('email')}</label>
                    <Field
                      type="email"
                      id="email"
                      name="email"
                      placeholder={t('email')}
                      className={errors.email && touched.email ? styles.hasError : ''}
                    />
                    {errors.email && touched.email && (
                      <div className={styles.errorText}>{errors.email}</div>
                    )}
                  </div>

                  {showAdditionalFields && (
                    <div className={styles.additionalFields}>
                      <div className={styles.inputGroup}>
                        <label htmlFor="zip">
                          {t('zipCode')}<small> ({t('required')})</small>
                        </label>
                        <Field
                          type="text"
                          id="zip"
                          name="zip"
                          placeholder={t('zipCode')}
                          className={errors.zip && touched.zip ? styles.hasError : ''}
                        />
                        {errors.zip && touched.zip && (
                          <div className={styles.errorText}>{errors.zip}</div>
                        )}
                      </div>

                      <div className={styles.inputGroup}>
                        <label htmlFor="firstName">
                          {t('firstName')}<small> ({t('displayedNextToQuestion')})</small>
                        </label>
                        <Field
                          type="text"
                          id="firstName"
                          name="firstName"
                          placeholder={t('firstName')}
                          className={errors.firstName && touched.firstName ? styles.hasError : ''}
                        />
                        {errors.firstName && touched.firstName && (
                          <div className={styles.errorText}>{errors.firstName}</div>
                        )}
                      </div>

                      <div className={styles.inputGroup}>
                        <label htmlFor="lastName">
                          {t('lastName')}<small> ({t('notDisplayedToOtherUsers')})</small>
                        </label>
                        <Field
                          type="text"
                          id="lastName"
                          name="lastName"
                          placeholder={t('lastName')}
                        />
                      </div>
                    </div>
                  )}
                </>
              )}

              {isExistingUser && (
                <div className={styles.userInfoSummary}>
                  <p className={styles.welcomeBack}>
                    {t('welcomeBack')}, {values.firstName}!
                  </p>
                  <p className={styles.userEmail}>{values.email}</p>
                </div>
              )}

              {showCompletionMessage && (
                <div className={styles.successMessage}>
                  {t('questionSubmitted')}
                </div>
              )}

              {formError && (
                <div className={styles.errorMessage}>
                  {formError}
                </div>
              )}

              <button
                type="submit"
                className={styles.submitButton}
                disabled={isSubmitting}
                onClick={(e) => {
                  e.preventDefault();
                  formik.handleSubmit();
                }}
              >
                {isSubmitting ? t('submitting') : t('submitQuestion')}
              </button>
            </Form>
          </FormikProvider>
        </div>
      </div>
    </div>
  );
}
