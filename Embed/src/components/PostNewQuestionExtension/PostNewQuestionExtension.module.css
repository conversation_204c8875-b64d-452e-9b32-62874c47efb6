
.container {
  margin: 1rem 0;
  padding: 1rem;
  background: rgb(242, 246, 255);
  border: 1px solid rgb(219, 230, 255);
  border-radius: 0.5rem;
  color: rgb(59, 92, 162);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  font-weight: 700;
  color: rgb(59, 92, 162);
}

.header h4 {
  margin: 0;
  font-size: 1rem;
}

.icon {
  transition: transform 0.3s ease;
}

.caretExpanded {
  transform: rotate(180deg);
}

/* Popup styles */
.popupOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 1px;
  background-color: rgba(219, 230, 255, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-end;
  z-index: 1000;
  transition: 0.5s all;
  opacity: 0;
  visibility: hidden;
}

.popupContent {
  top: -50px;
  transition: 0.5s all;
  background: white;
  width: 100%;
  max-width: calc(100% - 2px);
  max-height: 90vh;
  overflow-y: auto;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  position: relative;
  opacity: 0;
  visibility: hidden;
}

.popupOverlayVisible {
  opacity: 1;
  visibility: visible;
}
.popupOverlayVisible .popupContent {
  top: 0px;
  opacity: 1;
  visibility: visible;
}

.popupHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.popupHeader h3 {
  margin: 0;
  font-size: 1.25rem;
  color: rgb(59, 92, 162);
}

.closeButton {
  background: none;
  border: none;
  color: rgb(59, 92, 162);
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  color: rgb(41, 65, 115);
}

/* Keep existing form styles */
.inputGroup {
  margin-bottom: 1rem;
}

.inputGroup label {
  display: block;
  margin-bottom: 0.5rem;
  color: rgb(153, 153, 153);
  font-size: 0.875rem;
  opacity: 0.9;
}

.inputGroup label small {
  margin-left: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.75rem;
}

.counter {
  float: right;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.75rem;
}

.inputGroup textarea,
.inputGroup input,
.inputGroup select {
  width: 100%;
  padding: 0.75rem;
  background-color: white;
  border: 1px solid rgb(216, 216, 216);
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: rgb(0, 0, 0);
}

.inputGroup textarea {
  min-height: 100px;
  resize: vertical;
}

.inputGroup textarea:focus,
.inputGroup input:focus,
.inputGroup select:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
}

.hasError {
  border-color: #FF2F28 !important;
  background-color: rgba(255, 47, 40, 0.1) !important;
}

.errorText {
  color: var(--error-color, #FF2F28);
  margin-top: 0.25rem;
  font-size: 0.75rem;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.successMessage {
  margin: 1rem 0;
  padding: 0.75rem;
  background-color: rgba(41, 179, 110, 0.2);
  color: var(--primary-color, rgb(59, 92, 162));
  border-radius: 0.5rem;
  font-size: 0.875rem;
}

.additionalFields {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.submitButton {
  background-color: var(--submit-button-color, rgb(52, 83, 141));
  color: var(--text-on-primary, rgb(255, 255, 255));
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  margin-top: 1rem;
  transition: background-color 0.2s ease;
}

.submitButton:hover {
  background-color: var(--submit-button-hover, rgb(62, 93, 151));
}

.submitButton:active {
  background-color: var(--submit-button-active, rgb(42, 73, 131));
}

.submitButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.submitButton:not(:disabled):hover {
  background-color: rgba(52, 83, 141, 0.9);
}

.loadingText {
  color: rgb(59, 92, 162);
  font-size: 0.875rem;
  padding: 0.5rem;
  text-align: center;
  background: rgba(242, 246, 255, 0.8);
  border-radius: 0.5rem;
}

.categorySelect {
  position: relative;
  width: 100%;
}

.categorySelect .inset {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  pointer-events: none;
  color: #64748b;
}

.categorySelect .outset {
  width: 100%;
  padding: 8px 32px 8px 40px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  appearance: none;
  background-color: white;
}

.categorySelect .outset:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.categorySelect .outset.hasError {
  border-color: #ef4444;
}

.categorySelect::after {
  content: '';
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #64748b;
  pointer-events: none;
}

.errorMessage {
  margin: 1rem 0;
  padding: 0.75rem;
  background-color: rgba(255, 47, 40, 0.1);
  color: #FF2F28;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  border: 1px solid #FF2F28;
}

.userInfoSummary {
  background-color: rgba(0, 110, 255, 0.05);
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 20px;
}

.welcomeBack {
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #333;
}

.userEmail {
  margin: 0;
  color: #666;
  font-size: 0.9em;
}
