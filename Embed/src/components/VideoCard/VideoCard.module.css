.container {
  position: relative;
  cursor: pointer;
  border-radius: 0.5rem;
  overflow: hidden;
  width: 270px;
  height: 200px;
  transition: all 0.3s ease;
}

:global(.shrink) .container,
.shrink ~ .container {
  height: 120px;
}

.shrink {
  width: 200px;
  height: 110px;
}

.thumbnail {
  width: 100%;
  height: 100%;
  object-fit: contain;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.thumbnailBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(8px) brightness(0.7);
  transform: scale(1.1);
  z-index: 0;
}

.overlay {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 2;
}

.playButtonWrapper {
  width: 100%;
  height: 3rem;
  position: absolute;
  bottom: 0;
  overflow: hidden;
  border-radius: 0 0 0.5rem 0.5rem;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
  z-index: 3;
}

.watchText {
  color: white;
  font-size: 1.25rem;
  font-weight: 700;
  margin-left: 0.4rem;
}

.container:hover .playButtonWrapper {
  /* background-color: #e94e77; */
  transform: scale(1.1);
  border-radius: 0 0 1.5rem 1.5rem;
}

.container:hover .overlay {
  background-color: rgba(0, 0, 0, 0.4);
}

.playIcon {
  width: 2rem;
  height: 2rem;
  color: #FFFFFF55;
  transition: all 0.3s ease;
}

.titleContainer {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  padding: 0.75rem;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8), transparent);
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 3;
}

.container:hover .titleContainer {
  opacity: 1;
  transform: translateY(0);
}

.title {
  color: white;
  font-weight: 500;
  font-size: 0.875rem;;
}
