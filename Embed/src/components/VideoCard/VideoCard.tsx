import { Play } from 'lucide-react';
import styles from './VideoCard.module.css';
import { useLanguage } from '../../context/LanguageContext';
import type { Answer } from '../../interfaces/answer.interface';

interface VideoCardProps {
  answer: Answer;
  onClick: () => void;
  isQuestionFormExpanded?: boolean;
}

export function VideoCard({ answer, onClick, isQuestionFormExpanded = false }: VideoCardProps) {
  const { translateContent, t } = useLanguage();

  const translatedTitle = translateContent({
    text: answer.question.text,
    translations: answer.question.translations,
    originalLanguage: answer.question.originalLanguage
  });

  return (
    <div
      className={`${styles.container} ${isQuestionFormExpanded ? styles.shrink : ''}`}
      onClick={onClick}
    >
      <img
        src={answer.thumbnail}
        alt="Background"
        className={styles.thumbnailBackground}
      />
      <img
        src={answer.thumbnail}
        alt={translatedTitle}
        className={styles.thumbnail}
      />
      <div className={styles.overlay}>
        <div className={styles.playButtonWrapper}>
          <Play className={styles.playIcon} fill="rgba(255, 255, 255, 0.7)" strokeWidth={0} />
          <div className={styles.watchText}>{t('watch')}</div>
        </div>
      </div>
      <div className={styles.titleContainer}>
        <h4 className={styles.title}>{translatedTitle}</h4>
      </div>
    </div>
  );
}
