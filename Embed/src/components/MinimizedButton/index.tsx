import styles from './MinimizedButton.module.css';

interface MinimizedButtonProps {
  onClick: () => void;
}

export function MinimizedButton({ onClick }: MinimizedButtonProps) {
  return (
    <button
      onClick={onClick}
      className={styles.button}
    >
      <img
        src="/src/assets/ask-the-city-button.svg"
        alt="Ask the City"
        className={styles.image}
      />
    </button>
  );
}
