import React, { useState, useEffect } from 'react';
import { ReactComponent as AskButtonSvg } from '../../assets/ask-the-city-button.svg';
import { useLanguage } from '../../context/LanguageContext';
import { SvgIcon } from '../SvgIcon/SvgIcon';
import { ClientColorService } from '../../services/clientColorService';
import type { ClientInterface } from '../../interfaces/client.interface';

// Import custom button images
// import lorainButton from '../../assets/clients/custom-buttons/lorain.png';
// import loudounCityButton from '../../assets/clients/custom-buttons/loudoun-city.png';
// import mountJacksonButton from '../../assets/clients/custom-buttons/mount-jackson.png';
// import parkCityButton from '../../assets/clients/custom-buttons/park-city.png';
import southBayButton from '../../assets/clients/custom-buttons/south-bay.png';
import traverseCityButton from '../../assets/clients/custom-buttons/traverse-city.png';
import hermistonButton from '../../assets/clients/custom-buttons/hermiston.png';
import mapleValleyButton from '../../assets/clients/custom-buttons/maple-valley.png';
import mapleValleyFlippedButton from '../../assets/clients/custom-buttons/maple-valley-flipped.png';
// import yorkButton from '../../assets/clients/custom-buttons/york.png';
import styles from './MinimizedButton.module.css';

interface MinimizedButtonProps {
  onClick: () => void;
  client?: ClientInterface | null;
}

export function MinimizedButton({ onClick, client }: MinimizedButtonProps) {
  const { clientName } = useLanguage();
  const [isMobile, setIsMobile] = useState(false);

  // Hook to detect mobile screen size
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Mapping of client IDs to their custom button images
  const customButtonImages: Record<string, string> = {
    // '294': yorkButton,        // york
    // '292': loudounCityButton, // loudoun-city
    // '293': mountJacksonButton, // mount-jackson
    // '288': parkCityButton,    // park-city
    // '278': lorainButton,      // lorain
    '279': isMobile ? mapleValleyFlippedButton : mapleValleyButton, // maple-valley
    '303': hermistonButton,    // hermiston
    '276': southBayButton,    // south-bay
    '269': traverseCityButton, // traverse-city
  };

  const hasCustomImage = client?.id && customButtonImages[client.id];
  const minimizedButtonImage = client?.id ? customButtonImages[client.id] : undefined;

  // Get client-specific button text or fall back to default
  const clientButtonText = client?.id ? ClientColorService.getClientButtonText(client.id) : null;
  const buttonText = clientButtonText || `Ask ${clientName}`;

  return (
    <div className={styles.container}>
      <button onClick={onClick} className={styles.button} aria-label={buttonText}>
        {hasCustomImage && minimizedButtonImage ? (
          <img
            src={minimizedButtonImage}
            alt={buttonText}
            className={styles.svg}
          />
        ) : (
          <SvgIcon
            icon={AskButtonSvg}
            className={styles.svg}
            title={buttonText}
            aria-label={buttonText}
            client={client}
          />
        )}
      </button>
    </div>
  );
}
