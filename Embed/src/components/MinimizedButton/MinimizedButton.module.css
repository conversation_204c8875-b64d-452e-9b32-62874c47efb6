.button {
  position: fixed;
  bottom: 1rem;
  left: 1rem;
  z-index: 50;
  width: 10rem;
  height: 10rem;
  transition: transform 0.2s;
  animation: fadeIn 500ms ease-in forwards;
  animation-delay: 500ms;
  opacity: 0; /* Start invisible */
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.button:hover {
  transform: translateY(-0.5rem);
}

.image {
  width: 100%;
  height: 100%;
}

@media (min-width: 768px) {
  .button {
    bottom: 2rem;
    left: 2rem;
  }
}