import { motion, AnimatePresence } from 'framer-motion';
import { useEffect, useRef } from 'react';
import { Send } from 'lucide-react';
import styles from './QuestionInput.module.css';
import { useLanguage } from '../../context/LanguageContext';
import { getCurrentClientId, getClientLogoUrl, getClientLogoStyles } from '../../config/clientLogoConfig';
import type { ClientInterface } from '../../interfaces/client.interface';

interface QuestionInputProps {
  value: string;
  onChange: (value: string) => void;
  isLoading?: boolean;
  onSubmit?: () => void;
  client?: ClientInterface | null;
}

export function QuestionInput({
  value,
  onChange,
  isLoading = false,
  onSubmit,
  client
}: QuestionInputProps) {
  const { t } = useLanguage();
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Get current client ID using centralized function
  const currentClientId = getCurrentClientId(client);

  // Get logo URL and styles using centralized configuration
  const logoUrl = getClientLogoUrl(currentClientId, client?.logoURL);
  const customLogoStyles = getClientLogoStyles(currentClientId);

  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey && value.trim() !== '') {
      e.preventDefault();
      onSubmit?.();
    }
  };

  const adjustHeight = () => {
    const textarea = textareaRef.current;

    if (textarea && !textarea.value) {
      textarea.style.height = 'auto';
    }
    else if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  };

  useEffect(() => {
    adjustHeight();
  }, [value]);

  return (
    <div className={styles.container}>
      <AnimatePresence mode="wait">
        {isLoading ? (
          <motion.div
            key="loading"
            className={styles.loadingContainer}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className={styles.iconWrapper}
              animate={{
                y: [-10, 0, -10],
                filter: ['drop-shadow(0 10px 15px rgba(0,0,0,0.3))', 'drop-shadow(0 20px 25px rgba(0,0,0,0.15))', 'drop-shadow(0 10px 15px rgba(0,0,0,0.3))'],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <img
                src={logoUrl || client?.logoURL || ''}
                alt={`${client?.name || currentClientId || 'Client'} Logo`}
                className={styles.loadingIcon}
                id="cityQuestionIcon"
                style={customLogoStyles}
              />
            </motion.div>

            {/* New loading bar with both animations */}
            <div className={styles.newLoadingBar}>
              <div className={styles.newProgressBar}></div>
              <div className={styles.gradientAnimation}></div>
            </div>
          </motion.div>
        ) : (
          <motion.div
            key="input"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            style={{ width: '100%' }}
          >
            {/* <div className={styles.headerContainer}>
              <h2 className={styles.title}>{t('askCity')}</h2>
            </div> */}

            <div className={styles.inputWrapper}>
              <textarea
                ref={textareaRef}
                placeholder={t('whatQuestion')}
                value={value}
                onChange={(e) => onChange(e.target.value)}
                onKeyPress={handleKeyPress}
                className={styles.input}
                rows={1}
              />
              <AnimatePresence>
                {value.trim() !== '' && (
                  <motion.button
                    className={styles.sendButton}
                    onClick={() => onSubmit?.()}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Send className={styles.sendIcon} size={20} />
                  </motion.button>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
