.pillBackground {
  background-color: white;
  border-radius: 10px;
  padding: 5px;
  display: inline-block;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.headerContainer {
  text-align: center;
  margin-bottom: 1.5rem;
}

.title {
  font-size: 1.75rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
}

.inputWrapper {
  position: relative;
  display: flex;
  align-items: flex-end;
}

.input {
  flex: 1;
  padding-right: 40px;
  width: 100%;
  padding: 1rem 3rem 1rem 1.25rem;
  border-radius: 0.5rem;
  background-color: white;
  color: #1F2937;
  resize: none;
  overflow: hidden;
  min-height: 3.5rem;
  border: none;
  font-family: inherit;
  font-size: inherit;
  line-height: 1.5;
}

.input::placeholder {
  color: #9CA3AF;
}

.input:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--focus-color, #e94e77);
}

.sendButton {
  position: absolute;
  right: 15px;
  top: 15px;
  background: none;
  border: none;
  padding: 4px 8px;
  cursor: pointer;
  color: var(--primary-color, #006eff);
  opacity: 0.7;
  transition: opacity 0.2s;
  border-radius: 50%;
  background-color: white;
}

.sendButton:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
}

.sendIcon {
  display: block;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 200px;
  padding-bottom: 5px;
}

.iconWrapper {
  max-width: 350px;
  max-height: 175px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.loadingIcon {
  width: 100%;
  height: 100%;
  max-height: 155px;
  object-fit: contain;
}

.newLoadingBar {
  width: 100%;
  height: 8px;
  background-color: var(--progress-bar-background, rgba(0, 0, 0, 0.2));
  border-radius: 0.5rem;
  margin-top: 20px;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.newProgressBar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  background-color: white;
  border-radius: 0.5rem;
  animation: progressAnimation 8s linear forwards;
  z-index: 1;
  /* box-shadow: 0 0 10px var(--progress-bar-color, rgba(233, 78, 119, 0.5)); */
}

.gradientAnimation {
  position: absolute;
  top: 0;
  left: -100%;
  height: 100%;
  width: 100%;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.5) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: gradientSlide 1.5s infinite linear;
  z-index: 2;
}

@keyframes progressAnimation {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}

@keyframes gradientSlide {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(200%);
  }
}

/* Add a pulsing effect to the loading bar */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.newProgressBar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
  animation: pulse 2s infinite;
}
