import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import axios from 'axios';

import { MinimizedButton } from '../MinimizedButton/MinimizedButton';
import { MainContent } from '../MainContent/MainContent';
import { SessionService, SessionState } from '../../services/sessionService';
import { WindowService } from '../../services/windowService';
import { TrackerService } from '../../services/tracker.service';
import { useAi } from '../../context/AiContext';
import type { Answer } from '../../interfaces/answer.interface';
import styles from './AppContent.module.css';
import { TranslationService } from '../../services/translationService';
import { applyClientCustomizationsToAllSvgs } from '../../utils/svgUtils';
import { ClientColorService } from '../../services/clientColorService';



export function AppContent() {
  const [session, setSession] = useState<SessionState | null>(null);
  const [question, setQuestion] = useState('');
  const [isMinimized, setIsMinimized] = useState(true);
  const [showAnswer, setShowAnswer] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState<Answer | null>(null);
  const { fetchAnswer } = useAi();

  useEffect(() => {
    const sessionService = new SessionService();
    sessionService.initialize()
      .then(sessionData => {
        setSession(sessionData);
        if (sessionData.client) {
          // Apply all client colors dynamically using the ClientColorService
          const colorService = ClientColorService.getInstance();
          colorService.applyClientColors(sessionData.client);

          // Set client name for translations
          if (sessionData.client.name) {
            TranslationService.setClientName(sessionData.client.name);
          }

          // Apply client customizations to all SVGs on the page
          applyClientCustomizationsToAllSvgs(sessionData.client);
        }
      })
      .catch(error => console.error('Session initialization failed:', error));
  }, []);

  useEffect(() => {
    WindowService.updateParentDimensions(isMinimized);
  }, [isMinimized]);

  useEffect(() => {
    // Set up axios interceptor to use current token
    if (session?.token) {
      axios.defaults.headers.common['Authorization'] = `${session.token}`;
    }
  }, [session?.token]);

  // Apply client customizations whenever the session changes or component state changes
  useEffect(() => {
    if (session?.client) {
      // Apply immediately
      applyClientCustomizationsToAllSvgs(session.client);

      // Also apply after a short delay to catch any SVGs that render later
      const timeoutId = setTimeout(() => {
        applyClientCustomizationsToAllSvgs(session.client);
      }, 100);

      // Apply after a longer delay to catch React component renders
      const longerTimeoutId = setTimeout(() => {
        applyClientCustomizationsToAllSvgs(session.client);
      }, 500);

      return () => {
        clearTimeout(timeoutId);
        clearTimeout(longerTimeoutId);
      };
    }
  }, [session?.client, isMinimized]);

  const handleQuestionChange = (value: string) => {
    setQuestion(value);
    setShowAnswer(false);
  };

  const handleSubmit = async (q: string | null = null) => {
    const selectedQuestion = q ? q.trim() : question.trim();

    if (!session || selectedQuestion === '') return;

    try {
      await fetchAnswer(
        selectedQuestion, 
        session.client?.id || localStorage.getItem('clientId') || '', 
        session.user?.id || localStorage.getItem('userId') || '',
        session.client?.isInternal
      );
      setShowAnswer(true);
    } catch (error) {
      console.error('Failed to get answer:', error);
    }
  };

  const handleVideoSelect = (answer: Answer | null) => {
    if (answer === null) {
      // When closing the video (answer is null), maintain the previous state
      setSelectedVideo(null);
      // Don't change the showAnswer state here, keep it as is
    } else {
      // When opening a video (including switching videos), store the new video
      setSelectedVideo(answer);
    }
  };

  const handleAskAgain = () => {
    setQuestion('');
    setShowAnswer(false);
  };

  const handleMinimizedButtonClick = () => {
    // Track module opening as engagement
    const trackerService = new TrackerService();
    trackerService.sendStats({
      event_action: 'module_open',
      event_category: 'Engagement',
      event_label: 'Marvin Embed Module Opened',
      screenName: 'Marvin Embed Module Open',
      referrer: document.referrer
    });

    setIsMinimized(false);
  };

  const handleMinimize = () => {
    // Track module closing as engagement
    const trackerService = new TrackerService();
    trackerService.sendStats({
      event_action: 'module_close',
      event_category: 'Engagement',
      event_label: 'Marvin Embed Module Closed',
      screenName: 'Marvin Embed Module Close',
      referrer: document.referrer
    });

    setIsMinimized(true);
  };

  return (
    <AnimatePresence mode="wait">
      {isMinimized ? (
        <MinimizedButton
          key="minimized"
          onClick={handleMinimizedButtonClick}
          client={session?.client || null}
        />
      ) : (
        <motion.div
          key="expanded"
          id="AppContent"
          className={styles.container}
          initial={{ x: '-100%', opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: '-100%', opacity: 0 }}
          transition={{ type: 'spring', damping: 25, stiffness: 300 }}
        >
          <MainContent
            session={session}
            selectedVideo={selectedVideo}
            showAnswer={showAnswer}
            question={question}
            onMinimize={handleMinimize}
            onVideoSelect={handleVideoSelect}
            onAskAgain={handleAskAgain}
            onQuestionChange={handleQuestionChange}
            onSubmit={handleSubmit}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
}
