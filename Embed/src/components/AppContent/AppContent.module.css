.container {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  z-index: 50;
  width: calc(100% - 2rem);
  max-height: calc(100vh - 40px);
  overflow: scroll;
  border-radius: 10px;
}

.innerContainer {
  background: var(--background-gradient, linear-gradient(105deg, #30466F 0.25%, #1E293D 100.25%));
  border-radius: 10px;
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  overflow: hidden;
}

.content {
  padding: 0 1.5rem 15px 1.5rem;
}

@media (min-width: 768px) {
  .container {
    bottom: 2rem;
    left: 2rem;
    width: 480px;
  }
}

@media all and (max-height: 730px) and (min-width: 800px) {
  .container {
    zoom: 0.8;
    max-height: calc(100vh - 40px + 20%);
  }
}
