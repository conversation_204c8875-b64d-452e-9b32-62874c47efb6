import { X, ArrowLeft } from 'lucide-react';
import styles from './Header.module.css';
import { useLanguage } from '../../context/LanguageContext';
import { ClientColorService } from '../../services/clientColorService';
import { getCurrentClientId } from '../../config/clientLogoConfig';
import { TrackerService } from '../../services/tracker.service';

interface HeaderProps {
  onMinimize: () => void;
  onBack?: () => void;
  showBackButton?: boolean;
}

export function Header({ onMinimize, onBack, showBackButton }: HeaderProps) {
  const { t, clientName } = useLanguage();

  // Get current client ID and check for custom button text
  const currentClientId = getCurrentClientId();
  const customButtonText = currentClientId ? ClientColorService.getClientButtonText(currentClientId) : null;

  // Use custom text if available, otherwise fall back to default behavior
  const titleText = customButtonText || t('askTheCity').replace('the City', clientName);

  const handleBackClick = () => {
    // Track back button press as engagement
    const trackerService = new TrackerService();
    trackerService.sendStats({
      event_action: 'back_button_press',
      event_category: 'Engagement',
      event_label: 'Back Button Pressed',
      screenName: 'Marvin Embed Back Navigation',
      referrer: document.referrer
    });

    if (onBack) {
      onBack();
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.titleWrapper}>
        {showBackButton && (
          <button
            onClick={handleBackClick}
            className={styles.backButton}
          >
            <ArrowLeft className={styles.backIcon} />
          </button>
        )}
        <h1 className={styles.title}>{titleText}</h1>
      </div>
      <button
        onClick={onMinimize}
        className={styles.minimizeButton}
      >
        <X className={styles.icon} />
      </button>
    </div>
  );
}
