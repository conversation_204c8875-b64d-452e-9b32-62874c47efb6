.container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1rem 0 1rem;
}

.titleWrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.title {
  font-size: 1.25rem;
  color: white;
  font-weight: 600;
}

.backButton {
  padding: 0.5rem;
  border-radius: 9999px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.backButton:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.backIcon {
  width: 1.5rem;
  height: 1.5rem;
  color: white;
}

.minimizeButton {
  padding: 0.5rem;
  border-radius: 9999px;
  transition: background-color 0.2s;
}

.minimizeButton:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.icon {
  width: 1.5rem;
  height: 1.5rem;
  color: white;
}
