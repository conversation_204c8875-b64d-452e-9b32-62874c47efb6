import { ArrowLeft, X } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';
import { setupSubtitleTracking, TranscriptionTranslations, type Transcription, getCurrentSubtitle } from '../../services/videoHelperService';
import styles from './VideoPlayer.module.css';
import { ReactComponent as RepdLogoSvg } from '../../assets/Rep\'d-logo-white.svg';
import { useLanguage } from '../../context/LanguageContext';
import type { Language } from '../../translations';
import { TrackerService } from '../../services/tracker.service';
// Fix the import path - use the answerService function instead of a class
import { getVideoTranscription } from '../../services/answerService';

interface VideoPlayerProps {
  video: {
    id: string;
    videoUrl: string;
    videoUrls?: {
      mp4: string;
      webm: string;
      ogv: string;
    };
    stats: {
      votes: number;
    };
    showTranscribedSubtitles: boolean; // This is undefined in your debug logs
    transcription: Transcription;       // This is not being passed correctly
    transcriptionTranslation: TranscriptionTranslations;
    question: {
      text: string;
      translations?: Partial<Record<Language, string>>;
      originalLanguage?: Language;
      user?: {
        firstName: string;
        location: string;
      };
    };
  };
  onClose: () => void;
  fromAnswerView?: boolean;
}

export function VideoPlayer({ video, onClose, fromAnswerView = false }: VideoPlayerProps) {
  const [isPaused, setIsPaused] = useState(false);
  const [currentSubtitle, setCurrentSubtitle] = useState('');
  const [isSubtitleChanging, setIsSubtitleChanging] = useState(false);
  const previousSubtitleRef = useRef('');
  const [transcriptionData, setTranscriptionData] = useState<{
    transcription: Transcription,
    transcriptionTranslation: TranscriptionTranslations
  }>({
    transcription: video.transcription || { items: [] },
    transcriptionTranslation: video.transcriptionTranslation || {}
  });
  const videoRef = useRef<HTMLVideoElement>(null);
  const backgroundVideoRef = useRef<HTMLVideoElement>(null);
  const { currentLanguage, translateContent } = useLanguage();
  const trackerService = useRef(new TrackerService());

  // Add this function to normalize language codes
  function normalizeLanguageCode(code: string): string {
    // Map API language codes to app language codes if needed
    const languageMap: Record<string, string> = {
      'en-US': 'en',
      'es-ES': 'es',
      'zh-CN': 'zh',
      'hi-IN': 'hi',
      'vi-VN': 'vi',
      'ht-HT': 'ht'
    };
    
    return languageMap[code] || code;
  }

  // Fetch transcription data if needed
  useEffect(() => {
    // Only fetch if transcription is empty but should be shown
    if (video.showTranscribedSubtitles && 
        (!video.transcription?.items?.length || 
         Object.keys(video.transcriptionTranslation || {}).length === 0)) {
      
      // Use the imported function directly instead of instantiating a class
      getVideoTranscription(video.id)
        .then(data => {
          console.log('Fetched transcription data:', data);
          setTranscriptionData(data);
        })
        .catch(error => {
          console.error('Failed to fetch transcription:', error);
        });
    }
  }, [video.id, video.showTranscribedSubtitles, video.transcription, video.transcriptionTranslation]);

  // Ensure required properties exist with defaults
  const safeVideo = {
    ...video,
    showTranscribedSubtitles: video.showTranscribedSubtitles === undefined ? true : video.showTranscribedSubtitles,
    transcription: transcriptionData.transcription,
    transcriptionTranslation: transcriptionData.transcriptionTranslation
  };

  const translatedTitle = translateContent(video.question);

  // Keep background video in sync with main video
  useEffect(() => {
    const mainVideo = videoRef.current;
    const bgVideo = backgroundVideoRef.current;

    if (!mainVideo || !bgVideo) return;

    const syncVideos = () => {
      bgVideo.currentTime = mainVideo.currentTime;
    };

    const handlePlay = () => {
      bgVideo.play();
    };

    const handlePause = () => {
      bgVideo.pause();
    };

    mainVideo.addEventListener('timeupdate', syncVideos);
    mainVideo.addEventListener('play', handlePlay);
    mainVideo.addEventListener('pause', handlePause);

    return () => {
      mainVideo.removeEventListener('timeupdate', syncVideos);
      mainVideo.removeEventListener('play', handlePlay);
      mainVideo.removeEventListener('pause', handlePause);
    };
  }, []);

  // Update subtitle with animation
  const updateSubtitleWithAnimation = (newSubtitle: string) => {
    if (newSubtitle !== previousSubtitleRef.current) {
      setIsSubtitleChanging(true);
      setCurrentSubtitle(newSubtitle);
      previousSubtitleRef.current = newSubtitle;
      
      // Reset the animation class after animation completes
      setTimeout(() => {
        setIsSubtitleChanging(false);
      }, 300);
    }
  };

  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    // Normalize translation keys if needed
    const normalizedTranslations: TranscriptionTranslations = {};
    if (safeVideo.transcriptionTranslation) {
      Object.entries(safeVideo.transcriptionTranslation).forEach(([key, value]) => {
        normalizedTranslations[normalizeLanguageCode(key)] = value;
      });
    }
    
    console.log('VideoPlayer Debug (with normalized translations):', {
      showTranscribedSubtitles: safeVideo.showTranscribedSubtitles,
      hasTranscription: !!safeVideo.transcription,
      transcriptionItems: safeVideo.transcription?.items?.length,
      transcriptionAudioSegments: safeVideo.transcription?.audio_segments?.length,
      hasTranslation: !!normalizedTranslations,
      translationLanguages: Object.keys(normalizedTranslations),
      currentLanguage,
      transcriptionStructure: safeVideo.transcription ? Object.keys(safeVideo.transcription) : 'none'
    });

    const cleanup = setupSubtitleTracking(
      videoElement,
      safeVideo.transcription,
      normalizedTranslations,
      currentLanguage,
      safeVideo.showTranscribedSubtitles,
      updateSubtitleWithAnimation
    );

    return cleanup;
  }, [safeVideo.transcription, safeVideo.transcriptionTranslation, currentLanguage, safeVideo.showTranscribedSubtitles]);

  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const handlePlay = () => {
      trackerService.current.sendStats({
        event_action: 'playVideo',
        event_category: 'Video',
        event_label: video.question.text,
        answer_id: video.id,
        question_id: video.question.id,
        referrer: window.location.href,
        mobile: /Mobile|Android|iOS/.test(navigator.userAgent),
        browser: navigator.userAgent
      });
    };

    const handlePause = () => {
      trackerService.current.sendStats({
        event_action: 'pauseVideo',
        event_category: 'Video',
        event_label: video.question.text,
        answer_id: video.id,
        question_id: video.question.id,
        referrer: window.location.href,
        mobile: /Mobile|Android|iOS/.test(navigator.userAgent),
        browser: navigator.userAgent
      });
    };

    videoElement.addEventListener('play', handlePlay);
    videoElement.addEventListener('pause', handlePause);

    return () => {
      videoElement.removeEventListener('play', handlePlay);
      videoElement.removeEventListener('pause', handlePause);
    };
  }, [video]);

  // Add this effect to log when language changes
  useEffect(() => {
    console.log('Language changed to:', currentLanguage);
    console.log('Available translation languages:', 
      safeVideo.transcriptionTranslation ? Object.keys(safeVideo.transcriptionTranslation) : []);
    
    // Check if we have translations for this language (could be audio segments or items)
    const translation = safeVideo.transcriptionTranslation?.[currentLanguage];
    const hasTranslation = translation && (
      Array.isArray(translation) ||
      ((translation as Transcription).items?.length ?? 0) > 0 ||
      ((translation as Transcription).audio_segments?.length ?? 0) > 0
    );
    
    console.log('Has translation for current language:', hasTranslation);
    
    // Force subtitle update when language changes
    if (videoRef.current) {
      const subtitle = getCurrentSubtitle(
        videoRef.current.currentTime,
        safeVideo.transcription,
        safeVideo.transcriptionTranslation,
        currentLanguage,
        safeVideo.showTranscribedSubtitles
      );
      setCurrentSubtitle(subtitle);
    }
  }, [currentLanguage, safeVideo.transcription, safeVideo.transcriptionTranslation, safeVideo.showTranscribedSubtitles]);

  console.log('Video object:', video);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <button onClick={onClose} className={styles.backButton}>
          <ArrowLeft className={styles.icon} />
        </button>
        <h2 className={styles.title}>{translatedTitle}</h2>
        <button onClick={onClose} className={styles.closeButton}>
          <X className={styles.icon} />
        </button>
      </div>
      
      <div className={styles.videoWrapper}>
        <video
          ref={backgroundVideoRef}
          className={styles.backgroundVideo}
          src={video.videoUrl}
          muted
          playsInline
        />
        <video
          ref={videoRef}
          controls
          autoPlay
          className={styles.video}
          onPlay={() => setIsPaused(false)}
          onPause={() => setIsPaused(true)}
          onError={(e) => console.error('Video error:', e)}
        >
          {/* Handle case where videoUrls might not exist */}
          {video.videoUrls && (
            <>
              <source src={video.videoUrls.mp4} type="video/mp4" />
              <source src={video.videoUrls.webm} type="video/webm" />
              <source src={video.videoUrls.ogv} type="video/ogg" />
            </>
          )}
          <source src={video.videoUrl} />
        </video>
        {isPaused && (
          <div className={styles.videoMetadata}>
            <div className={styles.author}>
              {console.log('Video object:', video) ?? <></>}
              {video.question.user ? (
                <>
                  <span className={styles.name}>
                    {`${video.question.user.firstName}`}
                  </span>
                  {video.question.user.location && (
                    <>
                      <span className={styles.bullet}>•</span>
                      <span className={styles.location}>{video.question.user.location}</span>
                    </>
                  )}
                  <span className={styles.bullet}>•</span>
                </>
              ) : null}
              <span className={styles.stats}>{video.stats.votes} votes</span>
            </div>
          </div>
        )}
        {/* Always render the subtitle container, but control visibility with CSS */}
        <div 
          className={`${styles.subtitles} ${isSubtitleChanging ? styles.changing : ''}`} 
          style={{ 
            opacity: currentSubtitle && safeVideo.showTranscribedSubtitles ? 1 : 0,
            display: safeVideo.showTranscribedSubtitles ? 'flex' : 'none' 
          }}
        >
          {currentSubtitle}
        </div>
      </div>

      <div className={styles.footer}>
        <div className={styles.poweredBy}>
          <RepdLogoSvg
            className={styles.logo}
            title="Rep'd Logo"
            role="img"
            aria-label="Rep'd Logo"
          />
          <span>Powered by Rep'd</span>
        </div>
      </div>
    </div>
  );
}
