.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #1e2942;
}

.header {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: #1e2942;
  justify-content: space-between;
}

.backButton, .closeButton {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: white;
}

.title {
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
  text-align: left;
  padding-left: 0.5rem;
}

.videoWrapper {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 300px;
  flex: 1;
  background-color: #000;
  overflow: hidden;
}

.backgroundVideo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(20px) brightness(0.7);
  opacity: 0.5;
  z-index: 0;
}

.subtitles {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 0.95em;
  text-align: center;
  max-width: 95%;
  z-index: 10; /* Increase z-index to ensure it's above all video elements */
  transition: opacity 0.3s ease;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.9);
  white-space: pre-wrap;
  min-height: 1.8em;
  max-height: 4.5em; /* Allow for up to 3 lines */
  overflow: scroll;
  pointer-events: none; /* Ensure clicks pass through to video */
  display: flex;
  align-items: flex-end;
  justify-content: center;
  line-height: 1.4;
  word-wrap: break-word;
  hyphens: auto;
  font-weight: 500;
  letter-spacing: 0.02em;

  /* hide scrollbar in WebKit browsers (Chrome, Safari) */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none;  /* IE and Edge */
}

.subtitles::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Add a fade-in/fade-out animation for subtitle changes */
/* @keyframes subtitleFade {
  0% { opacity: 0; transform: translateX(-50%) translateY(10px); }
  100% { opacity: 1; transform: translateX(-50%) translateY(0); }
} */

.subtitles.changing {
  animation: subtitleFade 0.05s ease-out forwards;
}

/* Responsive subtitle sizing for different embed sizes */
@media (max-width: 480px) {
  .subtitles {
    font-size: 0.85em;
    padding: 6px 12px;
    max-width: 98%;
    bottom: 15px;
    max-height: 5.5em;
  }
}

@media (max-height: 400px) {
  .subtitles {
    font-size: 0.8em;
    padding: 4px 8px;
    bottom: 10px;
    max-height: 4.5em;
    line-height: 1.3;
  }
}

/* Very small embed windows */
@media (max-width: 320px) {
  .subtitles {
    font-size: 0.75em;
    padding: 4px 6px;
    max-height: 4em;
    line-height: 1.2;
  }
}

/* Extra small height - prioritize readability */
@media (max-height: 300px) {
  .subtitles {
    font-size: 0.7em;
    padding: 2px 4px;
    max-height: 3.5em;
    bottom: 8px;
  }
}

.videoMetadata {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 1rem;
  background: linear-gradient(to bottom, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
  z-index: 1;
  color: white;
}

.videoTitle {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.author {
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.name {
  font-weight: 600;
}

.bullet {
  color: rgba(255, 255, 255, 0.5);
}

.location, .stats {
  color: rgba(255, 255, 255, 0.7);
}

.video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  z-index: 1;
  background: transparent;
}

.footer {
  padding: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #1e2942;
}

.poweredBy {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-size: 0.875rem;
}

.logo {
  height: 1rem;
  width: auto;
}

.icon {
  width: 1.25rem;
  height: 1.25rem;
}
