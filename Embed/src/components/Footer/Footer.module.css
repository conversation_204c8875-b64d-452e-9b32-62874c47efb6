.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.25rem 1rem .5rem 1rem;
  color: #9CA3AF;
  font-size: 0.875rem;
  position: relative;
  z-index: 3;
}

.controlsContainer {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logoContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ffffff90;
}

.logo {
  width: 1.25rem;
  height: 1.25rem;
}

.languageSelector {
  position: relative;
}

.selectorButton {
  display: flex;
  align-items: center;
  padding: 0.25rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
  background: none;
  border: none;
  cursor: pointer;
}

.selectorButton:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.flagContainer {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  overflow: hidden;
}

.flag {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.fontSizeSelector {
  position: relative;
}

.fontIconContainer {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.fontSizeMenu {
  position: absolute;
  bottom: 100%;
  right: 0;
  width: 120px;
  background-color: #1e2942;
  border-radius: 0.375rem;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  z-index: 10;
}

.fontSizeOption {
  width: 100%;
  padding: 0.5rem 1rem;
  text-align: left;
  background: none;
  border: none;
  color: #FFF;
  cursor: pointer;
  transition: background-color 0.2s;
}

.fontSizeOption.active {
  background-color: rgba(255, 255, 255, 0.15);
  font-weight: bold;
}

.bubbleCloud {
  position: absolute;
  bottom: 100%;
  right: 0;
  width: 280px;
  height: 280px;
  margin-bottom: 1rem;
  pointer-events: none;
}

.bubbleOption {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
  pointer-events: auto;
}

.bubbleFlagContainer {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #FFF;
  background-color: #1e2942;
  transition: border-color 0.2s;
}

.bubbleFlagContainer:hover {
  border-color: rgba(255, 255, 255, 0.3);
}

.bubbleFlag {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.customLanguageButton {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 0.75rem;
  border-radius: 50%;
}

.customLanguageBubble {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 0.875rem;
  border-radius: 50%;
}
