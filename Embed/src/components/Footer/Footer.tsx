import { useState } from 'react';
import { useLanguage } from '../../context/LanguageContext';
import { motion, AnimatePresence } from 'framer-motion';
import { Type } from 'lucide-react'; // Import Type icon for font size
import styles from './Footer.module.css';
import { ReactComponent as RepdLogo } from '../../assets/Rep\'d-logo-white.svg';
import { TrackerService } from '../../services/tracker.service';
import enFlag from '../../assets/flags/en.png';
import esFlag from '../../assets/flags/es.png';
import htFlag from '../../assets/flags/ht.png';
import hiFlag from '../../assets/flags/hi.png';
import viFlag from '../../assets/flags/vi.png';
import zhFlag from '../../assets/flags/zh.png';
import irFlag from '../../assets/flags/ir.png';
import type { Language } from '../../translations';

const supportedLanguages: Language[] = ['en', 'es', 'ht', 'hi', 'vi', 'zh', 'fa', 'te', 'ar'];

const languageToFlag: Record<Language, { src?: string, alt: string, customText?: string, customBg?: string }> = {
  en: {
    src: enFlag,
    alt: 'US Flag'
  },
  es: {
    src: esFlag,
    alt: 'Spanish Flag'
  },
  ht: {
    src: htFlag,
    alt: 'Haitian Flag'
  },
  hi: {
    src: hiFlag,
    alt: 'Indian Flag'
  },
  vi: {
    src: viFlag,
    alt: 'Vietnamese Flag'
  },
  zh: {
    src: zhFlag,
    alt: 'Chinese Flag'
  },
  fa: {
    src: irFlag,
    alt: 'Iran Flag'
  },
  te: {
    alt: 'Telugu',
    customText: 'తె',
    customBg: '#6B46C1'
  },
  ar: {
    alt: 'Arabic',
    customText: 'عربي',
    customBg: '#10B981'
  }
};

// Font size options with actual rem values
const fontSizeOptions = [
  { value: 'small', label: 'Small', size: '0.875' },
  { value: 'medium', label: 'Medium', size: '1' },
  { value: 'large', label: 'Large', size: '1.25' }
];

// Calculate positions for bubble cloud layout
const getBubblePosition = (index: number, total: number) => {
  const angle = (index / total) * 2 * Math.PI;
  const radius = 90; // Increased radius for more spacing
  return {
    x: Math.cos(angle) * radius,
    y: Math.sin(angle) * radius
  };
};

export function Footer() {
  const [isLangOpen, setIsLangOpen] = useState(false);
  const [isFontOpen, setIsFontOpen] = useState(false);
  const [fontSize, setFontSize] = useState(() => {
    // Initialize from localStorage or default to 'medium'
    return localStorage.getItem('fontSizePreference') || 'medium';
  });
  const { currentLanguage, setLanguage, t } = useLanguage();

  const handleFontSizeChange = (size: string) => {
    // Track font size selection
    const trackerService = new TrackerService();
    trackerService.sendStats({
      event_action: 'font_size_change',
      event_category: 'Engagement',
      event_label: `Font Size Changed to ${size}`,
      screenName: 'Marvin Embed Font Size Selection',
      referrer: document.referrer
    });

    setFontSize(size);

    // Store preference in localStorage
    localStorage.setItem('fontSizePreference', size);

    // Find the selected option to get the rem value
    const selectedOption = fontSizeOptions.find(option => option.value === size);
    if (selectedOption) {
      // Set the font-size on the :root element
      document.documentElement.style.fontSize = `${selectedOption.size}rem`;
    }

    setIsFontOpen(false);
  };

  const handleLanguageChange = (lang: Language) => {
    // Track language selection
    const trackerService = new TrackerService();
    trackerService.sendStats({
      event_action: 'language_change',
      event_category: 'Engagement',
      event_label: `Language Changed to ${lang}`,
      screenName: 'Marvin Embed Language Selection',
      referrer: document.referrer
    });

    setLanguage(lang);
    setIsLangOpen(false);
  };

  return (
    <div className={styles.container}>
      <div className={styles.logoContainer}>
        <RepdLogo
          className={styles.logo}
          title="Rep'd Logo"
          role="img"
          aria-label="Rep'd Logo"
        />
        <span>{t('poweredBy')}</span>
      </div>
      <div className={styles.controlsContainer}>
        <div className={styles.fontSizeSelector}>
          <button
            className={styles.selectorButton}
            onClick={() => {
              setIsFontOpen(!isFontOpen);
              setIsLangOpen(false);
            }}
          >
            <div className={styles.fontIconContainer}>
              <Type size={16} color="var(--primary-color)" />
            </div>
          </button>

          <AnimatePresence>
            {isFontOpen && (
              <motion.div
                className={styles.fontSizeMenu}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                {fontSizeOptions.map((option) => (
                  <motion.button
                    key={option.value}
                    className={`${styles.fontSizeOption} ${fontSize === option.value ? styles.active : ''}`}
                    onClick={() => handleFontSizeChange(option.value)}
                    whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                  >
                    {option.label}
                  </motion.button>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        <div className={styles.languageSelector}>
          <button
            className={styles.selectorButton}
            onClick={() => {
              setIsLangOpen(!isLangOpen);
              setIsFontOpen(false);
            }}
          >
            <div className={styles.flagContainer}>
              {languageToFlag[currentLanguage].src ? (
                <img
                  src={languageToFlag[currentLanguage].src}
                  alt={languageToFlag[currentLanguage].alt}
                  className={styles.flag}
                />
              ) : (
                <div
                  className={styles.customLanguageButton}
                  style={{ backgroundColor: languageToFlag[currentLanguage].customBg }}
                >
                  {languageToFlag[currentLanguage].customText}
                </div>
              )}
            </div>
          </button>

          <AnimatePresence>
            {isLangOpen && (
              <motion.div
                className={styles.bubbleCloud}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                {supportedLanguages.map((lang, index) => {
                  const position = getBubblePosition(index, supportedLanguages.length);
                  return (
                    <motion.button
                      key={lang}
                      className={styles.bubbleOption}
                      onClick={() => handleLanguageChange(lang)}
                      initial={{ scale: 0, x: 0, y: 0 }}
                      animate={{
                        scale: 1,
                        x: position.x,
                        y: position.y,
                      }}
                      exit={{ scale: 0, x: 0, y: 0 }}
                      transition={{
                        type: "spring",
                        stiffness: 300,
                        damping: 20,
                        delay: index * 0.05
                      }}
                      whileHover={{ scale: 1.2 }}
                    >
                      <div className={styles.bubbleFlagContainer}>
                        {languageToFlag[lang].src ? (
                          <img
                            src={languageToFlag[lang].src}
                            alt={languageToFlag[lang].alt}
                            className={styles.bubbleFlag}
                          />
                        ) : (
                          <div
                            className={styles.customLanguageBubble}
                            style={{ backgroundColor: languageToFlag[lang].customBg }}
                          >
                            {languageToFlag[lang].customText}
                          </div>
                        )}
                      </div>
                    </motion.button>
                  );
                })}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}
