import React, { useRef, useEffect } from 'react';
import classNames from 'classnames';
import { useClientSvgColors } from '../../utils/svgUtils';
import type { ClientInterface } from '../../interfaces/client.interface';

interface SvgIconProps extends React.SVGProps<SVGSVGElement> {
  icon: React.FC<React.SVGProps<SVGSVGElement>>;
  title?: string;
  description?: string;
  className?: string;
  client?: ClientInterface | null;
}

export const SvgIcon: React.FC<SvgIconProps> = ({
  icon: Icon,
  title,
  description,
  className,
  client,
  ...props
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const uniqueId = `svg-${Math.random().toString(36).substr(2, 9)}`;
  const titleId = title ? `${uniqueId}-title` : undefined;
  const descId = description ? `${uniqueId}-desc` : undefined;

  const ariaLabelledBy = [titleId, descId].filter(Boolean).join(' ') || undefined;

  // Apply client-specific customizations to the SVG
  useClientSvgColors(svgRef, client);

  return (
    <Icon
      ref={svgRef}
      className={classNames('svg-icon', className)}
      aria-labelledby={ariaLabelledBy}
      role="img"
      {...props}
    >
      {title && <title id={titleId}>{title}</title>}
      {description && <desc id={descId}>{description}</desc>}
    </Icon>
  );
};