
import { AnimatePresence } from 'framer-motion';
import { Header } from '../Header/Header';
import { VideoPlayer } from '../VideoPlayer/VideoPlayer';
import { AnswerView } from '../AnswerView/AnswerView';
import { ContentSection } from '../ContentSection/ContentSection';
import { Footer } from '../Footer/Footer';
import type { Answer } from '../../interfaces/answer.interface';
import type { SessionState } from '../../services/sessionService';
import styles from './MainContent.module.css';
import { useAi } from '../../context/AiContext';

interface MatchingAnswer {
  repd_answer_id: string;
  // Add any other fields that might be in the response
}

interface MainContentProps {
  session: SessionState | null;
  selectedVideo: Answer | null;
  showAnswer: boolean;
  question: string;
  onMinimize: () => void;
  onVideoSelect: (answer: Answer) => void;
  onAskAgain: () => void;
  onQuestionChange: (value: string) => void;
  onSubmit: (q: string) => void;
}

export function MainContent({
  session,
  selectedVideo,
  showAnswer,
  question,
  onMinimize,
  onVideoSelect,
  onAskAgain,
  onQuestionChange,
  onSubmit
}: MainContentProps) {
  const { answer, matchingAnswers, isLoading } = useAi();

  return (
    <div className={styles.innerContainer}>
      {!selectedVideo && (
        <Header 
          onMinimize={onMinimize} 
          onBack={onAskAgain}
          showBackButton={showAnswer}
        />
      )}
      <AnimatePresence mode="wait">
        {selectedVideo ? (
          <VideoPlayer
            video={{
              ...selectedVideo,
              // Ensure these properties are explicitly passed
              showTranscribedSubtitles: selectedVideo.showTranscribedSubtitles || true,
              transcription: selectedVideo.transcription || null,
              transcriptionTranslation: selectedVideo.transcriptionTranslation || null
            }}
            onClose={() => {
              // When closing the video, maintain the showAnswer state
              onVideoSelect(null);
            }}
            fromAnswerView={showAnswer}
          />
        ) : showAnswer ? (
          <AnswerView 
            question={question}
            answer={answer.response_text}
            sources={answer.sources || []}
            clientId={session?.client.id}
            userId={session?.user.id}
            onVideoSelect={(video) => {
              // When selecting a video from answer view, remember where we came from
              onVideoSelect(video);
            }}
            onAskAgain={onAskAgain}
            client={session?.client}
            matchingAnswers={matchingAnswers}
            onQuestionChange={onQuestionChange}
            onSubmit={onSubmit}
          />
        ) : (
          <ContentSection 
            isLoading={isLoading}
            session={session}
            question={question}
            onQuestionChange={onQuestionChange}
            onSubmit={onSubmit}
            onVideoSelect={onVideoSelect}
          />
        )}
      </AnimatePresence>
      {!selectedVideo && <Footer />}
    </div>
  );
}
