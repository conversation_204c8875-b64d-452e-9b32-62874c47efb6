import { AnimatePresence, motion } from 'framer-motion';
import { CityIllustration } from '../CityIllustration/CityIllustration';
import { QuestionInput } from '../QuestionInput/QuestionInput';
import { PopularAnswers } from '../PopularAnswers/PopularAnswers';
import type { Answer } from '../../interfaces/answer.interface';
import type { SessionState } from '../../services/sessionService';
import styles from './ContentSection.module.css';

interface ContentSectionProps {
  isLoading: boolean;
  session: SessionState | null;
  question: string;
  onQuestionChange: (value: string) => void;
  onSubmit: () => void;
  onVideoSelect: (answer: Answer) => void;
}

export function ContentSection({
  isLoading,
  session,
  question,
  onQuestionChange,
  onSubmit,
  onVideoSelect
}: ContentSectionProps) {
  return (
    <>
      <AnimatePresence>
        {!isLoading && (
          <motion.div
            initial={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <CityIllustration client={session?.client || null} />
          </motion.div>
        )}
      </AnimatePresence>
      <div className={styles.content}>
        <QuestionInput
          value={question}
          onChange={onQuestionChange}
          isLoading={isLoading}
          onSubmit={onSubmit}
          client={session?.client || null}
        />
        {session && (
          <AnimatePresence>
            {!isLoading && (
              <motion.div
                initial={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <PopularAnswers onVideoSelect={onVideoSelect} client={session?.client || null} />
              </motion.div>
            )}
          </AnimatePresence>
        )}
      </div>
    </>
  );
}