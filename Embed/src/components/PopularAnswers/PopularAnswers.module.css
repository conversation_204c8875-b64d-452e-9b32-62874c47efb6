.container {
  /* margin-top: 1.5rem; */
  background: var(--popular-answers-gradient, linear-gradient(180deg, rgba(30, 41, 61, 0.75) 0%, #1E293D 100%));
  position: relative;
  left: -22px;
  padding: 20px 20px 10px 22px;
  width: calc(100% + 42px);
  min-height: 120px; /* Title + padding + card height */
  transition: min-height 0.3s ease;
}

.title {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
}

.scrollContainer {
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scroll-behavior: smooth;
  /* padding-bottom: 1rem; */
  margin-left: -1.5rem;
  margin-right: -1.5rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  min-height: 120px; /* Match the height of VideoCard */
  transition: min-height 0.3s ease;
}

.scrollContainer::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.cardContainer {
  display: flex;
  gap: 1rem;
  min-width: max-content;
}

@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.loading {
  text-align: center;
  color: white;
  padding: 2rem;
  height: 149px; /* Match the height of VideoCard */
  display: flex;
  align-items: center;
  justify-content: center;
}

.skeletonContainer {
  display: flex;
  gap: 1rem;
  min-width: max-content;
}

.skeletonCard {
  width: 250px;
  height: 149px;
  border-radius: 0.5rem;
  background: var(--skeleton-gradient, linear-gradient(to right, #2a3a5a 8%, #3a4a6a 18%, #2a3a5a 33%));
  background-size: 800px 104px;
  animation: shimmer 1.5s infinite linear;
  position: relative;
  overflow: hidden;
}

/* Apply the same shrink effect to skeleton cards when question form is expanded */
.skeletonCardShrink {
  width: 200px;
  height: 100px;
  opacity: 0.85;
}

.error {
  text-align: center;
  color: var(--error-color, #e94e77);
  padding: 2rem;
}

.seeMoreButton {
  position: absolute;
  right: 15px;
  top: 15px;
  background: var(--primary-color, #407fd2);
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 1rem;
  color: var(--text-on-primary, white) !important;
}

.blackText {
  color: black !important;
}

.headerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.scrollWrapper {
  position: relative;
  width: 100%;
}

.navArrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
  opacity: 0;
  animation: fadeIn 0.2s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.navArrow:hover {
  background-color: rgba(233, 78, 119, 0.8);
}

.leftArrow {
  left: 8px;
}

.rightArrow {
  right: 8px;
}
