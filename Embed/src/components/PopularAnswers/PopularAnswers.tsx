
import { useState, useEffect, useMemo, useRef } from 'react';
import { Answer } from '../../interfaces/answer.interface';
import { getVideoAnswers } from '../../services/answerService';
import { VideoCard } from '../VideoCard/VideoCard';
import { useLanguage } from '../../context/LanguageContext';
import { useAi } from '../../context/AiContext';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import styles from './PopularAnswers.module.css';
import classNames from 'classnames';
import { clientIdToName } from '../../config/clientLogoConfig';
import { normalizeId } from '../../utils/idUtils';
import { ClientInterface } from '../../interfaces/client.interface';

// Stop words to filter out when analyzing word overlap (same as in openai.ts)
const STOP_WORDS = [
  'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'about', 'as',
  'what', 'is', 'are', 'where', 'when', 'how', 'why', 'can', 'do', 'does', 'which', 'who', 'whom', 'whose',
  'a', 'an', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'will', 'would', 'could', 'should',
  'this', 'that', 'these', 'those', 'there', 'here', 'then', 'than', 'from', 'into', 'out', 'up', 'down'
];

/**
 * Calculates word overlap between question and transcription text
 * Returns a score between 0 and 1 representing the percentage of question words found in transcription
 * Same logic as in openai.ts
 */
const calculateWordOverlap = (question: string, transcription: string): number => {
  if (!question || !transcription) return 0;

  // Normalize and extract meaningful words from question
  const questionWords = question
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 2) // Filter out very short words
    .filter(word => !STOP_WORDS.includes(word));

  if (questionWords.length === 0) return 0;

  // Normalize transcription text
  const transcriptionText = transcription.toLowerCase().replace(/[^\w\s]/g, ' ');

  // Count how many question words appear in transcription
  const matchingWords = questionWords.filter(word =>
    transcriptionText.includes(word)
  );

  const overlapScore = matchingWords.length / questionWords.length;
  return overlapScore;
};

interface PopularAnswersProps {
  onVideoSelect: (answer: Answer) => void;
  question?: string;
  titleKey?: 'popularAnswers' | 'relatedAnswers';
  isQuestionFormExpanded?: boolean;
  client?: ClientInterface | null;
}

export function PopularAnswers({
  onVideoSelect,
  question,
  titleKey = 'popularAnswers',
  isQuestionFormExpanded = false,
  client = null
}: PopularAnswersProps) {
  const { matchingAnswers, isLoading: aiLoading } = useAi();
  const [popularAnswers, setPopularAnswers] = useState<Answer[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useLanguage();
  const [initialLoadDone, setInitialLoadDone] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const [isHovering, setIsHovering] = useState(false);
  const [clientSubdomain, setClientSubdomain] = useState('');

  const clientId = localStorage.getItem('clientId');

  // Get client name from URL or localStorage
  useEffect(() => {
    // Extract client name from URL path
    const pathMatch = window.location.pathname.match(/\/([^\/]+)/);
    const clientNameFromUrl = pathMatch ? pathMatch[1].toLowerCase() : null;

    // Get client ID from localStorage
    const clientId = localStorage.getItem('clientId');

    // Determine subdomain: first try from URL, then from client ID mapping using centralized mapping
    const subdomain = clientNameFromUrl ||
                     (clientId && clientIdToName[clientId]) ||
                     'arlington';

    setClientSubdomain(subdomain);
  }, []);

  // Load initial answers only once
  useEffect(() => {
    const loadInitialAnswers = async () => {
      if (initialLoadDone) {
        return;
      }

      try {
        setIsLoading(true);
        const clientId = localStorage.getItem('clientId');
        const userId = localStorage.getItem('userId');

        if (!clientId || !userId) {
          return;
        }

        const initialAnswers = await getVideoAnswers(clientId, userId);
        setPopularAnswers(initialAnswers);
        setInitialLoadDone(true);
      } catch (err) {
        console.error('Failed to load answers:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialAnswers();
  }, [initialLoadDone]);

  const displayAnswers = useMemo(() => {
    if (titleKey === 'relatedAnswers' && matchingAnswers?.length > 0 && question) {
      // Convert matching answer IDs to full Answer objects
      const matchingIds = matchingAnswers.map(ma => normalizeId(ma.repd_answer_id));
      const matchedAnswers = popularAnswers.filter(answer => {
        const answerId = normalizeId(answer.id);
        return matchingIds.includes(answerId);
      });

      // Sort matched answers by relevance using word overlap scoring
      const sortedMatchedAnswers = matchedAnswers.sort((a, b) => {
        // Get transcription text for overlap calculation
        const getTranscriptionText = (answer: Answer): string => {
          if (!answer.transcription) return '';

          // Try audio_segments first (preferred format)
          if (answer.transcription.audio_segments && answer.transcription.audio_segments.length > 0) {
            return answer.transcription.audio_segments
              .map(segment => segment.transcript || '')
              .join(' ');
          }

          // Fallback to items format
          if (answer.transcription.items && answer.transcription.items.length > 0) {
            return answer.transcription.items
              .filter(item => item.type === 'pronunciation')
              .map(item => item.alternatives[0]?.content || '')
              .join(' ');
          }

          return '';
        };

        const transcriptionA = getTranscriptionText(a);
        const transcriptionB = getTranscriptionText(b);

        const overlapA = calculateWordOverlap(question, transcriptionA);
        const overlapB = calculateWordOverlap(question, transcriptionB);

        // Sort by overlap score descending (higher relevance first)
        return overlapB - overlapA;
      });

      // Add remaining popular answers that weren't matched
      const remainingAnswers = popularAnswers.filter(answer => {
        const answerId = normalizeId(answer.id);
        return !matchingIds.includes(answerId);
      });

      return [...sortedMatchedAnswers, ...remainingAnswers];
    }

    // For popular answers section, ensure pinned items are at the top
    return [...popularAnswers].sort((a, b) => {
      // First sort by isPinned (pinned items first)
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;

      // Keep original order for the rest
      return 0;
    });
  }, [titleKey, matchingAnswers, popularAnswers, question]);

  // Create skeleton loading cards
  const renderSkeletonCards = () => {
    // Create an array of 4 skeleton cards
    return Array(4).fill(0).map((_, index) => (
      <div
        key={`skeleton-${index}`}
        className={`${styles.skeletonCard} ${isQuestionFormExpanded ? styles.skeletonCardShrink : ''}`}
      />
    ));
  };

  const handleScroll = () => {
    if (!scrollContainerRef.current) return;
    
    const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
    setShowLeftArrow(scrollLeft > 0);
    setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10);
  };

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      // Check initial scroll state
      handleScroll();
    }
    
    return () => {
      scrollContainer?.removeEventListener('scroll', handleScroll);
    };
  }, [displayAnswers]);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -250, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 250, behavior: 'smooth' });
    }
  };

  if (initialLoadDone && (!displayAnswers || displayAnswers.length === 0)) {
    return <></>;
  }

  return (
    <div 
      className={styles.container}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <div className={styles.headerContainer}>
        <h2 className={styles.title}>{t(titleKey)}</h2>
        {client?.clientType !== "Internal" && (
          <a
            href={`https://app.repd.us/${clientSubdomain}`}
            target="_blank"
            rel="noopener noreferrer"
            className={classNames(styles.seeMoreButton, clientId === "300" && styles.blackText)} // Black text for Pleasantville
            id="seeMoreButton"
          >
            {t('seeMoreVideos')}
          </a>
        )}
      </div>
      <div className={styles.scrollWrapper}>
        {showLeftArrow && isHovering && (
          <button 
            className={`${styles.navArrow} ${styles.leftArrow}`}
            onClick={scrollLeft}
            aria-label="Scroll left"
          >
            <ChevronLeft size={20} />
          </button>
        )}
        <div 
          ref={scrollContainerRef}
          className={styles.scrollContainer}
        >
          {isLoading || aiLoading ? (
            <div className={styles.skeletonContainer}>
              {renderSkeletonCards()}
            </div>
          ) : (
            <div className={styles.cardContainer}>
              {displayAnswers.map((answer) => (
                <VideoCard
                  key={answer.id}
                  answer={answer}
                  onClick={() => onVideoSelect(answer)}
                  isQuestionFormExpanded={isQuestionFormExpanded}
                />
              ))}
            </div>
          )}
        </div>
        {showRightArrow && isHovering && (
          <button 
            className={`${styles.navArrow} ${styles.rightArrow}`}
            onClick={scrollRight}
            aria-label="Scroll right"
          >
            <ChevronRight size={20} />
          </button>
        )}
      </div>
    </div>
  );
}
