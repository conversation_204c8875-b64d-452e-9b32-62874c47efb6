import { useState, useEffect } from 'react';
import styles from './PasswordProtection.module.css';

interface PasswordProtectionProps {
  children: React.ReactNode;
  storageKey?: string;
}

const CORRECT_PASSWORD = 'RepdNYC123';

export function PasswordProtection({ children, storageKey = 'adminPassword' }: PasswordProtectionProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if password is already stored in localStorage
    const storedPassword = localStorage.getItem(storageKey);
    if (storedPassword === CORRECT_PASSWORD) {
      setIsAuthenticated(true);
    }
    setIsLoading(false);
  }, [storageKey]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (password === CORRECT_PASSWORD) {
      localStorage.setItem(storageKey, password);
      setIsAuthenticated(true);
      setError('');
    } else {
      setError('Incorrect password');
      setPassword('');
    }
  };



  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.loadingSpinner}>Loading...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className={styles.container}>
        <div className={styles.loginForm}>
          <h2 className={styles.title}>Access Protected</h2>
          <p className={styles.subtitle}>Please enter the password to continue</p>
          
          <form onSubmit={handleSubmit} className={styles.form}>
            <div className={styles.inputGroup}>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password"
                className={styles.passwordInput}
                autoFocus
              />
            </div>
            
            {error && <div className={styles.error}>{error}</div>}
            
            <button type="submit" className={styles.submitButton}>
              Access
            </button>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.authenticatedContainer}>
      {children}
    </div>
  );
}
