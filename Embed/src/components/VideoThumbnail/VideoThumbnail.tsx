import { Play } from 'lucide-react';
import styles from './VideoThumbnail.module.css';
import { useLanguage } from '../../context/LanguageContext';
import type { Answer } from '../../interfaces/answer.interface';

interface VideoThumbnailProps {
  answer: Answer;
  onClick: () => void;
}

export function VideoThumbnail({ answer, onClick }: VideoThumbnailProps) {
  const { translateContent } = useLanguage();

  const translatedTitle = translateContent({
    text: answer.question.text,
    translations: answer.question.translations,
    originalLanguage: answer.question.originalLanguage
  });

  return (
    <div className={styles.container} onClick={onClick}>
      <div className={styles.thumbnailWrapper}>
        <img
          src={answer.thumbnail}
          alt="Background"
          className={styles.thumbnailBackground}
        />
        <img
          src={answer.thumbnail}
          alt={translatedTitle}
          className={styles.thumbnail}
        />
        <div className={styles.overlay}>
          <div className={styles.playButtonWrapper}>
            <Play className={styles.playIcon} fill="white" strokeWidth={0} />
          </div>
        </div>
        <div className={styles.titleContainer}>
          <h4 className={styles.title}>{translatedTitle}</h4>
        </div>
      </div>
    </div>
  );
}
