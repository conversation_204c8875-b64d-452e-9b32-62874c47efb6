
.container {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.container:hover {
  transform: translateY(-2px);
}

.thumbnailWrapper {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
}

.thumbnailBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(8px);
  transform: scale(1.1);
  z-index: 0;
}

.thumbnail {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  z-index: 1;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
  z-index: 2;
}

.container:hover .overlay {
  background-color: rgba(0, 0, 0, 0.4);
}

.playButtonWrapper {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.container:hover .playButtonWrapper {
  background-color: #e94e77;
  transform: scale(1.1);
}

/* Emporia client override */
:global(.client-283) .container:hover .playButtonWrapper {
  background-color: #fca127;
}

.playIcon {
  width: 24px;
  height: 24px;
}

.titleContainer {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  padding: 0.75rem;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8), transparent);
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease;
  z-index: 3;
}

.title {
  color: white;
  font-weight: 500;
  font-size: 1.15rem;
  margin: 0;
}
