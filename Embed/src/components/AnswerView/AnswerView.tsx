import { motion } from 'framer-motion';
import { VideoCard } from '../VideoCard/VideoCard';
import { PopularAnswers } from '../PopularAnswers/PopularAnswers';
import { PostNewQuestionExtension } from '../PostNewQuestionExtension/PostNewQuestionExtension';
import type { Answer } from '../../interfaces/answer.interface';
import { useLanguage } from '../../context/LanguageContext';
import styles from './AnswerView.module.css';
import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { TrackerService } from '../../services/tracker.service';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExternalLinkAlt } from '@fortawesome/free-solid-svg-icons';
import type { ClientInterface } from '../../interfaces/client.interface';
import { formatSourceUrl, extractSourcesFromAnswer } from '../../helpers/responseHelpers';
import { Send } from 'lucide-react';
import { fillerWords } from '../../utils/fillerWords';
import { VideoThumbnail } from '../VideoThumbnail/VideoThumbnail';
import { getVideoAnswers } from '../../services/answerService';
import { normalizeId, idsMatch } from '../../utils/idUtils';

interface Source {
  url: string;
  score: number;
}

interface MatchingAnswer {
  repd_answer_id: string;
  // Add any other fields that might be in the response
}

interface AnswerViewProps {
  question: string;
  answer: string;
  sources?: Source[];
  clientId: string;
  userId?: string;
  onVideoSelect: (answer: Answer) => void;
  onAskAgain: () => void;
  client: ClientInterface;
  matchingAnswers?: MatchingAnswer[]; // Updated to use the correct structure
  onQuestionChange: (value: string) => void;
  onSubmit: (q: string) => void;
}

export function AnswerView({
  question,
  answer,
  sources = [],
  clientId,
  userId,
  onVideoSelect,
  onAskAgain,
  client,
  matchingAnswers = [], // Now an array of objects with repd_answer_id
  onQuestionChange,
  onSubmit
}: AnswerViewProps) {
  const { t } = useLanguage();
  const trackerService = new TrackerService();
  const scrollRef = useRef<HTMLDivElement>(null);
  const [showTopShadow, setShowTopShadow] = useState(false);
  const [showBottomShadow, setShowBottomShadow] = useState(false);
  const [isQuestionFormExpanded, setIsQuestionFormExpanded] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedQuestion, setEditedQuestion] = useState(question);
  const [popularAnswers, setPopularAnswers] = useState<Answer[]>([]);

  // Add this utility function at the top of the component
  const formatSourceUrl = (url: string): string => {
    try {
      const urlObj = new URL(url);
      // Remove 'www.' from hostname
      const hostname = urlObj.hostname.replace(/^www\./, '');

      // Get pathname without query parameters and remove common prefixes
      let pathname = urlObj.pathname
        .replace(/^\/files\/assets\//, '')
        .replace(/^\/city\//, '')
        .replace(/^\/v\/\d+\//, '');

      // If pathname is too long, try to get just the filename
      if (pathname.length > 50) {
        pathname = pathname.split('/').pop() || pathname;
      }

      return `${hostname}${pathname}`;
    } catch {
      // If URL parsing fails, return original
      return url;
    }
  };

  // Add this function to extract keywords from text
  const extractKeywords = (text: string): string[] => {
    return text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3 && !fillerWords.includes(word)); // Filter out filler words
  };

  // Parse categories from client with null check
  const categories = client?.categories
    ? client.categories
        .replace(/\s+/g, ' ')
        .replace(/, /g, ',')
        .replace(/ ,/g, ',')
        .replace(/^ /, '')
        .replace(/ $/, '')
        .split(',')
        .filter(Boolean) // Filter out empty strings
    : [];

  const handleScroll = useCallback(() => {
    if (!scrollRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
    setShowTopShadow(scrollTop > 0);
    setShowBottomShadow(scrollTop < scrollHeight - clientHeight - 1);
  }, []);

  useEffect(() => {
    const scrollElement = scrollRef.current;
    if (scrollElement) {
      handleScroll(); // Check initial state
      scrollElement.addEventListener('scroll', handleScroll);
    }
    return () => {
      scrollElement?.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  // Extract URLs from answer text if no sources provided
  const extractedSources = useMemo(() =>
    extractSourcesFromAnswer(answer, sources), [answer, sources]
  );

  // Add this effect to fetch popular answers
  useEffect(() => {
    const fetchPopularAnswers = async () => {
      try {
        if (!clientId || !userId) return;
        const answers = await getVideoAnswers(clientId, userId);
        setPopularAnswers(answers);
        console.log('Fetched popular answers:', answers);
      } catch (error) {
        console.error('Failed to fetch popular answers:', error);
      }
    };

    fetchPopularAnswers();
  }, [clientId, userId]);

  // Find exact question matches
  const exactMatches = useMemo(() => {
    // Skip processing if data isn't ready
    if (popularAnswers.length === 0) {
      console.log('No popular answers available yet, skipping matching');
      return [];
    }

    console.log('Matching answers from AI:', JSON.stringify(matchingAnswers, null, 2));
    console.log('Current question:', question);
    console.log('Popular answers count:', popularAnswers.length);

    // If no matching answers from AI, try keyword matching
    // if (!matchingAnswers || matchingAnswers.length === 0) {
    //   console.log('No matching answers from AI, trying keyword matching');

    //   // Extract keywords from question
    //   const keywords = extractKeywords(question);
    //   console.log('Keywords from question:', keywords);

    //   if (keywords.length > 0) {
    //     // Find answers that contain any of the keywords in their question text
    //     const keywordMatches = popularAnswers.filter(answer => {
    //       const answerText = answer.question.text.toLowerCase();
    //       return keywords.some(keyword => answerText.includes(keyword));
    //     });

    //     console.log('Keyword matches:', keywordMatches);

    //     // Sort by number of matching keywords (most matches first)
    //     if (keywordMatches.length > 0) {
    //       const sortedMatches = keywordMatches.sort((a, b) => {
    //         const aMatches = keywords.filter(kw => a.question.text.toLowerCase().includes(kw)).length;
    //         const bMatches = keywords.filter(kw => b.question.text.toLowerCase().includes(kw)).length;
    //         return bMatches - aMatches;
    //       });

    //       console.log('Sorted keyword matches:', sortedMatches);
    //       return sortedMatches.slice(0, 3); // Return top 3 matches
    //     }
    //   }

    //   // If no keyword matches, return empty array
    //   return [];
    // }

    // Extract the repd_answer_ids from matchingAnswers
    const matchingIds = matchingAnswers.map(match => normalizeId(match.repd_answer_id));
    console.log('Matching IDs extracted (normalized):', matchingIds);

    // Log all popular answer IDs for comparison
    const popularAnswerIds = popularAnswers.map(answer => normalizeId(answer.id));
    console.log('All popular answer IDs (normalized):', popularAnswerIds);

    // Check for any intersection between the two ID sets
    const commonIds = matchingIds.filter(id => popularAnswerIds.includes(id));
    console.log('Common IDs found:', commonIds);

    // Filter popular answers to find those with IDs in the matchingIds array
    // Use more explicit type conversion and comparison
    const matchedAnswers = popularAnswers.filter(answer => {
      const answerId = normalizeId(answer.id);
      const isMatch = matchingIds.includes(answerId);
      console.log(`Checking answer ID ${answerId} against matching IDs:`, isMatch);
      return isMatch;
    });

    console.log('Matched answers by ID:', matchedAnswers);

    // If we have matches by ID, return those
    if (matchedAnswers.length > 0) {
      return matchedAnswers;
    }

    // Fallback to text matching if no ID matches found
    const textMatches = popularAnswers.filter(a => {
      // First check for exact matches
      const normalizedAnswer = a.question.text.toLowerCase().trim();
      const normalizedQuestion = question.toLowerCase().trim();

      // Exact match check
      return normalizedAnswer === normalizedQuestion;
    });

    console.log('Text matches (fallback):', textMatches);
    return textMatches;
  }, [matchingAnswers, popularAnswers, question]);

  // Add this function to handle question submission
  const handleQuestionSubmit = () => {
    if (editedQuestion.trim() !== '') {
      onQuestionChange(editedQuestion);
      onSubmit(editedQuestion);
      setIsEditing(false);
    }
  };

  // Add this function to process the answer HTML and add target="_blank" to all links
  const processAnswerHtml = (html: string): string => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Find all anchor tags and add target="_blank" and rel="noreferrer"
    const anchors = tempDiv.getElementsByTagName('a');
    for (let i = 0; i < anchors.length; i++) {
      anchors[i].setAttribute('target', '_blank');
      anchors[i].setAttribute('rel', 'noreferrer');
    }

    return tempDiv.innerHTML;
  };

  return (
    <motion.div
      className={styles.container}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <div className={styles.questionCard}>
        {isEditing ? (
          <div className={styles.questionInputWrapper}>
            <textarea
              className={styles.questionInput}
              value={editedQuestion}
              onChange={(e) => setEditedQuestion(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleQuestionSubmit();
                }
              }}
              placeholder="Edit your question..."
              autoFocus
              onFocus={(e) => {
                // Move cursor to the end of the text
                const length = e.target.value.length;
                e.target.setSelectionRange(length, length);
              }}
            />
            <button
              className={styles.sendButton}
              onClick={handleQuestionSubmit}
            >
              <Send size={16} />
            </button>
          </div>
        ) : (
          <div
            className={styles.questionBar}
            onClick={() => setIsEditing(true)}
          >
            {question}
          </div>
        )}
      </div>

      <div className={styles.disclaimerText}>
        DISCLAIMER: AI may produce inaccurate information about people, places or facts.
      </div>

      <div className={`${styles.scrollableWrapper}
        ${showTopShadow ? styles.showTopShadow : ''}
        ${showBottomShadow ? styles.showBottomShadow : ''}`}
      >
        <div
          ref={scrollRef}
          className={`${styles.scrollableContent} ${exactMatches.length > 0 ? styles.expandedHeight : ''}`}
        >
          {exactMatches.length > 0 && (
            <div className={styles.bestMatchContainer}>
              <VideoThumbnail
                answer={exactMatches[0]}
                onClick={() => onVideoSelect(exactMatches[0])}
              />
            </div>
          )}

          <div className={styles.answerCard}>
            <div
              className={styles.answerContainer}
              dangerouslySetInnerHTML={{ __html: processAnswerHtml(answer) }}
            />

            <button
              className={styles.anotherQuestionButton}
              onClick={() => {
                // Same functionality as clicking the questionBar - open the question input for editing
                setIsEditing(true);
              }}
            >
              Have another question?
            </button>
          </div>

          {extractedSources.length > 0 && (
            <div className={styles.sourcesCard}>
              <h3 className={styles.sourcesTitle}>{t("sources")}</h3>
              <ol className={styles.sourcesList}>
                {extractedSources
                  .filter(source => !source.url.includes('repd-api-files'))
                  .filter(source => source.url.startsWith('http'))
                  .map((source, index) => (
                    <li key={index} className={styles.sourceItem}>
                      <a
                        href={source.url}
                        target="_blank"
                        rel="noreferrer"
                        className={styles.sourceLink}
                        onClick={() => trackerService.sendStats({
                          event_action: 'clickSource',
                          event_category: 'Source',
                          event_label: source.url
                        })}
                      >
                        {formatSourceUrl(source.url)}
                        <FontAwesomeIcon
                          icon={faExternalLinkAlt}
                          className={styles.externalIcon}
                        />
                      </a>
                      {source.text && (
                        <p className={styles.sourceText}>{source.text}</p>
                      )}
                    </li>
                  ))}
              </ol>
            </div>
          )}
        </div>
      </div>

      <PostNewQuestionExtension
        question={question}
        clientId={clientId}
        userId={userId}
        onClose={onAskAgain}
        categories={categories}
        client={client}
        onToggleExpand={setIsQuestionFormExpanded}
      />

      {exactMatches.length === 0 && (
        <div className={styles.relatedAnswers}>
          <PopularAnswers
            onVideoSelect={onVideoSelect}
            question={question}
            titleKey="relatedAnswers"
            isQuestionFormExpanded={isQuestionFormExpanded}
            client={client}
          />
        </div>
      )}
    </motion.div>
  );
}
