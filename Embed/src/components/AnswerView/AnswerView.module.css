.container {
  padding: 1rem 1rem 0rem 1rem;
}

.questionCard {
  background: white;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.questionBar {
  background: white;
  padding: 1rem 1.25rem;
  border-radius: 0.75rem;
  font-size: 1rem;
  color: #1e2942;
  cursor: pointer;
  transition: background-color 0.2s;
}

.questionBar:hover {
  background: #E5E7EB;
}

.scrollableWrapper {
  position: relative;
  margin-left: -0.9rem;
}

.scrollableWrapper::before,
.scrollableWrapper::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 40px;
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.scrollableWrapper::before {
  top: 0;
  background: var(--scroll-fade-gradient, linear-gradient(to bottom, #30466F 0%, rgba(48, 70, 111, 0) 100%));
  width: calc(100% + 0.9rem);
}

.scrollableWrapper::after {
  bottom: 0;
  background: var(--scroll-fade-gradient-bottom, linear-gradient(to top, #30466F 0%, rgba(48, 70, 111, 0) 100%));
  width: calc(100% + 0.9rem);
}

.scrollableWrapper.showTopShadow::before {
  opacity: 0.5;
}

.scrollableWrapper.showBottomShadow::after {
  opacity: 0.5;
}

.scrollableContent {
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  position: relative;
  width: calc(100% + 0.9rem);
  border-radius: 8px;
  transition: max-height 0.3s ease;
}

.scrollableContent.expandedHeight {
  max-height: 460px;
}

.scrollableContent::-webkit-scrollbar {
  width: 6px;
}

.scrollableContent::-webkit-scrollbar-track {
  background: transparent;
}

.scrollableContent::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.answerCard {
  background: white;
  padding: 1.5rem;
}

.answerContainer {
  color: #1e2942;
  line-height: 1.5;
}

.answerContainer p {
  margin-bottom: 1rem;
}

.answerContainer a {
  color: var(--primary-color, #2563eb);
  text-decoration: underline;
}

.answerContainer ul {
  list-style-type: disc;
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.answerContainer ol {
  list-style-type: decimal;
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.answerContainer li {
  margin-bottom: 0.5rem;
  display: list-item; /* Ensures the list item marker is visible */
}

.anotherQuestionButton {
  margin-top: 1.5rem;
  padding: 1rem;
  background: rgb(242, 246, 255);
  border: 1px solid rgb(219, 230, 255);
  border-radius: 0.5rem;
  color: rgb(59, 92, 162);
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  width: 100%;
  transition: background-color 0.2s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.anotherQuestionButton:hover {
  background: rgb(232, 240, 255);
}

.relatedAnswers {
  /* margin-top: 1rem; */
}

.relatedTitle {
  background-color: #f6f8fa;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin: 1rem;
  border-radius: 0.5rem;
  font-size: 1.25rem;
}

.sourcesCard {
  background: #f2f6ff;
  padding: 1.25rem 1.5rem 0.5rem 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid #dbe6ff;
  margin-top: 20px;
}

.sourcesTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #3b5ca2;
}

.sourceItem {
  margin-bottom: 1rem;
  background: white;
  border-radius: 5px;
  padding: 10px;
  word-break: break-word;
}

.sourceLink {
  display: flex;
  align-items: center;
  color: var(--primary-color, #2563eb);
  text-decoration: none;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.sourceLink:hover {
  text-decoration: underline;
}

.externalIcon {
  margin-left: 0.5rem;
  opacity: 0.5;
  font-size: 0.875rem;
}

.sourceText {
  color: #4b5563;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.exactMatches {
  margin-bottom: 1rem;
  padding: 1rem;
  background: var(--background-secondary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

.questionInputWrapper {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.5rem;
  background-color: white;
  border-radius: 0.5rem;
}

.questionInput {
  flex: 1;
  padding: 0.5rem;
  border: none;
  background: transparent;
  font-size: 1rem;
  resize: none;
  min-height: 24px;
  max-height: 100px;
  overflow-y: auto;
}

.questionInput:focus {
  outline: none;
}

.sendButton {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #006eff;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
}

.sendButton:hover {
  background-color: rgba(0, 110, 255, 0.1);
}

.bestMatchContainer {
  padding: 1rem 1rem 0 1rem;
  background: white;
}

.disclaimerText {
  font-size: 0.6rem;
  font-style: italic;
  color: #FFF;
  margin-bottom: 0.75rem;
  padding: 0 0.5rem;
}
