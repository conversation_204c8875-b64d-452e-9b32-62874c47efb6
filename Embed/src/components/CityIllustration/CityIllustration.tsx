import styles from './CityIllustration.module.css';
import { ReactComponent as CityQuestionIcon } from '../../assets/city-question-icon.svg';
import { SvgIcon } from '../SvgIcon/SvgIcon';
import { getCurrentClientId, getClientLogoUrl, getClientLogoStyles } from '../../config/clientLogoConfig';
import type { ClientInterface } from '../../interfaces/client.interface';

interface CityIllustrationProps {
  client?: ClientInterface | null;
}

export function CityIllustration({ client }: CityIllustrationProps) {
  // Get current client ID using centralized function
  const currentClientId = getCurrentClientId(client);

  // Get logo URL and styles using centralized configuration
  const logoUrl = getClientLogoUrl(currentClientId, client?.logoURL);
  const customLogoStyles = getClientLogoStyles(currentClientId);

  const useCustomLogo = logoUrl !== null;

  return (
    <div className={styles.container}>
      {/* <SkylineSvg
        className={styles.skyline}
        title="City Skyline"
        role="img"
        aria-label="City Skyline"
      /> */}
      <div className={styles.iconContainer}>
        <div className={styles.circle}>
          {useCustomLogo ? (
            <img
              src={logoUrl || ''}
              alt={`${client?.name || currentClientId || 'Client'} Logo`}
              className={styles.icon}
              id="cityQuestionIcon"
              style={customLogoStyles}
            />
          ) : (
            <SvgIcon
              icon={CityQuestionIcon}
              className={styles.icon}
              title="City Question Icon"
              id="cityQuestionIcon"
              aria-label="City Question Icon"
              client={client}
            />
          )}
        </div>
      </div>
    </div>
  );
}
