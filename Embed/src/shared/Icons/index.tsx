import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faBookOpen, 
  faTree, 
  faChild, 
  faHeartbeat,
  faBus,
  faGavel,
  faHome,
  faMobile
} from '@fortawesome/free-solid-svg-icons';

export const IconsDictionaryByCategory: { [key: string]: RegExp } = {
  bookOpen: /Safety|Education|Charter|Educational|Pre(.|)K|School|Student|Teacher/i,
  tree: /Climate|Green|Renewable Energy/i,
  child: /Child|Minor|Childcare|Parenting/i,
  heartbeat: /Health|Healthcare|Addiction|COVID(.|)19|Medicaid|Medicare|Mental|Opioid|Epidemic|Opioid Crisis|Prescrition|Drug/i,
  bus: /Transportation|Accessibility|Infrastructure/i,
  gavel: /Gun|Safety|Criminal|Justice Reform|Incarceration|Bail|Crime|Death|penalty|Legalizing|marijuana|Police|Policing|Prosecutorial|Discretion|Immunity|Conviction/i,
  home: /House|Home|Housing|Evictions|Homeless|Landlord|Tenant/i,
  mobile: /Technology|Broadband access|Online Learning|Regulation/i,
};

const IconsMap = {
  bookOpen: faBookOpen,
  tree: faTree,
  child: faChild,
  heartbeat: faHeartbeat,
  bus: faBus,
  gavel: faGavel,
  home: faHome,
  mobile: faMobile
};

export const Icons: React.FC<{ 
  iconType: string; 
  iconClass?: string; 
  wrapperClass?: string;
}> = ({ iconType, iconClass = '', wrapperClass = '' }) => {
  const getIconType = (category: string): string => {
    return Object.entries(IconsDictionaryByCategory).find(
      ([_, regex]) => regex.test(category)
    )?.[0] || 'bookOpen';
  };

  const icon = IconsMap[getIconType(iconType)];

  return (
    <span className={wrapperClass}>
      <FontAwesomeIcon icon={icon} className={iconClass} />
    </span>
  );
};
