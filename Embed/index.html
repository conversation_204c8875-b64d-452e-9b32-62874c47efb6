<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&display=swap" rel="stylesheet">
    <title>Rep'd - <PERSON> Embed</title>
    <script>
      // Early client detection
      (function() {
        // Check URL path for client name
        const pathMatch = window.location.pathname.match(/\/([^\/]+)/);
        const clientNameFromUrl = pathMatch ? pathMatch[1].toLowerCase() : null;
        
        // Map client names to their IDs
        const clientNameToId = {
          'emporia': '283',
          'page': '295',
          'york': '294',
          'mountjackson': '293',
          'loudoun': '292',
          'parkcity': '288',
          'roelandpark': '287',
          'myrtlebeach': '299',
          'morrisville': '281',
          'traversecity': '269',
          'myrtlebeachprst': '266',
          'tulare': '234',
          'salisbury': '221'
        };
        
        // Determine client ID
        let clientId = localStorage.getItem('clientId');
        
        // If URL contains a known client name, use its ID and store it
        if (clientNameFromUrl && clientNameToId[clientNameFromUrl]) {
          clientId = clientNameToId[clientNameFromUrl];
          localStorage.setItem('clientId', clientId);
        }
        
        // Add client-specific class to html element for CSS targeting
        if (clientId) {
          document.documentElement.classList.add(`client-${clientId}`);
        }
      })();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
