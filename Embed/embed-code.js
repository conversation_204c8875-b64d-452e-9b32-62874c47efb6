(function() {
  const iframe = document.createElement('iframe');
  iframe.id = 'repd-embed';
  
  // Get the current page URL
  const currentPath = window.location.pathname;
  const referral = window.location.href;
  
  // Construct embed URL using current path
  const embedUrl = new URL(`https://embed.repd.us${currentPath}`);
  embedUrl.searchParams.append('referrer', encodeURIComponent(referral));
  
  iframe.src = embedUrl.toString();
  iframe.style.cssText = `
    position: fixed;
    bottom: 0rem;
    right: 0rem;
    border: none;
    z-index: 50;
    transition: all 0.3s ease;
    background: transparent;
    width: 10rem;
    height: 10rem;
    max-height: calc(100vh - 40px);
  `;

  // Handle messages from iframe for resize events
  window.addEventListener('message', (event) => {
    if (event.origin !== 'https://embed.repd.us') return;
    
    const { type, dimensions } = event.data;
    if (type === 'resize') {
      iframe.style.width = dimensions.width;
      iframe.style.height = dimensions.height;
      
      // Ensure smooth transition when height changes
      requestAnimationFrame(() => {
        iframe.style.transition = 'all 0.3s ease';
      });
    }
  });

  document.body.appendChild(iframe);
})();
