# Dynamic Client Colors System

This document describes the new dynamic client colors system that ensures all client colors are properly applied throughout the application.

## Overview

The dynamic colors system replaces static CSS override files with a comprehensive, dynamic approach that:

1. **Automatically applies client colors** from the `ClientInterface` data
2. **Uses CSS variables** for consistent color application
3. **Supports client-specific overrides** for special cases
4. **Eliminates the need for static CSS files** per client
5. **Provides fallback colors** for better reliability

## Architecture

### Core Components

1. **ClientColorService** (`src/services/clientColorService.ts`)
   - Singleton service that manages all client color application
   - Dynamically sets CSS variables based on client data
   - Handles client-specific overrides for special cases

2. **Color Utilities** (`src/utils/colorUtils.ts`)
   - Shared color conversion functions (hex to HSL, HSL to hex, etc.)
   - Color palette generation functions
   - Gradient color creation utilities

3. **Dynamic Colors CSS** (`src/styles/dynamicColors.css`)
   - Global CSS that uses CSS variables for styling
   - Replaces hardcoded colors with variable references
   - Provides fallback values for all color properties

4. **Enhanced Global CSS** (`src/styles/global.css`)
   - Extended CSS variable definitions
   - Default color values that work as fallbacks

## CSS Variables

### Primary Client Colors
- `--primary-color`: Main brand color (from `topBarColour`)
- `--accent-color`: Secondary brand color (from `videoLinksColour`)
- `--new-question-color`: New question button color
- `--plus-ask-pill-color`: Plus ask pill color
- `--open-qa-color`: Open Q&A section color

### Background Colors
- `--background-gradient`: Main background gradient
- `--background-light`: Light background color
- `--background-dark`: Dark background color
- `--popular-answers-gradient`: Popular answers section gradient
- `--scroll-fade-gradient`: Scroll fade effects
- `--skeleton-gradient`: Loading skeleton animation

### Interactive Elements
- `--hover-color`: Hover state color
- `--active-color`: Active state color
- `--focus-color`: Focus state color
- `--error-color`: Error message color
- `--success-color`: Success message color
- `--warning-color`: Warning message color

### Progress Bars
- `--progress-bar-color`: Progress bar fill color
- `--progress-bar-background`: Progress bar background color

### Buttons
- `--button-gradient`: Button gradient background
- `--button-gradient-hover`: Button hover gradient
- `--submit-button-color`: Submit button color
- `--submit-button-hover`: Submit button hover color
- `--submit-button-active`: Submit button active color

### Text Colors
- `--text-on-primary`: Text color on primary backgrounds
- `--text-muted`: Muted text color

## Usage

### Automatic Application

Colors are automatically applied when the session initializes:

```typescript
// In AppContent.tsx
const colorService = ClientColorService.getInstance();
colorService.applyClientColors(sessionData.client);
```

### Manual Color Updates

You can manually update specific colors:

```typescript
const colorService = ClientColorService.getInstance();
colorService.updateColorProperty('--accent-color', '#FF5733');
```

### CSS Usage

Use CSS variables in your stylesheets:

```css
.myButton {
  background-color: var(--primary-color, #2760CF);
  color: var(--text-on-primary, #FFFFFF);
}

.myButton:hover {
  background-color: var(--hover-color, #3A70DF);
}
```

### Component Usage

Components automatically inherit the dynamic colors:

```tsx
// Colors are automatically applied via CSS variables
<button className="submitButton">Submit</button>
```

## Client-Specific Overrides

Some clients require specific color combinations that differ from their base colors:

### Emporia (Client ID: 283)
- Uses blue accent color instead of pink
- Orange progress bar color
- Special error color handling

### Page (Client ID: 295)
- Teal color scheme
- Consistent teal across all elements

### Adding New Client Overrides

To add overrides for a new client:

1. Add a case in `ClientColorService.applyClientSpecificOverrides()`:

```typescript
case '297': // New Client
  root.style.setProperty('--accent-color', '#CUSTOM_COLOR');
  root.style.setProperty('--progress-bar-color', '#CUSTOM_COLOR');
  break;
```

2. Optionally add CSS class-based overrides in `dynamicColors.css`:

```css
body.client-297 {
  --accent-color: #CUSTOM_COLOR;
  --progress-bar-color: #CUSTOM_COLOR;
}
```

## Migration from Static CSS Files

The new system replaces static client override CSS files:

### Before (Static CSS)
```css
/* src/styles/client-overrides/283.css */
.newProgressBar {
  background-color: #fca127AA !important;
}
```

### After (Dynamic CSS Variables)
```css
/* Automatically handled by ClientColorService */
/* CSS uses variables that are set dynamically */
.newProgressBar {
  background-color: var(--progress-bar-color, #e94e77) !important;
}
```

## Benefits

1. **Consistency**: All colors come from a single source of truth
2. **Maintainability**: No need to maintain separate CSS files per client
3. **Flexibility**: Easy to add new color properties or clients
4. **Performance**: Reduced CSS bundle size
5. **Reliability**: Fallback colors ensure the app always looks good
6. **Developer Experience**: Clear color system with TypeScript support

## Backward Compatibility

The system maintains backward compatibility:

1. **Existing CSS classes** continue to work
2. **Static CSS files** are still loaded but overridden by dynamic colors
3. **Gradual migration** is possible - components can be updated incrementally

## Testing

To test client colors:

1. **Switch clients** by changing the URL or client data
2. **Use browser dev tools** to inspect CSS variable values
3. **Test fallback colors** by temporarily removing client data
4. **Verify progress bars** during loading states
5. **Check all interactive states** (hover, active, focus)

## Future Enhancements

Potential improvements:

1. **Theme editor** for real-time color customization
2. **Color accessibility** validation
3. **Dark mode** support
4. **Animation transitions** between color changes
5. **Color palette** suggestions based on brand colors
