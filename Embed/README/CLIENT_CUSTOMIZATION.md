# Client Customization Documentation (WIP)

This document records all hardcoded client-specific customizations in the codebase to facilitate future refactoring and standardization.

## Emporia (Client ID: 283)

### CSS Overrides
- **File**: `src/styles/client-overrides/283.css`
- **Colors**:
  - Background: `#042454` (dark blue gradient)
  - Accent color: `#0E4EA0` (blue)
  - Progress bar: `#fca127AA` (orange with transparency)

### SVG Utilities
- **File**: `src/utils/svgUtils.ts`
- **Implementation**: Replaces pink colors with Emporia blue (`#0E4EA0`)
- **Logo URL**: `https://files.repd.us/manual/Emporia-Icon-White.png`

### Client ID Mappings
- **File**: `src/App.tsx`
```typescript
const clientNameToId: Record<string, string> = {
  'emporia': '283'
};
```

- **File**: `index.html`
```javascript
const clientNameToId = {
  'emporia': '283'
};
```

- **File**: `src/components/PopularAnswers/PopularAnswers.tsx`
```typescript
const clientIdToSubdomain: Record<string, string> = {
  '283': 'emporia'
};
```

### Display Name Mapping
- **File**: `src/context/LanguageContext.tsx`
```typescript
const clientNameMap: Record<string, string> = {
  'emporia': 'Emporia'
};
```

### Component-Specific Checks
- **File**: `src/components/CityIllustration/CityIllustration.tsx`
```typescript
const isEmporia = localStorage.getItem('clientId') === '283' || 
                  window.location.pathname.toLowerCase().includes('emporia');
```

## Page (Client ID: 295)
- **Website**: https://cityofpage.org/
- **CSS File**: `src/styles/client-overrides/295.css`
- **Colors**:
  - Background: `#c94726` (orange-red)
  - Accent color: `#85d0d3` (teal)
  - Progress bar: `#85d0d3` (teal)
- **Logo URL**: `https://files.repd.us/manual/Page-Logo-White.png`
- **Client ID Mappings**: Added to all mapping objects

## York (Client ID: 294)
- **Website**: https://www.cityofyork.net/
- **CSS File**: `src/styles/client-overrides/294.css` (needs verification)
- **Client ID Mappings**: Needs to be documented

## Mount Jackson (Client ID: 293)
- **Website**: https://www.mountjackson.com/
- **CSS File**: `src/styles/client-overrides/293.css` (needs verification)
- **Client ID Mappings**: Needs to be documented

## Loudoun County (Client ID: 292)
- **Website**: https://www.loudoun.gov/
- **CSS File**: `src/styles/client-overrides/292.css` (needs verification)
- **Client ID Mappings**: Needs to be documented

## Park City (Client ID: 288)
- **Website**: https://www.parkcityks.gov/
- **CSS File**: `src/styles/client-overrides/288.css` (needs verification)
- **Client ID Mappings**: Needs to be documented

## Roeland Park (Client ID: 287)
- **Website**: https://roelandpark.net/
- **CSS File**: `src/styles/client-overrides/287.css` (needs verification)
- **Client ID Mappings**: Needs to be documented

## Myrtle Beach (Client ID: 299)
- **Website**: https://www.cityofmyrtlebeach.com/
- **CSS File**: `src/styles/client-overrides/299.css` (needs verification)
- **Client ID Mappings**: Needs to be documented

## Town of Morrisville (Client ID: 281)
- **Website**: https://www.morrisvillenc.gov/
- **CSS File**: `src/styles/client-overrides/281.css` (needs verification)
- **Client ID Mappings**: Needs to be documented

## City of Traverse City (Client ID: 269)
- **Website**: https://www.traversecitymi.gov/
- **CSS File**: `src/styles/client-overrides/269.css` (needs verification)
- **Client ID Mappings**: Needs to be documented

## Myrtle Beach PRST (Client ID: 266)
- **Website**: https://www.myrtlebeachprst.com/
- **CSS File**: `src/styles/client-overrides/266.css` (needs verification)
- **Client ID Mappings**: Needs to be documented

## Tulare (Client ID: 234)
- **Website**: https://www.tulare.ca.gov/
- **CSS File**: `src/styles/client-overrides/234.css` (needs verification)
- **Client ID Mappings**: Needs to be documented

## Salisbury (Client ID: 221)
- **Website**: https://salisbury.md/
- **CSS File**: `src/styles/client-overrides/221.css` (needs verification)
- **Client ID Mappings**: Needs to be documented

## Other Clients

To document additional clients, follow this process for each client:

1. **Identify Client ID and Name**:
   - Search for client IDs in mapping objects (`clientNameToId`, `clientIdToSubdomain`)
   - Check for client-specific CSS files in `src/styles/client-overrides/`

2. **Document CSS Overrides**:
   - Locate client CSS file (e.g., `src/styles/client-overrides/{clientId}.css`)
   - Document key color values and custom styles

3. **Document SVG Customizations**:
   - Check `src/utils/svgUtils.ts` for client-specific SVG manipulations
   - Document any custom logo URLs or color replacements

4. **Document ID Mappings**:
   - Record all instances of the client ID in mapping objects
   - Note any special handling in components

5. **Document Component-Specific Logic**:
   - Search for client ID checks in component files
   - Document any conditional rendering or behavior

## Issues and Recommendations

1. **Inconsistent Client Detection**:
   - Multiple methods used to detect client (localStorage, URL path, hardcoded IDs)
   - Recommendation: Standardize client detection in a single service

2. **Hardcoded Values**:
   - Client IDs, names, and colors are hardcoded in multiple files
   - Recommendation: Move to a centralized configuration file

3. **CSS Organization**:
   - Client-specific CSS is in separate files but referenced inconsistently
   - Recommendation: Implement a theming system with CSS variables

4. **SVG Manipulation**:
   - Direct DOM manipulation for SVG colors
   - Recommendation: Use CSS variables or themed components

## Next Steps

1. Create a `ClientService` that handles all client-specific logic
2. Implement a proper theming system using CSS variables
3. Move all client mappings to a single configuration file
4. Standardize the client detection and customization process
5. Create a client configuration schema to validate all client settings
6. Implement automated tests to verify client customizations work correctly
