# Audio Segments Subtitle Update

## Overview
Updated the embed's subtitle functionality to match the web app's implementation using audio segments instead of the previous items-based approach, while optimizing for the smaller embed window size.

## Changes Made

### 1. Interface Updates (`src/interfaces/answer.interface.ts`)
- Added `AudioSegment` interface with `start_time`, `end_time`, and `transcript` properties
- Updated `Transcription` interface to include optional `audio_segments` array
- Modified `TranscriptionTranslations` to support both `Transcription` objects and `AudioSegment[]` arrays

### 2. Video Helper Service (`src/services/videoHelperService.ts`)
- **Complete rewrite of `getCurrentSubtitle` function** to prioritize audio segments over items
- Added helper functions for embed-specific text handling:
  - `truncateTextForEmbed()`: Intelligently truncates long text at word boundaries
  - `splitTextIntoLines()`: Splits text into multiple lines for better readability
- **New `getSubtitleFromAudioSegments()` function**:
  - Finds current audio segment based on video time
  - Handles gaps between segments appropriately
  - Splits long transcripts into max 2 lines for embed display
  - Shows contextual subtitles before/after speech
- **Fallback `getSubtitleFromItems()` function**:
  - Maintains backward compatibility with items-based transcription
  - Shows 5 recent words instead of 3 for better context
  - Applies text truncation for embed constraints

### 3. Video Player Component (`src/components/VideoPlayer/VideoPlayer.tsx`)
- Updated translation detection logic to handle both audio segments and items
- Fixed TypeScript type checking for new transcription format
- Added missing dependency to useEffect for proper re-rendering

### 4. CSS Styling (`src/components/VideoPlayer/VideoPlayer.module.css`)
- **Optimized subtitle container for embed**:
  - Reduced font size from 1.2em to 0.9em
  - Increased max-width from 80% to 90%
  - Added max-height constraint (4.5em) with overflow hidden
  - Improved line-height (1.3) and text wrapping
- **Responsive design for different embed sizes**:
  - Mobile (≤480px): Further reduced font size and padding
  - Small height (≤400px): Compact styling for narrow embeds
  - Very small (≤320px): Minimal styling for tiny embeds

## Key Features

### Audio Segments Priority
The system now prioritizes audio segments over items, matching the web app behavior:
1. **Audio Segments**: Used when available (preferred method)
2. **Items**: Used as fallback for backward compatibility

### Embed-Specific Optimizations
- **Text Length Management**: Long transcripts are split into 2 lines maximum
- **Intelligent Truncation**: Text is truncated at word boundaries with ellipsis
- **Responsive Sizing**: Font size and padding adjust based on embed dimensions
- **Gap Handling**: Appropriate behavior during silence between speech segments

### Multi-Language Support
- Supports both translated audio segments (array format) and transcription objects
- Maintains compatibility with existing translation system
- Proper fallback to original language when translations unavailable

## Technical Implementation

### Data Flow
1. **Check for translations**: Prioritizes current language translations
2. **Determine data type**: Handles both audio segments arrays and transcription objects
3. **Extract segments**: Gets audio_segments from transcription or direct array
4. **Process timing**: Maps current video time to appropriate segment
5. **Format text**: Applies embed-specific formatting and truncation
6. **Display**: Shows optimized subtitle with smooth transitions

### Timing Logic
- **In Segment**: Shows current segment transcript (split into lines)
- **Before Speech**: Shows empty subtitle until speech begins
- **After Speech**: Shows last segment transcript
- **Between Segments**: Shows empty subtitle during gaps

### Performance Optimizations
- **Hold Period**: 300ms minimum between subtitle updates to prevent flickering
- **Efficient Lookups**: Direct segment matching instead of filtering all items
- **Minimal DOM Updates**: Only updates when subtitle content actually changes

## Testing Recommendations

1. **Test with audio segments data**: Verify proper segment-based timing
2. **Test with items data**: Ensure backward compatibility works
3. **Test translations**: Check both array and object translation formats
4. **Test responsive behavior**: Verify styling at different embed sizes
5. **Test edge cases**: Before speech, after speech, and gaps between segments

## Migration Notes

- **Backward Compatible**: Existing items-based transcriptions continue to work
- **Automatic Detection**: System automatically detects and uses best available data format
- **No Breaking Changes**: All existing embed implementations remain functional
- **Enhanced Experience**: Audio segments provide more natural subtitle timing and content
