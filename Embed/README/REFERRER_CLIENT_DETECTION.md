# Referrer-Based Client Detection

## Overview

This feature provides a fallback mechanism for client identification when the embed URL path is empty. Instead of failing to load, the embed system can now identify the client based on the referrer URL (the parent website where the embed is hosted).

## Problem Solved

**Scenario**: An embed is live on a client's website, but the client path in the embed URL is empty (e.g., `https://embed.repd.us/` instead of `https://embed.repd.us/emporia`).

**Previous Behavior**: The embed would fail to identify the client and either show a generic experience or fail to load.

**New Behavior**: The embed extracts the client information from the referrer URL and loads the appropriate client configuration.

## How It Works

### 1. Client Detection Flow

The system now follows this priority order for client identification:

1. **URL Path** - Extract client name from embed URL path (e.g., `/emporia`)
2. **Referrer URL** - Extract client from the parent website domain
3. **Search Parameters** - Check for explicit `client` parameter
4. **localStorage** - Use previously stored client ID
5. **Environment Default** - Fall back to `REACT_APP_DEFAULT_CLIENT`

### 2. Domain Mapping

Client domains are mapped to internal client names in `src/config/clientDomainMapping.ts`:

```typescript
export const CLIENT_DOMAIN_MAP = {
  'emporia.gov': 'emporia',
  'arlington.gov': 'arlington',
  'cityofpage.org': 'page',
  // ... more mappings
};
```

### 3. Implementation Details

**Files Modified:**
- `src/services/sessionService.ts` - Enhanced `getClientNameFromUrl()` method
- `src/App.tsx` - Added referrer fallback to `getClientIdFromPath()`
- `src/config/clientDomainMapping.ts` - New centralized domain mapping

**Key Functions:**
- `extractClientFromReferrer(referrerUrl)` - Main extraction logic
- `addClientDomainMapping(domain, clientName)` - Add new mappings dynamically

## Usage Examples

### Scenario 1: Empty Path with Known Referrer
```
Embed URL: https://embed.repd.us/?referrer=https%3A//emporia.gov/services
Referrer: https://emporia.gov/services
Result: Client 'emporia' identified from referrer domain
```

### Scenario 2: Subdomain Handling
```
Embed URL: https://embed.repd.us/?referrer=https%3A//www.arlington.gov/page
Referrer: https://www.arlington.gov/page
Result: Client 'arlington' identified (www. prefix ignored)
```

### Scenario 3: Unknown Domain
```
Embed URL: https://embed.repd.us/?referrer=https%3A//unknown-site.com
Referrer: https://unknown-site.com
Result: Falls back to environment default or localStorage
```

## Adding New Clients

To add support for a new client domain:

### 1. Update Domain Mapping
```typescript
// In src/config/clientDomainMapping.ts
export const CLIENT_DOMAIN_MAP = {
  // ... existing mappings
  'newclient.gov': 'newclient',
};
```

### 2. Ensure Client Name Mapping
```typescript
// In src/config/clientLogoConfig.ts
export const clientNameToId = {
  // ... existing mappings
  'newclient': '123',
};
```

### 3. Test the Integration
1. Deploy the updated embed code
2. Test with referrer URL: `https://embed.repd.us/?referrer=https%3A//newclient.gov`
3. Verify client identification in browser console

## Debugging

### Console Logging
The system logs client identification attempts:

```javascript
// Successful identification
"Client identified from referrer: emporia"

// Failed identification
"No client mapping found for referrer: unknown-domain.com"
```

### Testing Locally
```javascript
// Test referrer extraction
import { extractClientFromReferrer } from './config/clientDomainMapping';

console.log(extractClientFromReferrer('https://emporia.gov/services'));
// Output: 'emporia'
```

## Security Considerations

1. **Referrer Validation**: The system only extracts client names from pre-configured domains
2. **URL Parsing**: Uses native URL constructor with proper error handling
3. **Fallback Chain**: Multiple fallback mechanisms prevent system failure

## Performance Impact

- **Minimal**: Only processes referrer when path-based detection fails
- **Cached**: Client identification is cached in localStorage
- **Efficient**: Simple domain string matching with early returns

## Future Enhancements

1. **Dynamic Domain Discovery**: API endpoint to fetch client domains
2. **Wildcard Patterns**: Support for subdomain wildcards (*.emporia.gov)
3. **Analytics Integration**: Track referrer-based identifications
4. **Admin Interface**: UI for managing domain mappings

## Troubleshooting

### Common Issues

**Issue**: Client not identified despite correct referrer
**Solution**: Check if domain is in `CLIENT_DOMAIN_MAP` and client name is in `clientNameToId`

**Issue**: Console shows "Error parsing referrer URL"
**Solution**: Verify referrer URL is properly encoded in embed URL parameters

**Issue**: Embed loads but shows wrong client
**Solution**: Check for conflicting localStorage values or URL path parameters

### Support

For issues with referrer-based client detection:
1. Check browser console for identification logs
2. Verify domain mapping configuration
3. Test with direct embed URL including referrer parameter
4. Contact development team with specific referrer URL and expected client
