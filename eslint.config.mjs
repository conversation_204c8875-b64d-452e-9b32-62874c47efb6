import eslintJs from "@eslint/js";
import tseslint from "typescript-eslint";

import prettierConfig from "eslint-config-prettier";
import importsPlugin from "eslint-plugin-import";
import prettierPlugin from "eslint-plugin-prettier";

import globals from "globals";

export default tseslint.config(
  eslintJs.configs.recommended,
  ...tseslint.configs.strictTypeChecked,
  {
    ignores: ["build", "dist", "eslint.config.mjs"],
  },
  {
    files: ["**/*.{js,ts}"],

    plugins: {
      import: importsPlugin,
      prettier: prettierPlugin,
    },

    rules: {
      ...prettierPlugin.configs.recommended.rules,
      "import/order": [
        "error",
        {
          groups: [["builtin"], ["external"], ["internal", "parent", "sibling", "index"]],
          pathGroups: [
            {
              pattern: "@/**",
              group: "internal",
            },
          ],
          "newlines-between": "always",
        },
      ],
      "@typescript-eslint/no-unused-vars": "off",
      "@typescript-eslint/restrict-template-expressions": "off",
      "prettier/prettier": "error",
    },
  },

  {
    languageOptions: {
      globals: globals.node,
      parserOptions: {
        project: "./tsconfig.json",
      },
    },
  },

  prettierConfig,
);
