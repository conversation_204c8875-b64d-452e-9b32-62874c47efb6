import { URL } from "url";

import { logger } from "@/services/logger";
import { delay } from "./async-utils";
import { extractDomainAndTld } from "./url-utils";
import { getFileType } from "./file-utils";

export const fetchWithTimeout = async (url: string, options: RequestInit = {}, timeout: number) => {
  const controller = new AbortController();

  const timeoutId = setTimeout(() => {
    controller.abort();
    logger.error(`Request to ${url} timed out after ${String(timeout)}ms`);
  }, timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });
    return response;
  } finally {
    clearTimeout(timeoutId);
  }
};

const USER_AGENTS = [
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
  "Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
  "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59",
];

/**
 * Get a random user agent string
 * @returns Random user agent string
 */
export const getRandomUserAgent = (): string => {
  const randomIndex = Math.floor(Math.random() * USER_AGENTS.length);
  return USER_AGENTS[randomIndex];
};

/**
 * Fetch with user agent headers to avoid being blocked
 * @param url URL to fetch
 * @param timeout Timeout in milliseconds
 * @param options Fetch options
 * @returns Response
 */
export const fetchWithUserHeaders = async (url: string, options = {}) => {
  const defaultOptions = {
    headers: {
      "User-Agent":
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      Accept: "*/*",
      "Accept-Language": "en-US,en;q=0.9",
      Accepts: "application/json",
      Connection: "keep-alive",
      "Sec-Fetch-Dest": "empty",
      "Sec-Fetch-Mode": "cors",
      "Sec-Fetch-Site": "same-site",
      "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": '"macOS"',
    },
    redirect: "follow",
    timeout: 30000,
  };

  const mergedOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...(options.headers || {}),
    },
  };

  // Special handling for CivicPlus sites (like Roeland Park)
  if (
    url.includes("roelandpark.net") &&
    (url.includes("/DocumentCenter/View/") || url.includes("/Archive.aspx?ADID="))
  ) {
    logger.info(`Using special fetch options for CivicPlus URL: ${url}`);
    mergedOptions.headers["Cookie"] = "CivicPlus=accepted";
  }

  // Special handling for Morrisville site
  if (url.includes("morrisvillenc.gov")) {
    logger.info(`Using special fetch options for Morrisville URL: ${url}`);
    mergedOptions.headers["Sec-Fetch-Dest"] = "empty";
    mergedOptions.headers["Sec-Fetch-Mode"] = "cors";
    mergedOptions.headers["Sec-Fetch-Site"] = "same-site";
    mergedOptions.headers["Accepts"] = "application/json";
    mergedOptions.headers["Origin"] = "http://localhost:3001";
    mergedOptions.headers["Referer"] = "http://localhost:3001/";
  }

  return fetch(url, mergedOptions);
};

// Re-export from lib for backward compatibility
export { extractDomainAndTld, delay };

/**
 * Extract domain from a URL
 * @param url URL to extract domain from
 * @returns Domain name without protocol, www, or path
 */
export const getSiteDomain = (url: string): string => {
  try {
    const parsedUrl = new URL(url);
    let hostname = parsedUrl.hostname;

    // Remove www. prefix if present
    if (hostname.startsWith("www.")) {
      hostname = hostname.substring(4);
    }

    return hostname;
  } catch (error) {
    // If URL parsing fails, try a simple extraction
    const match = url.match(/^(?:https?:\/\/)?(?:www\.)?([^\/]+)/i);
    return match ? match[1] : "unknown-domain";
  }
};

// Re-export from lib for backward compatibility
export { getFileType };
