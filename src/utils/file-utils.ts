/**
 * File utility functions for type detection, path handling, and validation
 */

import * as path from "path";

/**
 * Get file type from URL or filename
 * @param url URL or filename
 * @returns File type (extension without dot)
 */
export const getFileType = (url: string, contentType = "", content = ""): string => {
  // Check for explicit content type first
  if (contentType) {
    if (contentType.includes("text/html")) return "html";
    if (contentType.includes("application/pdf")) return "pdf";
    if (contentType.includes("text/plain")) return "text";
  }

  // Check URL extension
  const urlLower = url.toLowerCase();
  if (urlLower.endsWith(".pdf")) return "pdf";
  if (urlLower.endsWith(".txt")) return "text";
  if (urlLower.endsWith(".html") || urlLower.endsWith(".htm")) return "html";

  // For URLs without clear extensions, check if content looks like HTML
  if (content && (content.includes("<html") || content.includes("<!DOCTYPE") || content.includes("<body"))) {
    return "html";
  }

  // Default to HTML for web URLs without clear file extensions
  if (url.startsWith("http") && !urlLower.match(/\.(pdf|txt|doc|docx|zip|exe|img|jpg|png|gif)$/)) {
    return "html";
  }

  return "unknown";
};

/**
 * Check if a file type is supported for processing
 * @param fileType File extension without dot
 * @returns Boolean indicating if file type is supported
 */
export const isSupportedFileType = (fileType: string): boolean => {
  const supportedTypes = ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "csv"];

  return supportedTypes.includes(fileType.toLowerCase());
};

/**
 * Check if a file type is a document type
 * @param fileType File extension without dot
 * @returns Boolean indicating if file type is a document
 */
export const isDocumentType = (fileType: string): boolean => {
  const documentTypes = ["pdf", "doc", "docx", "txt", "rtf"];

  return documentTypes.includes(fileType.toLowerCase());
};

/**
 * Check if a file type is a spreadsheet type
 * @param fileType File extension without dot
 * @returns Boolean indicating if file type is a spreadsheet
 */
export const isSpreadsheetType = (fileType: string): boolean => {
  const spreadsheetTypes = ["xls", "xlsx", "csv", "ods"];

  return spreadsheetTypes.includes(fileType.toLowerCase());
};

/**
 * Check if a file type is a presentation type
 * @param fileType File extension without dot
 * @returns Boolean indicating if file type is a presentation
 */
export const isPresentationType = (fileType: string): boolean => {
  const presentationTypes = ["ppt", "pptx", "odp"];

  return presentationTypes.includes(fileType.toLowerCase());
};

/**
 * Get content type based on file extension
 * @param filePath File path or extension
 * @returns MIME content type
 */
export const getContentType = (filePath: string): string => {
  const extension = path.extname(filePath).toLowerCase();

  const contentTypes: Record<string, string> = {
    ".pdf": "application/pdf",
    ".doc": "application/msword",
    ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ".xls": "application/vnd.ms-excel",
    ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ".ppt": "application/vnd.ms-powerpoint",
    ".pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    ".txt": "text/plain",
    ".csv": "text/csv",
    ".html": "text/html",
    ".htm": "text/html",
    ".xml": "application/xml",
    ".json": "application/json",
    ".zip": "application/zip",
    ".rar": "application/x-rar-compressed",
    ".tar": "application/x-tar",
    ".gz": "application/gzip",
    ".jpg": "image/jpeg",
    ".jpeg": "image/jpeg",
    ".png": "image/png",
    ".gif": "image/gif",
    ".svg": "image/svg+xml",
    ".bmp": "image/bmp",
    ".mp3": "audio/mpeg",
    ".mp4": "video/mp4",
    ".avi": "video/x-msvideo",
    ".mov": "video/quicktime",
    ".wmv": "video/x-ms-wmv",
    ".flv": "video/x-flv",
  };

  return contentTypes[extension] || "application/octet-stream";
};

/**
 * Generate a safe filename from a URL or title
 * @param input URL or title string
 * @param maxLength Maximum length of filename
 * @returns Safe filename
 */
export const generateSafeFilename = (input: string, maxLength = 100): string => {
  try {
    let filename = input;

    // If it's a URL, extract meaningful parts
    if (input.startsWith("http")) {
      const url = new URL(input);
      const hostname = url.hostname.replace(/^www\./, "");
      const pathname = url.pathname.replace(/\//g, "_");
      filename = `${hostname}${pathname}`;
    }

    // Remove or replace unsafe characters
    filename = filename
      .replace(/[<>:"/\\|?*]/g, "_") // Replace unsafe characters
      .replace(/\s+/g, "_") // Replace spaces with underscores
      .replace(/_+/g, "_") // Replace multiple underscores with single
      .replace(/^_|_$/g, ""); // Remove leading/trailing underscores

    // Truncate if too long
    if (filename.length > maxLength) {
      filename = filename.substring(0, maxLength);
    }

    return filename || "unnamed_file";
  } catch (error) {
    return "unnamed_file";
  }
};

/**
 * Generate S3 key for a file based on URL and site ID
 * @param url File URL
 * @param siteId Site ID (optional)
 * @param prefix Key prefix (optional)
 * @returns S3 key string
 */
export const generateS3Key = (url: string, siteId?: number, prefix = "files"): string => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.replace(/^www\./, "");
    const pathname = urlObj.pathname;

    // Extract filename and extension
    const filename = pathname.split("/").pop() || "file";
    const extension = getFileType(url);

    // Create a safe filename
    const safeFilename = generateSafeFilename(filename);

    // Generate timestamp for uniqueness
    const timestamp = Date.now();

    // Construct S3 key
    const parts = [prefix];

    if (siteId) {
      parts.push(`site_${siteId}`);
    }

    parts.push(hostname);
    parts.push(`${timestamp}_${safeFilename}`);

    if (extension && !safeFilename.endsWith(`.${extension}`)) {
      parts[parts.length - 1] += `.${extension}`;
    }

    return parts.join("/");
  } catch (error) {
    // Fallback key generation
    const timestamp = Date.now();
    const extension = getFileType(url);
    return `${prefix}/unknown/${timestamp}_file${extension ? `.${extension}` : ""}`;
  }
};

/**
 * Check if a file exists and get its stats
 * @param filePath Path to file
 * @returns Promise with file stats or null if doesn't exist
 */
export const getFileStats = async (
  filePath: string,
): Promise<{
  exists: boolean;
  size?: number;
  modified?: Date;
} | null> => {
  try {
    const fs = await import("fs/promises");
    const stats = await fs.stat(filePath);

    return {
      exists: true,
      size: stats.size,
      modified: stats.mtime,
    };
  } catch (error) {
    return {
      exists: false,
    };
  }
};

/**
 * Ensure directory exists, create if it doesn't
 * @param dirPath Directory path
 * @returns Promise that resolves when directory exists
 */
export const ensureDirectoryExists = async (dirPath: string): Promise<void> => {
  try {
    const fs = await import("fs/promises");
    await fs.mkdir(dirPath, { recursive: true });
  } catch (error) {
    throw new Error(`Failed to create directory ${dirPath}: ${error}`);
  }
};

/**
 * Get file size in human readable format
 * @param bytes File size in bytes
 * @returns Human readable size string
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

/**
 * Extract file links from an array of URLs
 * @param urls Array of URLs to filter
 * @returns Array of URLs that point to files
 */
export const extractFileLinks = (urls: string[]): string[] => {
  return urls.filter((url) => {
    const fileType = getFileType(url);
    return isSupportedFileType(fileType);
  });
};

/**
 * Categorize file type into broad categories
 * @param fileType File extension without dot
 * @returns Category string
 */
export const categorizeFileType = (fileType: string): string => {
  if (isDocumentType(fileType)) return "document";
  if (isSpreadsheetType(fileType)) return "spreadsheet";
  if (isPresentationType(fileType)) return "presentation";

  const imageTypes = ["jpg", "jpeg", "png", "gif", "svg", "bmp"];
  if (imageTypes.includes(fileType.toLowerCase())) return "image";

  const videoTypes = ["mp4", "avi", "mov", "wmv", "flv"];
  if (videoTypes.includes(fileType.toLowerCase())) return "video";

  const audioTypes = ["mp3", "wav", "ogg", "m4a"];
  if (audioTypes.includes(fileType.toLowerCase())) return "audio";

  const archiveTypes = ["zip", "rar", "tar", "gz", "7z"];
  if (archiveTypes.includes(fileType.toLowerCase())) return "archive";

  return "other";
};

/**
 * Check if a file should be processed based on its URL and detected file type
 * @param url File URL
 * @param fileType Detected file type
 * @returns Boolean indicating if the file should be processed
 */
export const shouldProcessFile = (url: string, fileType: string): boolean => {
  // Always process web URLs, even if file type is unknown
  if (url.startsWith("http") && fileType === "unknown") {
    console.log(`[info]: Processing URL with unknown type as HTML: ${url}`);
    return true;
  }

  // Process known supported types
  return ["html", "pdf", "text"].includes(fileType);
};
