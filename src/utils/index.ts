/**
 * Centralized exports for all utility functions
 */

// Re-export everything from existing helpers
export * from "./helpers";
export * from "./sourceHelpers";

// Async utilities
export { delay, retryWithBackoff, processBatch as processAsyncBatch, processWithTimeout } from "./async-utils";

// URL utilities
export {
  extractDomainAndTld,
  shouldProcessUrl,
  resolveUrl,
  removeFragment,
  isSameDomain,
  isValidUrl,
  getBaseUrl,
  getDirectoryPath,
  normalizeUrl,
  shouldSkipUrl,
  getUrlExtension,
  isFileUrl,
} from "./url-utils";

// Date utilities
export {
  extractDatesFromContent,
  extractMostRecentDateFromContent,
  safeParseDate,
  calculateContentAge,
  extractPublicationDate,
  isReasonableDate,
  formatDate,
} from "./date-utils";

// File utilities
export {
  getFileType,
  isSupportedFileType,
  isDocumentType,
  isSpreadsheetType,
  isPresentationType,
  getContentType,
  generateSafeFilename,
  generateS3Key,
  getFileStats,
  ensureDirectoryExists,
  formatFileSize,
  extractFileLinks,
  categorizeFileType,
} from "./file-utils";

// Validation utilities
export {
  urlSchema,
  optionalUrlSchema,
  emailSchema,
  positiveIntSchema,
  nonNegativeIntSchema,
  fileTypeSchema,
  siteLinkStatusSchema,
  fileLinkStatusSchema,
  dateStringSchema,
  metadataSchema,
  paginationSchema,
  siteSchema,
  siteLinkSchema,
  fileLinkSchema,
  embeddingSchema,
  questionEmbeddingSchema,
  processingOptionsSchema,
  batchOptionsSchema,
  validateUrl,
  validateEmail,
  validateFileType,
  sanitizeString,
  sanitizeMetadata,
  validateProcessingOptions,
  validateBatchOptions,
  createSafeErrorMessage,
  parseCommandLineArgs,
} from "./validation";
