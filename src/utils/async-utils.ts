/**
 * Async utility functions for common operations
 */

/**
 * Helper function for delay between operations
 * @param ms Milliseconds to delay
 * @returns Promise that resolves after the delay
 */
export const delay = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

/**
 * Retry a function with exponential backoff
 * @param fn Function to retry
 * @param maxRetries Maximum number of retries
 * @param baseDelay Base delay in milliseconds
 * @returns Promise that resolves with the function result
 */
export const retryWithBackoff = async <T>(fn: () => Promise<T>, maxRetries = 3, baseDelay = 1000): Promise<T> => {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      if (attempt === maxRetries) {
        throw lastError;
      }

      const delayMs = baseDelay * Math.pow(2, attempt);
      await delay(delayMs);
    }
  }

  throw lastError!;
};

/**
 * Process items in batches with concurrency control
 * @param items Items to process
 * @param processor Function to process each item
 * @param batchSize Number of items to process concurrently
 * @param delayBetweenBatches Delay between batches in milliseconds
 * @returns Promise that resolves when all items are processed
 */
export const processBatch = async <T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  batchSize = 10,
  delayBetweenBatches = 1000,
): Promise<R[]> => {
  const results: R[] = [];

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchPromises = batch.map(processor);
    const batchResults = await Promise.all(batchPromises);

    results.push(...batchResults);

    // Add delay between batches (except for the last batch)
    if (i + batchSize < items.length && delayBetweenBatches > 0) {
      await delay(delayBetweenBatches);
    }
  }

  return results;
};

/**
 * Process items with timeout and concurrency control
 * @param items Items to process
 * @param processor Function to process each item
 * @param options Processing options
 * @returns Promise that resolves with results and errors
 */
export const processWithTimeout = async <T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  options: {
    concurrency?: number;
    timeout?: number;
    delayBetweenBatches?: number;
  } = {},
): Promise<{
  results: R[];
  errors: Array<{ item: T; error: Error }>;
}> => {
  const { concurrency = 3, timeout = 5000, delayBetweenBatches = 1000 } = options;

  const results: R[] = [];
  const errors: Array<{ item: T; error: Error }> = [];

  // Split items into chunks for processing
  const chunks: T[][] = [];
  for (let i = 0; i < items.length; i += concurrency) {
    chunks.push(items.slice(i, i + concurrency));
  }

  for (const chunk of chunks) {
    // Process each chunk of items concurrently
    const promises = chunk.map(async (item) => {
      try {
        // Add timeout to each item processing
        const result = await Promise.race([
          processor(item),
          new Promise<never>((_, reject) =>
            setTimeout(() => {
              reject(new Error("Operation timeout"));
            }, timeout),
          ),
        ]);
        return { success: true, result, item };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error : new Error(String(error)),
          item,
        };
      }
    });

    // Wait for all promises in this chunk to resolve
    const chunkResults = await Promise.allSettled(promises);

    // Process results
    for (const promiseResult of chunkResults) {
      if (promiseResult.status === "fulfilled") {
        const { success, result, error, item } = promiseResult.value;
        if (success) {
          results.push(result);
        } else {
          errors.push({ item, error });
        }
      } else {
        // This shouldn't happen since we're catching errors above,
        // but handle it just in case
        errors.push({
          item: chunk[0], // We don't know which item failed
          error: new Error("Promise rejected unexpectedly"),
        });
      }
    }

    // Add delay between chunks
    if (chunks.length > 1 && delayBetweenBatches > 0) {
      await delay(delayBetweenBatches);
    }
  }

  return { results, errors };
};
