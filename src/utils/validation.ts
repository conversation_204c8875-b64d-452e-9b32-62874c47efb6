/**
 * Common validation utilities and schemas
 */

import { z } from "zod";

/**
 * URL validation schema
 */
export const urlSchema = z.string().url("Invalid URL format");

/**
 * Optional URL schema (can be empty string)
 */
export const optionalUrlSchema = z.union([z.string().url(), z.literal("")]);

/**
 * Email validation schema
 */
export const emailSchema = z.string().email("Invalid email format");

/**
 * Positive integer schema
 */
export const positiveIntSchema = z.number().int().positive("Must be a positive integer");

/**
 * Non-negative integer schema
 */
export const nonNegativeIntSchema = z.number().int().min(0, "Must be non-negative");

/**
 * File type validation schema
 */
export const fileTypeSchema = z.enum([
  "pdf",
  "doc",
  "docx",
  "xls",
  "xlsx",
  "ppt",
  "pptx",
  "txt",
  "csv",
  "html",
  "htm",
  "xml",
  "json",
]);

/**
 * Status validation schema for site links
 */
export const siteLinkStatusSchema = z.enum(["pending", "scraped", "failed", "ignored"]);

/**
 * Status validation schema for file links
 */
export const fileLinkStatusSchema = z.enum(["pending", "processed", "failed", "ignored", "download_failed", "scraped"]);

/**
 * Date string validation schema
 */
export const dateStringSchema = z.string().refine((date) => !isNaN(Date.parse(date)), "Invalid date format");

/**
 * Metadata validation schema
 */
export const metadataSchema = z.record(z.unknown()).optional();

/**
 * Pagination parameters schema
 */
export const paginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
  offset: z.number().int().min(0).optional(),
});

/**
 * Site creation/update schema
 */
export const siteSchema = z.object({
  url: urlSchema,
  enabled: z.boolean().default(true),
  requiresJavascript: z.boolean().default(false),
  client_id: z.number().int().positive().optional(),
  schema: z.string().optional(),
});

/**
 * Site link creation schema
 */
export const siteLinkSchema = z.object({
  url: urlSchema,
  siteId: positiveIntSchema,
  status: siteLinkStatusSchema.default("pending"),
  lastScrapedAt: z.date().optional(),
  linksOnPage: z.array(z.string()).optional(),
  fileLinksOnPage: z.array(z.string()).optional(),
  originalHtml: z.string().optional(),
  pageContent: z.string().optional(),
  timeElapsed: z.number().optional(),
  metadata: metadataSchema,
  comment: z.string().optional(),
});

/**
 * File link creation schema
 */
export const fileLinkSchema = z.object({
  url: urlSchema,
  fileType: fileTypeSchema.optional(),
  siteLinkId: positiveIntSchema,
  siteId: positiveIntSchema,
  status: fileLinkStatusSchema.default("pending"),
  processedAt: z.date().optional(),
  s3Key: z.string().optional(),
  s3Url: optionalUrlSchema.optional(),
  metadata: metadataSchema,
  comment: z.string().optional(),
});

/**
 * Embedding creation schema
 */
export const embeddingSchema = z.object({
  content: z.string().min(1, "Content cannot be empty"),
  embedding: z.array(z.number()),
  metadata: metadataSchema,
  fromSiteLinkId: positiveIntSchema.optional(),
  fromFileLinkId: positiveIntSchema.optional(),
});

/**
 * Question embedding schema
 */
export const questionEmbeddingSchema = z.object({
  question: z.string().min(1, "Question cannot be empty"),
  embedding: z.array(z.number()),
  clientId: positiveIntSchema,
  sources: z.array(z.string()).optional(),
});

/**
 * Processing options schema
 */
export const processingOptionsSchema = z.object({
  linksLimit: positiveIntSchema.default(10000),
  includeScraped: z.boolean().default(false),
  crawlInternalLinks: z.boolean().default(true),
  maxDepth: positiveIntSchema.default(5),
  concurrency: positiveIntSchema.default(3),
  urlIncludesFilter: z.string().optional(),
  forceRefresh: z.boolean().default(false),
});

/**
 * Batch processing options schema
 */
export const batchOptionsSchema = z.object({
  batchSize: positiveIntSchema.default(10),
  delayBetweenBatches: nonNegativeIntSchema.default(1000),
  itemTimeout: positiveIntSchema.default(30000),
  continueOnError: z.boolean().default(true),
});

/**
 * Validate URL format
 */
export const validateUrl = (url: string): boolean => {
  try {
    urlSchema.parse(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Validate email format
 */
export const validateEmail = (email: string): boolean => {
  try {
    emailSchema.parse(email);
    return true;
  } catch {
    return false;
  }
};

/**
 * Validate file type
 */
export const validateFileType = (fileType: string): boolean => {
  try {
    fileTypeSchema.parse(fileType);
    return true;
  } catch {
    return false;
  }
};

/**
 * Sanitize string for safe usage
 */
export const sanitizeString = (input: string, maxLength = 1000): string => {
  if (!input || typeof input !== "string") {
    return "";
  }

  return input
    .trim()
    .replace(/[\x00-\x1F\x7F]/g, "") // Remove control characters
    .substring(0, maxLength);
};

/**
 * Validate and sanitize metadata object
 */
export const sanitizeMetadata = (metadata: unknown): Record<string, unknown> => {
  if (!metadata || typeof metadata !== "object") {
    return {};
  }

  const sanitized: Record<string, unknown> = {};

  for (const [key, value] of Object.entries(metadata as Record<string, unknown>)) {
    // Sanitize key
    const sanitizedKey = sanitizeString(key, 100);
    if (!sanitizedKey) continue;

    // Sanitize value based on type
    if (typeof value === "string") {
      sanitized[sanitizedKey] = sanitizeString(value);
    } else if (typeof value === "number" && isFinite(value)) {
      sanitized[sanitizedKey] = value;
    } else if (typeof value === "boolean") {
      sanitized[sanitizedKey] = value;
    } else if (value instanceof Date) {
      sanitized[sanitizedKey] = value.toISOString();
    } else if (Array.isArray(value)) {
      // Only allow arrays of strings/numbers
      const sanitizedArray = value
        .filter((item) => typeof item === "string" || typeof item === "number")
        .map((item) => (typeof item === "string" ? sanitizeString(item) : item))
        .slice(0, 100); // Limit array size

      if (sanitizedArray.length > 0) {
        sanitized[sanitizedKey] = sanitizedArray;
      }
    }
    // Skip other types (objects, functions, etc.)
  }

  return sanitized;
};

/**
 * Validate processing options
 */
export const validateProcessingOptions = (options: unknown) => {
  return processingOptionsSchema.parse(options);
};

/**
 * Validate batch options
 */
export const validateBatchOptions = (options: unknown) => {
  return batchOptionsSchema.parse(options);
};

/**
 * Create a safe error message from unknown error
 */
export const createSafeErrorMessage = (error: unknown): string => {
  if (error instanceof Error) {
    return sanitizeString(error.message, 500);
  }

  if (typeof error === "string") {
    return sanitizeString(error, 500);
  }

  return "Unknown error occurred";
};

/**
 * Validate and parse command line arguments
 */
export const parseCommandLineArgs = (args: string[]): Record<string, string | boolean | number> => {
  const parsed: Record<string, string | boolean | number> = {};

  for (const arg of args) {
    if (arg.startsWith("--")) {
      const [key, value] = arg.substring(2).split("=");

      if (!key) continue;

      if (value === undefined) {
        // Flag without value
        parsed[key] = true;
      } else if (value.toLowerCase() === "true") {
        parsed[key] = true;
      } else if (value.toLowerCase() === "false") {
        parsed[key] = false;
      } else if (!isNaN(Number(value))) {
        parsed[key] = Number(value);
      } else {
        // Remove quotes if present
        parsed[key] = value.replace(/^["']|["']$/g, "");
      }
    }
  }

  return parsed;
};
