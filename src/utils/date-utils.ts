/**
 * Date utility functions for parsing, extraction, and validation
 */

/**
 * Extract dates from text content using various patterns
 * @param content Text content to extract dates from
 * @returns Array of date strings found in content
 */
export const extractDatesFromContent = (content: string): string[] => {
  if (!content || typeof content !== "string") {
    return [];
  }

  const datePatterns = [
    // Full month name with day and year: January 15, 2021
    /\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}\b/g,

    // Day, month name, year: 15 January 2021
    /\b\d{1,2}\s+(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}\b/g,

    // ISO format: 2021-01-15
    /\b\d{4}-\d{2}-\d{2}\b/g,

    // US format: 01/15/2021 or 1/15/21
    /\b\d{1,2}\/\d{1,2}\/\d{4}\b/g,

    // Updated/Published prefixes
    /\bUpdated:?\s+([A-Za-z]+\s+\d{1,2},\s+\d{4})\b/i,
    /\bPublished:?\s+([A-Za-z]+\s+\d{1,2},\s+\d{4})\b/i,

    // Posted on format
    /\bPosted\s+on\s+([A-Za-z]+\s+\d{1,2},\s+\d{4})\b/i,

    // Month and year only: January 2021
    /\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}\b/g,

    // Year only (as fallback): 2021
    /\b(20\d{2})\b/g,
  ];

  const dates: string[] = [];

  for (const pattern of datePatterns) {
    const matches = content.match(pattern);
    if (matches) {
      dates.push(...matches);
    }
  }

  return [...new Set(dates)]; // Remove duplicates
};

/**
 * Extract the most recent valid date from content
 * @param content Text content to extract date from
 * @returns Most recent Date object or null if none found
 */
export const extractMostRecentDateFromContent = (content: string): Date | null => {
  const dates = extractDatesFromContent(content);
  if (dates.length === 0) return null;

  // Convert all valid dates to Date objects
  const dateObjects = dates
    .map((dateStr) => {
      try {
        const date = new Date(dateStr);
        // Ignore dates that are in the future or invalid
        if (date > new Date() || isNaN(date.getTime())) {
          return null;
        }
        return date;
      } catch (e) {
        return null;
      }
    })
    .filter((date): date is Date => date !== null);

  if (dateObjects.length === 0) return null;

  // Return the most recent date
  return new Date(Math.max(...dateObjects.map((d) => d.getTime())));
};

/**
 * Safely parse a date string with fallback
 * @param dateString Date string to parse
 * @param fallback Fallback date if parsing fails
 * @returns Parsed Date object or fallback
 */
export const safeParseDate = (dateString: string, fallback?: Date): Date | null => {
  if (!dateString || typeof dateString !== "string") {
    return fallback || null;
  }

  try {
    // Handle PDF date format like "D:20210815120000Z"
    if (dateString.startsWith("D:")) {
      const cleanDateString = dateString.substring(2, 16); // Extract YYYYMMDDHHMMSS
      const year = cleanDateString.substring(0, 4);
      const month = cleanDateString.substring(4, 6);
      const day = cleanDateString.substring(6, 8);
      const hour = cleanDateString.substring(8, 10) || "00";
      const minute = cleanDateString.substring(10, 12) || "00";
      const second = cleanDateString.substring(12, 14) || "00";

      const isoString = `${year}-${month}-${day}T${hour}:${minute}:${second}Z`;
      const date = new Date(isoString);

      if (!isNaN(date.getTime())) {
        return date;
      }
    }

    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      return date;
    }

    return fallback || null;
  } catch (error) {
    return fallback || null;
  }
};

/**
 * Calculate content age in seconds from metadata and content
 * @param metadata Metadata object that may contain date fields
 * @param content Text content to extract dates from
 * @returns Age in seconds
 */
export const calculateContentAge = (metadata: Record<string, any>, content: string): number => {
  const now = new Date();
  let contentDate: Date | null = null;

  // First try to extract date from metadata
  if (metadata.lastModified && !metadata.lastModified.includes("D:")) {
    contentDate = safeParseDate(metadata.lastModified);
  } else if (metadata.createdAt) {
    contentDate = safeParseDate(metadata.createdAt);
  } else if (metadata.extractedDate) {
    contentDate = safeParseDate(metadata.extractedDate);
  } else {
    // Try to extract date from content
    contentDate = extractMostRecentDateFromContent(content);
  }

  // If we couldn't determine a date, default to 10 years old
  if (!contentDate || isNaN(contentDate.getTime())) {
    return 315360000; // 10 years in seconds
  }

  // If the date is more than 2 years old, apply a significant penalty
  const ageInSeconds = (now.getTime() - contentDate.getTime()) / 1000;
  const twoYearsInSeconds = 63072000; // 2 years in seconds

  if (ageInSeconds > twoYearsInSeconds) {
    return ageInSeconds * 2; // Double the age penalty for content older than 2 years
  }

  return ageInSeconds;
};

/**
 * Extract publication date from archive URL or content
 * @param url URL that might contain date information
 * @param content Text content to extract dates from
 * @param metadata Metadata object that may contain date fields
 * @returns Publication Date object or null
 */
export const extractPublicationDate = (url: string, content: string, metadata: Record<string, any>): Date | null => {
  // First check if we have a valid date in metadata
  if (metadata.publicationDate) {
    const pubDate = safeParseDate(metadata.publicationDate);
    if (pubDate) return pubDate;
  }

  // For archive URLs, try to extract date from URL structure or page content
  if (url.includes("/Archive.aspx") || url.includes("/ArchiveCenter/")) {
    // Check for date in content first - specifically look for "Posted on" or similar phrases
    const postedDateMatch = content.match(/Posted\s+on\s+([A-Za-z]+\s+\d{1,2},\s+\d{4})/i);
    if (postedDateMatch && postedDateMatch[1]) {
      const postedDate = safeParseDate(postedDateMatch[1]);
      if (postedDate) return postedDate;
    }

    // Look for month/year patterns that might indicate publication date
    const monthYearMatch = content.match(
      /\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}\b/i,
    );
    if (monthYearMatch && monthYearMatch[0]) {
      const monthYearDate = safeParseDate(monthYearMatch[0]);
      if (monthYearDate) return monthYearDate;
    }
  }

  // Fall back to extracting any dates from content
  return extractMostRecentDateFromContent(content);
};

/**
 * Check if a date is within a reasonable range (not too old, not in future)
 * @param date Date to validate
 * @param maxAgeYears Maximum age in years (default: 20)
 * @returns Boolean indicating if date is valid
 */
export const isReasonableDate = (date: Date, maxAgeYears = 20): boolean => {
  if (!date || isNaN(date.getTime())) {
    return false;
  }

  const now = new Date();
  const maxAge = maxAgeYears * 365 * 24 * 60 * 60 * 1000; // Convert years to milliseconds
  const minDate = new Date(now.getTime() - maxAge);

  return date >= minDate && date <= now;
};

/**
 * Format a date for display
 * @param date Date to format
 * @param format Format type ('short', 'medium', 'long')
 * @returns Formatted date string
 */
export const formatDate = (date: Date, format: "short" | "medium" | "long" = "medium"): string => {
  if (!date || isNaN(date.getTime())) {
    return "Invalid Date";
  }

  const options: Intl.DateTimeFormatOptions = {
    short: { year: "numeric", month: "numeric", day: "numeric" },
    medium: { year: "numeric", month: "short", day: "numeric" },
    long: { year: "numeric", month: "long", day: "numeric" },
  }[format];

  return date.toLocaleDateString("en-US", options);
};
