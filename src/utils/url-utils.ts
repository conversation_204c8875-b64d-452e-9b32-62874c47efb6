/**
 * URL utility functions for parsing, validation, and manipulation
 */

/**
 * Extract domain and TLD from a URL string
 * @param urlString URL to extract domain from
 * @returns Domain and TLD (e.g., "example.com")
 */
export const extractDomainAndTld = (urlString: string): string => {
  try {
    // Handle relative URLs
    if (urlString.startsWith("/")) {
      return ""; // Will be handled separately
    }

    // Parse URL
    const url = new URL(urlString);
    const hostname = url.hostname;

    // Extract domain and TLD by removing subdomains
    const parts = hostname.split(".");
    if (parts.length > 2) {
      // Handle special cases like co.uk, com.au
      if (
        parts.length > 2 &&
        (parts[parts.length - 2] === "co" || parts[parts.length - 2] === "com" || parts[parts.length - 2] === "gov") &&
        parts[parts.length - 1].length === 2
      ) {
        // For cases like example.co.uk, return co.uk
        return parts.slice(-3).join(".");
      }
      // For normal subdomains like sub.example.com, return example.com
      return parts.slice(-2).join(".");
    }

    // Already a base domain like example.com
    return hostname;
  } catch (error) {
    throw new Error(`Error extracting domain from URL: ${urlString} - ${error}`);
  }
};

/**
 * Check if a URL should be processed based on common patterns
 * @param url URL to check
 * @returns Boolean indicating if URL should be processed
 */
export const shouldProcessUrl = (url: string): boolean => {
  // Skip fragment URLs (they point to the same page)
  if (url.includes("#")) {
    return false;
  }

  // Skip common non-content URLs
  const skipPatterns = [
    "javascript:",
    "mailto:",
    "tel:",
    "sms:",
    "whatsapp:",
    "facebook.com",
    "twitter.com",
    "instagram.com",
    "linkedin.com",
    "youtube.com",
    "google.com",
    "print=yes",
    "print=true",
    "print.html",
    "print.php",
    "print.aspx",
    "print.jsp",
    "calendar",
    "login",
    "logout",
    "signin",
    "signout",
    "register",
    "search",
    "rss",
    "feed",
    "api",
    "banner",
    "advertisement",
    "ads",
  ];

  const urlLower = url.toLowerCase();
  for (const pattern of skipPatterns) {
    if (urlLower.includes(pattern)) {
      return false;
    }
  }

  return true;
};

/**
 * Resolve a relative URL to an absolute URL
 * @param url Relative or absolute URL
 * @param baseUrl Base URL for resolving relative URLs
 * @param directoryPath Directory path for resolving relative URLs
 * @returns Absolute URL
 */
export const resolveUrl = (url: string, baseUrl: string, directoryPath?: string): string => {
  try {
    // Handle relative URLs
    if (url.startsWith("/")) {
      // Absolute path relative to domain root
      return new URL(url, baseUrl).href;
    } else if (!url.includes("://")) {
      // Relative path (no protocol)
      const resolveBase = directoryPath ? `${baseUrl}${directoryPath}` : baseUrl;
      return new URL(url, resolveBase).href;
    } else {
      // Already absolute URL
      return url;
    }
  } catch (error) {
    throw new Error(`Error resolving URL: ${url} with base: ${baseUrl} - ${error}`);
  }
};

/**
 * Remove fragments from URL
 * @param url URL to clean
 * @returns URL without fragment
 */
export const removeFragment = (url: string): string => {
  if (url.includes("#")) {
    return url.split("#")[0];
  }
  return url;
};

/**
 * Check if two URLs belong to the same domain
 * @param url1 First URL
 * @param url2 Second URL
 * @returns Boolean indicating if URLs are from same domain
 */
export const isSameDomain = (url1: string, url2: string): boolean => {
  try {
    const domain1 = extractDomainAndTld(url1);
    const domain2 = extractDomainAndTld(url2);
    return domain1 === domain2;
  } catch (error) {
    return false;
  }
};

/**
 * Validate if a string is a valid URL
 * @param urlString String to validate
 * @returns Boolean indicating if string is valid URL
 */
export const isValidUrl = (urlString: string): boolean => {
  try {
    new URL(urlString);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Get the base URL (protocol + hostname + port) from a URL
 * @param url URL to extract base from
 * @returns Base URL
 */
export const getBaseUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    return urlObj.origin;
  } catch (error) {
    throw new Error(`Error extracting base URL from: ${url} - ${error}`);
  }
};

/**
 * Get the directory path from a URL
 * @param url URL to extract directory path from
 * @returns Directory path
 */
export const getDirectoryPath = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    return pathname.substring(0, pathname.lastIndexOf("/") + 1);
  } catch (error) {
    throw new Error(`Error extracting directory path from: ${url} - ${error}`);
  }
};

/**
 * Normalize a URL by removing fragments and trailing slashes
 * @param url URL to normalize
 * @returns Normalized URL
 */
export const normalizeUrl = (url: string): string => {
  try {
    let normalized = removeFragment(url);

    // Remove trailing slash unless it's the root path
    if (normalized.endsWith("/") && normalized !== new URL(normalized).origin + "/") {
      normalized = normalized.slice(0, -1);
    }

    return normalized;
  } catch (error) {
    throw new Error(`Error normalizing URL: ${url} - ${error}`);
  }
};

/**
 * Check if URL contains banner or advertisement content
 * @param url URL to check
 * @returns Object with skip flag and reason
 */
export const shouldSkipUrl = (url: string): { skip: boolean; reason?: string } => {
  const urlLower = url.toLowerCase();

  if (urlLower.includes("banner")) {
    return { skip: true, reason: "URL contains 'banner', probably just a picture" };
  }

  if (urlLower.includes("advertisement") || urlLower.includes("ads")) {
    return { skip: true, reason: "URL contains advertisement content" };
  }

  return { skip: false };
};

/**
 * Extract file extension from URL
 * @param url URL to extract extension from
 * @returns File extension without dot, or empty string if none
 */
export const getUrlExtension = (url: string): string => {
  try {
    // Remove query parameters and fragments
    const cleanUrl = url.split("?")[0].split("#")[0];
    const filename = cleanUrl.split("/").pop() || "";
    const parts = filename.split(".");

    if (parts.length > 1) {
      return parts.pop()?.toLowerCase() || "";
    }

    return "";
  } catch (error) {
    return "";
  }
};

/**
 * Check if URL points to a file based on extension
 * @param url URL to check
 * @returns Boolean indicating if URL points to a file
 */
export const isFileUrl = (url: string): boolean => {
  const extensions = [".pdf", ".docx", ".doc", ".csv", ".xlsx", ".xls", ".ppt", ".pptx", ".txt"];
  const lowerUrl = url.toLowerCase();

  // Check for known file extensions
  if (extensions.some((ext) => lowerUrl.endsWith(ext))) {
    return true;
  }

  // Check for relative paths that look like file links
  if (url.startsWith("/") && url.includes("/media/") && extensions.some((ext) => url.includes(ext))) {
    return true;
  }

  // Special handling for CivicPlus DocumentCenter links
  if (lowerUrl.includes("/documentcenter/view/") || lowerUrl.includes("/archive.aspx?adid=")) {
    return true;
  }

  // Remove the problematic logic that treats all extensionless URLs as files
  return false;
};
