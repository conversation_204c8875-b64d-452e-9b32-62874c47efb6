import { logger } from "@/services/logger";
import { backgroundJobService } from "@/services/jobs/background-job-service";

async function main() {
  logger.info("Starting background job processor...");

  // Handle graceful shutdown
  const shutdown = async (signal: string) => {
    logger.info(`Received ${signal}, shutting down gracefully...`);
    try {
      await backgroundJobService.stop();
      process.exit(0);
    } catch (error) {
      logger.error("Error during shutdown:", error);
      process.exit(1);
    }
  };

  process.on("SIGTERM", () => shutdown("SIGTERM"));
  process.on("SIGINT", () => shutdown("SIGINT"));

  try {
    // Initialize and start the background job service
    await backgroundJobService.initialize();
    await backgroundJobService.start();

    // Keep the process running
    logger.info("Background job processor is running. Press Ctrl+C to stop.");
    
    // Set up periodic health checks
    setInterval(async () => {
      try {
        const health = await backgroundJobService.healthCheck();
        logger.info(`Health check: ${JSON.stringify(health)}`);
      } catch (error) {
        logger.error("Health check failed:", error);
      }
    }, 60000); // Every minute

    // Set up periodic cleanup
    setInterval(async () => {
      try {
        await backgroundJobService.cleanupOldJobs(7); // Clean up jobs older than 7 days
      } catch (error) {
        logger.error("Cleanup failed:", error);
      }
    }, 24 * 60 * 60 * 1000); // Every 24 hours

  } catch (error) {
    logger.error("Failed to start background job processor:", error);
    process.exit(1);
  }
}

// Handle unhandled rejections
process.on("unhandledRejection", (reason, promise) => {
  logger.error("Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

process.on("uncaughtException", (error) => {
  logger.error("Uncaught Exception:", error);
  process.exit(1);
});

void main();
