import { initDb } from "@/store/instance";
import { Site } from "@/store/models/site";
import { SiteLink } from "@/store/models/site_link";
import { logger } from "@/services/logger";

import { Op } from "sequelize";

async function addSpecificUrl(siteUrl: string, specificUrl: string) {
  try {
    await initDb();

    // Find site with ILIKE match or create new
    let site = await Site.findOne({
      where: {
        url: {
          [Op.iLike]: siteUrl,
        },
      },
    });

    if (!site) {
      throw new Error(`Site with URL ${siteUrl} not found`);

      // site = await Site.create({
      //   name: siteUrl,
      //   clientId: "1",
      //   schema: "client_1",
      //   requiresJavascript: false,
      //   lastScrapedAt: undefined,
      //   url: siteUrl,
      //   enabled: true,
      // });
    }

    const siteId = site.getDataValue("id");

    // Add the specific URL
    const [link, created] = await SiteLink.findOrCreate({
      where: {
        url: specificUrl,
        siteId,
      },
      defaults: {
        url: specificUrl,
        siteId,
        status: "pending",
        lastScrapedAt: null,
      },
    });

    if (created) {
      logger.info(`Added new link: ${specificUrl}`);
    } else {
      logger.info(`Link already exists: ${specificUrl}`);
    }

    return { success: true };
  } catch (error) {
    logger.error("Error adding specific URL:", error);
    throw error;
  }
}

// Get command line arguments
const args = process.argv.slice(2);
if (args.length < 2) {
  logger.error("Usage: npm run add-specific-url -- <site-url> <specific-url>");
  process.exit(1);
}

const siteUrl = args[0];
const specificUrl = args[1];

void addSpecificUrl(siteUrl, specificUrl)
  .then(() => {
    logger.info(`Successfully added ${specificUrl} to site ${siteUrl}`);
    process.exit(0);
  })
  .catch(() => {
    process.exit(1);
  });
