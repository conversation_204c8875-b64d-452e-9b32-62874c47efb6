import { initDb } from "@/store/instance";
import { Site } from "@/store/models/site";
import { SiteLink } from "@/store/models/site_link";
import { FileLink } from "@/store/models/file_link";
import { Embedding } from "@/store/models/embedding";
import { logger } from "@/services/logger";
import { sequelize } from "@/store/instance";

async function deleteSiteData(siteUrl: string) {
  await initDb();

  try {
    // Find the site
    const site = await Site.findOne({
      where: { url: siteUrl },
    });

    if (!site) {
      logger.error(`Site with URL ${siteUrl} not found`);
      process.exit(1);
    }

    const siteId = site.getDataValue("id");
    logger.info(`Found site with ID ${siteId} for URL ${siteUrl}`);

    // Start a transaction
    const transaction = await sequelize.transaction();

    try {
      // 1. Delete embeddings associated with the site's links
      const deletedEmbeddings = await sequelize.query(
        `DELETE FROM embeddings 
         WHERE "fromSiteLinkId" IN (
           SELECT id FROM site_links WHERE "siteId" = :siteId
         )`,
        {
          replacements: { siteId },
          type: sequelize.QueryTypes.DELETE,
          transaction,
        },
      );

      logger.info(`Deleted embeddings for site ${siteUrl}`);

      // 2. Delete file links associated with the site's links
      const deletedFileLinks = await FileLink.destroy({
        where: {
          siteLinkId: {
            [sequelize.Op.in]: sequelize.literal(`(SELECT id FROM site_links WHERE "siteId" = ${siteId})`),
          },
        },
        transaction,
      });

      logger.info(`Deleted ${deletedFileLinks} file links for site ${siteUrl}`);

      // 3. Delete site links
      const deletedSiteLinks = await SiteLink.destroy({
        where: { siteId },
        transaction,
      });

      logger.info(`Deleted ${deletedSiteLinks} site links for site ${siteUrl}`);

      // 4. Update the site status but don't delete it
      await site.update(
        {
          lastScrapedAt: null,
          // Keep the site enabled so it can be re-scraped
        },
        { transaction },
      );

      logger.info(`Reset site ${siteUrl} for re-scraping`);

      // Commit the transaction
      await transaction.commit();
      logger.info(`Successfully deleted all data for site ${siteUrl}`);
    } catch (error) {
      // Rollback the transaction if there's an error
      await transaction.rollback();
      logger.error(`Error deleting data for site ${siteUrl}:`, error);
      throw error;
    }
  } catch (error) {
    logger.error(`Error in deleteSiteData:`, error);
    process.exit(1);
  }
}

// Get command line arguments
const args = process.argv.slice(2);
if (args.length < 1) {
  logger.error("Usage: npm run delete-site-data -- <site-url>");
  process.exit(1);
}

const siteUrl = args[0];

void deleteSiteData(siteUrl)
  .then(() => {
    logger.info(`Site data deletion completed for ${siteUrl}`);
    process.exit(0);
  })
  .catch((error) => {
    logger.error("Error:", error);
    process.exit(1);
  });
