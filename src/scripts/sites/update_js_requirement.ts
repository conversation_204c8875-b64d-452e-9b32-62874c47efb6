import { initDb } from "@/store/instance";
import { Site } from "@/store/models/site";
import { logger } from "@/services/logger";
import { fetchWithUserHeaders } from "@/utils/helpers";

// List of known sites that require JavaScript
const KNOWN_JS_SITES = [
  "parkcityks.citycode.net",
  "southbaycity.com",
  // Add other known sites here
];

async function updateJsRequirement() {
  await initDb();

  try {
    // Get all sites
    const sites = await Site.findAll();

    for (const site of sites) {
      const siteUrl = site.getDataValue("url");
      const domain = new URL(siteUrl).hostname;

      // Check if this is a known JS site
      if (KNOWN_JS_SITES.some((jsSite) => domain.includes(jsSite))) {
        logger.info(`Marking ${siteUrl} as requiring JavaScript (known site)`);
        await site.update({ requiresJavascript: true });
        continue;
      }

      // For other sites, check the homepage
      try {
        const response = await fetchWithUserHeaders(siteUrl);
        const html = await response.text();

        const requiresJs =
          html.includes("JavaScript is disabled") ||
          html.includes("enable JavaScript") ||
          html.includes("requires JavaScript") ||
          html.includes("noscript") ||
          html.includes("js-required");

        if (requiresJs) {
          logger.info(`Detected JavaScript requirement for ${siteUrl}`);
          await site.update({ requiresJavascript: true });
        }
      } catch (error) {
        logger.error(`Error checking JavaScript requirement for ${siteUrl}:`, error);
      }
    }

    logger.info("Completed updating JavaScript requirements for all sites");
  } catch (error) {
    logger.error("Error updating JavaScript requirements:", error);
    throw error;
  }
}

// Run the function
void updateJsRequirement()
  .then(() => process.exit(0))
  .catch(() => process.exit(1));
