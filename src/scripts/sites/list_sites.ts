import { Site } from "@/store/models/site";
import { logger } from "@/services/logger";
import { SiteLink } from "@/store/models/site_link";
import { Embedding } from "@/store/models/embedding";
import { sequelize } from "@/store/instance";

async function listSites() {
  try {
    const sites = await Site.findAll({
      order: [["url", "ASC"]],
    });

    if (sites.length === 0) {
      logger.info("No sites found in the database");
      return;
    }

    logger.info(`Found ${sites.length} sites:`);

    for (const site of sites) {
      const siteId = site.getDataValue("id");
      const url = site.getDataValue("url");
      const enabled = site.getDataValue("enabled");
      const lastScraped = site.getDataValue("lastScrapedAt");

      // Count links for this site
      const linkCount = await SiteLink.count({
        where: { siteId },
      });

      // Count processed links
      const processedLinkCount = await SiteLink.count({
        where: {
          siteId,
          status: "scraped",
        },
      });

      // Count embeddings (using raw query for performance)
      const [embeddingResults] = await sequelize.query(`
        SELECT COUNT(*) as count
        FROM embeddings e
        JOIN site_links sl ON e.from_site_link_id = sl.id
        WHERE sl.site_id = ${siteId}
      `);

      const embeddingCount = embeddingResults[0]?.count || 0;

      logger.info(`
Site: ${url}
  - ID: ${siteId}
  - Enabled: ${enabled}
  - Last Scraped: ${lastScraped ? new Date(lastScraped).toLocaleString() : "Never"}
  - Links: ${linkCount} (${processedLinkCount} processed)
  - Embeddings: ${embeddingCount}
      `);
    }
  } catch (error) {
    logger.error("Error listing sites:", error);
    throw error;
  }
}

void listSites()
  .then(() => process.exit(0))
  .catch(() => process.exit(1));
