import { Site } from "@/store/models/site";
import { logger } from "@/services/logger";

async function toggleSite(siteUrl: string, enabled: boolean) {
  const site = await Site.findOne({
    where: { url: siteUrl },
  });

  if (!site) {
    logger.error(`Site with URL ${siteUrl} not found`);
    process.exit(1);
  }

  await site.update({ enabled });
  logger.info(`Site ${siteUrl} has been ${enabled ? "enabled" : "disabled"}`);
}

// Get command line arguments
const args = process.argv.slice(2);
if (args.length < 2) {
  logger.error("Usage: npm run toggle-site -- <site-url> <true|false>");
  process.exit(1);
}

const siteUrl = args[0];
const enabled = args[1].toLowerCase() === "true";

void toggleSite(siteUrl, enabled)
  .then(() => process.exit(0))
  .catch((error: unknown) => {
    logger.error("Error toggling site:", error);
    process.exit(1);
  });
