/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { initDb } from "@/store/instance";
import { Site } from "@/store/models/site";
import { logger } from "@/services/logger";

async function addSite(siteUrl: string, enabled: boolean = true) {
  await initDb();

  try {
    // Normalize URL (ensure it has http/https prefix)
    if (!siteUrl.startsWith("http://") && !siteUrl.startsWith("https://")) {
      siteUrl = "https://" + siteUrl;
    }

    // Remove trailing slash if present
    if (siteUrl.endsWith("/")) {
      siteUrl = siteUrl.slice(0, -1);
    }

    const [site, created] = await Site.findOrCreate({
      where: { url: siteUrl },
      defaults: {
        url: siteUrl,
        enabled: enabled,
      } as any,
    });

    if (created) {
      logger.info(`Site ${siteUrl} added successfully with enabled=${enabled}`);
    } else {
      // Update the existing site's enabled status
      await site.update({ enabled });
      logger.info(`Site ${siteUrl} already exists, updated enabled status to ${enabled}`);
    }
  } catch (error) {
    logger.error(`Error adding site ${siteUrl}:`, error);
    throw error;
  }
}

// Get command line arguments
const args = process.argv.slice(2);
if (args.length < 1) {
  logger.error("Usage: npm run add-site -- <site-url> [true|false]");
  process.exit(1);
}

const siteUrl = args[0];
const enabled = args.length > 1 ? args[1].toLowerCase() === "true" : true;

void addSite(siteUrl, enabled)
  .then(() => process.exit(0))
  .catch(() => process.exit(1));
