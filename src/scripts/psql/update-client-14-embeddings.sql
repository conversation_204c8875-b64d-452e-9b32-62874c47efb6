-- Migration script specifically for client ID 14
-- This script updates embeddings and refreshes materialized views

-- First, save the current maintenance_work_mem setting
DO $$
DECLARE
    original_work_mem TEXT;
BEGIN
    -- Get current setting
    SELECT setting INTO original_work_mem FROM pg_settings WHERE name = 'maintenance_work_mem';
    
    -- Store it for later restoration
    PERFORM set_config('my.original_maintenance_work_mem', original_work_mem, false);
    
    -- Increase maintenance_work_mem for better performance
    EXECUTE 'SET maintenance_work_mem = ''512MB''';
    RAISE NOTICE 'Temporarily increased maintenance_work_mem from % to 512MB', original_work_mem;
END $$;

-- Update site_id for client 14 embeddings
DO $$
DECLARE
    updated_count INTEGER;
    schema_name TEXT := 'client_14';
BEGIN
    RAISE NOTICE 'Updating embeddings for client_id 14...';
    
    -- Update embeddings that have from_site_link_id but no site_id
    UPDATE embeddings e
    SET site_id = sl.site_id
    FROM site_links sl
    JOIN sites s ON sl.site_id = s.id
    WHERE e.from_site_link_id = sl.id
    AND e.site_id IS NULL
    AND s.client_id = '14';
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % embeddings with site_id from site_links', updated_count;
    
    -- Update file embeddings that have metadata.file_link_id but no site_id
    UPDATE embeddings e
    SET site_id = fl.site_id
    FROM file_links fl
    JOIN site_links sl ON fl.site_link_id = sl.id
    JOIN sites s ON sl.site_id = s.id
    WHERE (e.metadata->>'file_link_id')::integer = fl.id
    AND e.site_id IS NULL
    AND s.client_id = '14';
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % file embeddings with site_id', updated_count;
    
    -- Refresh the materialized views for client 14
    IF EXISTS (
        SELECT 1 FROM pg_matviews 
        WHERE schemaname = schema_name 
        AND matviewname = 'mv_embeddings'
    ) THEN
        EXECUTE format('REFRESH MATERIALIZED VIEW %I.mv_embeddings', schema_name);
        RAISE NOTICE 'Refreshed materialized view: %.mv_embeddings', schema_name;
    ELSE
        RAISE NOTICE 'Materialized view %.mv_embeddings does not exist', schema_name;
    END IF;
    
    -- Run ANALYZE to update statistics
    EXECUTE format('ANALYZE %I.mv_embeddings', schema_name);
    RAISE NOTICE 'Updated statistics for %.mv_embeddings', schema_name;
END $$;

-- Restore the original maintenance_work_mem setting
DO $$
DECLARE
    original_work_mem TEXT;
BEGIN
    -- Get the saved setting
    SELECT current_setting('my.original_maintenance_work_mem') INTO original_work_mem;
    
    -- Restore it
    EXECUTE format('SET maintenance_work_mem = %L', original_work_mem);
    RAISE NOTICE 'Restored maintenance_work_mem to %', original_work_mem;
END $$;

-- Report on the current state of embeddings for client 14
SELECT 
    'Client 14 embeddings summary:',
    COUNT(*) AS total_embeddings,
    COUNT(CASE WHEN site_id IS NOT NULL THEN 1 END) AS embeddings_with_site_id,
    COUNT(CASE WHEN site_id IS NULL THEN 1 END) AS embeddings_without_site_id
FROM embeddings e
JOIN site_links sl ON e.from_site_link_id = sl.id
JOIN sites s ON sl.site_id = s.id
WHERE s.client_id = '14';

-- Check file embeddings separately
SELECT 
    'Client 14 file embeddings summary:',
    COUNT(*) AS total_file_embeddings,
    COUNT(CASE WHEN e.site_id IS NOT NULL THEN 1 END) AS file_embeddings_with_site_id,
    COUNT(CASE WHEN e.site_id IS NULL THEN 1 END) AS file_embeddings_without_site_id
FROM embeddings e
JOIN file_links fl ON (e.metadata->>'file_link_id')::integer = fl.id
JOIN site_links sl ON fl.site_link_id = sl.id
JOIN sites s ON sl.site_id = s.id
WHERE s.client_id = '14';