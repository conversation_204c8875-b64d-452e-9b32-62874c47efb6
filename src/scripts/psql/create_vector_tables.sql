-- Check if pgvector extension is installed
CREATE EXTENSION IF NOT EXISTS vector;

-- Table to track sync status
CREATE TABLE IF NOT EXISTS embedding_sync_status (
  id SERIAL PRIMARY KEY,
  embedding_type VARCHAR(50) NOT NULL,
  last_synced_at TIMESTAMP NOT NULL,
  records_synced INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Table for answer embeddings
CREATE TABLE IF NOT EXISTS answer_embeddings (
  id SERIAL PRIMARY KEY,
  supabase_id BIGINT UNIQUE NOT NULL,
  question TEXT,
  question_embedding VECTOR(1536),
  transcription TEXT,
  transcription_embedding VECTOR(1536),
  repd_answer_id BIGINT,
  repd_client_id BIGINT,
  question_es TEXT,
  question_es_embedding VECTOR(1536),
  transcription_es TEXT,
  transcription_es_embedding VECTOR(1536),
  created_at TIMESTAMP,
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Table for question embeddings
CREATE TABLE IF NOT EXISTS question_embeddings (
  id SERIAL PRIMARY KEY,
  original_id BIGINT UNIQUE NOT NULL,
  question TEXT NOT NULL,
  answer TEXT,
  embedding VECTOR(1536),
  related_embeddings TEXT,
  question_embedding_token_usage INTEGER,
  answer_token_usage INTEGER,
  search_time FLOAT,
  get_question_embedding_time FLOAT,
  answering_time FLOAT,
  sources JSONB,
  created_at TIMESTAMP,
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for vector similarity search
CREATE INDEX IF NOT EXISTS answer_question_embedding_idx ON answer_embeddings USING ivfflat (question_embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS answer_transcription_embedding_idx ON answer_embeddings USING ivfflat (transcription_embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS answer_question_es_embedding_idx ON answer_embeddings USING ivfflat (question_es_embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS answer_transcription_es_embedding_idx ON answer_embeddings USING ivfflat (transcription_es_embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS question_embedding_idx ON question_embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Create regular indexes for faster lookups
CREATE INDEX IF NOT EXISTS answer_repd_client_id_idx ON answer_embeddings(repd_client_id);
CREATE INDEX IF NOT EXISTS answer_repd_answer_id_idx ON answer_embeddings(repd_answer_id);
CREATE INDEX IF NOT EXISTS question_original_id_idx ON question_embeddings(original_id);