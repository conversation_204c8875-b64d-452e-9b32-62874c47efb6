-- Update site_id for embeddings that have from_site_link_id
UPDATE embeddings e
SET site_id = sl.site_id
FROM site_links sl
WHERE e.from_site_link_id = sl.id
AND e.site_id IS NULL;

-- Update site_id for file embeddings that have metadata.file_link_id
UPDATE embeddings e
SET site_id = fl.site_id
FROM file_links fl
JOIN site_links sl ON fl.site_link_id = sl.id
WHERE (e.metadata->>'file_link_id')::integer = fl.id
AND e.site_id IS NULL;

-- Log the results
DO $$
DECLARE
    updated_count INTEGER;
    remaining_null INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    SELECT COUNT(*) INTO remaining_null 
    FROM embeddings 
    WHERE site_id IS NULL;
    
    RAISE NOTICE 'Updated % embeddings with site_id', updated_count;
    RAISE NOTICE '% embeddings still have NULL site_id', remaining_null;
END $$;