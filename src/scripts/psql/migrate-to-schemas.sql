-- <PERSON><PERSON>t to migrate data into client-specific schemas (incremental approach)
-- Usage: psql -U username -d database_name -f src/scripts/psql/migrate-to-schemas.sql

-- First, save the current maintenance_work_mem setting
DO $$
DECLARE
    original_work_mem TEXT;
BEGIN
    -- Get current setting
    SELECT setting INTO original_work_mem FROM pg_settings WHERE name = 'maintenance_work_mem';

    -- Store it for later restoration
    PERFORM set_config('my.original_maintenance_work_mem', original_work_mem, false);

    -- Increase maintenance_work_mem to 256MB for index creation
    EXECUTE 'SET maintenance_work_mem = ''256MB''';
    RAISE NOTICE 'Temporarily increased maintenance_work_mem from % to 256MB', original_work_mem;
END $$;

-- Function to create schema for a client
CREATE OR REPLACE FUNCTION create_client_schema(client_id_param INTEGER) RETURNS VOID AS $$
DECLARE
    schema_name_var TEXT;
    schema_exists BOOLEAN;
BEGIN
    -- Create schema name from client ID
    schema_name_var := 'client_' || client_id_param;

    -- Check if schema already exists
    SELECT EXISTS(
        SELECT 1 FROM information_schema.schemata WHERE schema_name = schema_name_var
    ) INTO schema_exists;

    RAISE NOTICE 'Creating schema: % (exists: %)', schema_name_var, schema_exists;

    -- Create schema if it doesn't exist
    EXECUTE format('CREATE SCHEMA IF NOT EXISTS %I;', schema_name_var);
    
    -- Process each table separately
    PERFORM migrate_sites_table(client_id_param, schema_name_var);
    PERFORM migrate_site_links_table(client_id_param, schema_name_var);
    PERFORM migrate_file_links_table(client_id_param, schema_name_var);
    PERFORM migrate_embeddings_table(client_id_param, schema_name_var);
    
    -- Update the schema column in the sites table
    EXECUTE format('
        UPDATE public.sites
        SET schema = %L
        WHERE client_id = %L
        AND (schema IS NULL OR schema <> %L);
    ', schema_name_var, client_id_param, schema_name_var);
END;
$$ LANGUAGE plpgsql;

-- Function to migrate sites table
CREATE OR REPLACE FUNCTION migrate_sites_table(client_id_param INTEGER, schema_name_var TEXT) RETURNS VOID AS $$
DECLARE
    target_columns TEXT;
BEGIN
    RAISE NOTICE 'Migrating sites table for client %', client_id_param;
    
    -- Create the sites table in the new schema
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS %I.sites (LIKE public.sites INCLUDING ALL);
    ', schema_name_var);

    -- Get ordered columns for the sites table
    SELECT string_agg(quote_ident(column_name), ', ')
    INTO target_columns
    FROM (
        SELECT column_name
        FROM information_schema.columns
        WHERE table_schema = schema_name_var
          AND table_name = 'sites'
        ORDER BY ordinal_position
    ) ordered_columns;

    -- Use explicit column list
    EXECUTE format('
        INSERT INTO %I.sites (%s)
        SELECT %s FROM public.sites
        WHERE client_id = %L
        AND id NOT IN (SELECT id FROM %I.sites)
        ON CONFLICT (id) DO NOTHING;
    ', schema_name_var, target_columns, target_columns, client_id_param, schema_name_var);

    -- Create index on sites table if it doesn't exist
    EXECUTE format('
        CREATE INDEX IF NOT EXISTS idx_%s_sites_client_id ON %I.sites(client_id);
    ', client_id_param, schema_name_var);
END;
$$ LANGUAGE plpgsql;

-- Function to migrate site_links table
CREATE OR REPLACE FUNCTION migrate_site_links_table(
    client_id_param INTEGER,
    schema_name_var TEXT
) RETURNS VOID AS $$
DECLARE
    insert_cols  TEXT;  -- columns for INSERT clause
    select_cols  TEXT;  -- columns for SELECT clause, each prefixed with sl.
BEGIN
    RAISE NOTICE 'Migrating site_links table for client %', client_id_param;

    -- Ensure target table exists
    EXECUTE format(
        'CREATE TABLE IF NOT EXISTS %I.site_links (LIKE public.site_links INCLUDING ALL);',
        schema_name_var
    );

    /*------------------------------------------------------------------
      Build two comma-separated column lists:
      • insert_cols: id, site_id, url, …
      • select_cols: sl.id, sl.site_id, sl.url, …
    ------------------------------------------------------------------*/
    SELECT string_agg(quote_ident(column_name), ', ')
    INTO   insert_cols
    FROM   information_schema.columns
    WHERE  table_schema = schema_name_var
      AND  table_name   = 'site_links';

    SELECT string_agg('sl.' || quote_ident(column_name), ', ')
    INTO   select_cols
    FROM   information_schema.columns
    WHERE  table_schema = schema_name_var
      AND  table_name   = 'site_links';

    /*------------------------------------------------------------------
      Insert data for this client, skipping rows already copied
    ------------------------------------------------------------------*/
    EXECUTE format($f$
        INSERT INTO %I.site_links (%s)
        SELECT %s
        FROM   public.site_links sl
        JOIN   public.sites      s  ON sl.site_id = s.id
        WHERE  s.client_id = %L
          AND  sl.id NOT IN (SELECT id FROM %I.site_links)
        ON CONFLICT (id) DO NOTHING;
    $f$, schema_name_var, insert_cols, select_cols,
        client_id_param, schema_name_var);

    /*------------------------------------------------------------------
      Create (or keep) helpful indexes
    ------------------------------------------------------------------*/
    EXECUTE format(
        'CREATE INDEX IF NOT EXISTS idx_%s_site_links_site_id ON %I.site_links(site_id);',
        client_id_param, schema_name_var
    );

    EXECUTE format(
        'CREATE INDEX IF NOT EXISTS idx_%s_site_links_url ON %I.site_links(url);',
        client_id_param, schema_name_var
    );
END;
$$ LANGUAGE plpgsql;

-- Function to migrate file_links table
CREATE OR REPLACE FUNCTION migrate_file_links_table(
    client_id_param INTEGER,
    schema_name_var TEXT
) RETURNS VOID AS $$
DECLARE
    insert_cols  TEXT;  -- columns for INSERT clause
    select_cols  TEXT;  -- columns for SELECT clause, each prefixed with fl.
BEGIN
    RAISE NOTICE 'Migrating file_links table for client %', client_id_param;

    ----------------------------------------------------------------------
    -- Ensure the target table exists
    ----------------------------------------------------------------------
    EXECUTE format(
        'CREATE TABLE IF NOT EXISTS %I.file_links (LIKE public.file_links INCLUDING ALL);',
        schema_name_var
    );

    ----------------------------------------------------------------------
    -- Build the column lists
    ----------------------------------------------------------------------
    -- e.g. insert_cols  = id, site_link_id, url, created_at, …
    --      select_cols  = fl.id, fl.site_link_id, fl.url, fl.created_at, …
    SELECT string_agg(quote_ident(column_name), ', ')
    INTO   insert_cols
    FROM   information_schema.columns
    WHERE  table_schema = schema_name_var
      AND  table_name   = 'file_links';

    SELECT string_agg('fl.' || quote_ident(column_name), ', ')
    INTO   select_cols
    FROM   information_schema.columns
    WHERE  table_schema = schema_name_var
      AND  table_name   = 'file_links';

    ----------------------------------------------------------------------
    -- Copy rows belonging to this client, skipping any already copied
    ----------------------------------------------------------------------
    EXECUTE format($f$
        INSERT INTO %I.file_links (%s)
        SELECT %s
        FROM   public.file_links fl
        JOIN   public.site_links sl ON fl.site_link_id = sl.id
        JOIN   public.sites      s  ON sl.site_id      = s.id
        WHERE  s.client_id = %L
          AND  fl.id NOT IN (SELECT id FROM %I.file_links)
        ON CONFLICT (id) DO NOTHING;
    $f$, schema_name_var, insert_cols, select_cols,
        client_id_param, schema_name_var);

    ----------------------------------------------------------------------
    -- Helpful indexes (create if missing)
    ----------------------------------------------------------------------
    EXECUTE format(
        'CREATE INDEX IF NOT EXISTS idx_%s_file_links_site_link_id ON %I.file_links(site_link_id);',
        client_id_param, schema_name_var
    );

    EXECUTE format(
        'CREATE INDEX IF NOT EXISTS idx_%s_file_links_url ON %I.file_links(url);',
        client_id_param, schema_name_var
    );
END;
$$ LANGUAGE plpgsql;

-- Function to migrate embeddings table
CREATE OR REPLACE FUNCTION migrate_embeddings_table(
    client_id_param INTEGER,
    schema_name_var TEXT
) RETURNS VOID AS $$
DECLARE
    insert_cols  TEXT;  -- columns for INSERT clause
    select_cols  TEXT;  -- columns for SELECT clause, each prefixed with e.
BEGIN
    RAISE NOTICE 'Migrating embeddings table for client %', client_id_param;

    ----------------------------------------------------------------------
    -- Ensure the target table exists
    ----------------------------------------------------------------------
    EXECUTE format(
        'CREATE TABLE IF NOT EXISTS %I.embeddings (LIKE public.embeddings INCLUDING ALL);',
        schema_name_var
    );

    ----------------------------------------------------------------------
    -- Build the column lists
    ----------------------------------------------------------------------
    -- Example:
    --   insert_cols = id, site_id, from_site_link_id, embedding, metadata, ...
    --   select_cols = e.id, e.site_id, e.from_site_link_id, e.embedding, e.metadata, ...
    SELECT string_agg(quote_ident(column_name), ', ')
    INTO   insert_cols
    FROM   information_schema.columns
    WHERE  table_schema = schema_name_var
      AND  table_name   = 'embeddings';

    SELECT string_agg('e.' || quote_ident(column_name), ', ')
    INTO   select_cols
    FROM   information_schema.columns
    WHERE  table_schema = schema_name_var
      AND  table_name   = 'embeddings';

    ----------------------------------------------------------------------
    -- Copy rows for this client, skipping those already migrated
    ----------------------------------------------------------------------
    EXECUTE format($f$
        INSERT INTO %I.embeddings (%s)
        SELECT %s
        FROM   public.embeddings e
        LEFT   JOIN public.site_links sl ON e.from_site_link_id = sl.id
        LEFT   JOIN public.sites      s  ON COALESCE(e.site_id, sl.site_id) = s.id
        WHERE  s.client_id = %L
          AND  e.id NOT IN (SELECT id FROM %I.embeddings)
        ON CONFLICT (id) DO NOTHING;
    $f$, schema_name_var, insert_cols, select_cols,
        client_id_param, schema_name_var);

    ----------------------------------------------------------------------
    -- Helpful indexes (create if missing)
    ----------------------------------------------------------------------
    EXECUTE format(
        'CREATE INDEX IF NOT EXISTS idx_%s_embeddings_from_site_link_id ON %I.embeddings(from_site_link_id);',
        client_id_param, schema_name_var
    );

    EXECUTE format(
        'CREATE INDEX IF NOT EXISTS idx_%s_embeddings_metadata ON %I.embeddings USING gin(metadata);',
        client_id_param, schema_name_var
    );

    ----------------------------------------------------------------------
    -- Optional vector index if pgvector is installed
    ----------------------------------------------------------------------
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'vector') THEN
        EXECUTE format(
            $f$CREATE INDEX IF NOT EXISTS idx_%s_embeddings_embedding
              ON %I.embeddings
              USING ivfflat (embedding vector_cosine_ops)
              WITH (lists = 100);$f$,
            client_id_param, schema_name_var
        );
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Loop through each client ID in the sites table
DO $$
DECLARE
    client_record RECORD;
BEGIN
    FOR client_record IN SELECT DISTINCT client_id FROM sites WHERE client_id < 250 LOOP
        RAISE NOTICE 'Processing client_id: %', client_record.client_id;

        -- Call separate functions for each client
        PERFORM create_client_schema(client_record.client_id);
    END LOOP;
END $$;
DO $$
DECLARE
    client_record RECORD;
BEGIN
    FOR client_record IN SELECT DISTINCT client_id FROM sites WHERE client_id < 300 LOOP
        RAISE NOTICE 'Processing client_id: %', client_record.client_id;

        -- Call separate functions for each client
        PERFORM create_client_schema(client_record.client_id);
    END LOOP;
END $$;
DO $$
DECLARE
    client_record RECORD;
BEGIN
    FOR client_record IN SELECT DISTINCT client_id FROM sites WHERE client_id < 350 LOOP
        RAISE NOTICE 'Processing client_id: %', client_record.client_id;

        -- Call separate functions for each client
        PERFORM create_client_schema(client_record.client_id);
    END LOOP;
END $$;

-- Restore the original maintenance_work_mem setting
DO $$
DECLARE
    original_work_mem TEXT;
BEGIN
    -- Get the saved setting
    SELECT current_setting('my.original_maintenance_work_mem') INTO original_work_mem;

    -- Restore it
    EXECUTE format('SET maintenance_work_mem = %L', original_work_mem);
    RAISE NOTICE 'Restored maintenance_work_mem to %', original_work_mem;
END $$;


-- Clean up temporary functions
DROP FUNCTION IF EXISTS create_client_schema;
DROP FUNCTION IF EXISTS migrate_sites_table;
DROP FUNCTION IF EXISTS migrate_site_links_table;
DROP FUNCTION IF EXISTS migrate_file_links_table;
DROP FUNCTION IF EXISTS migrate_embeddings_table;
