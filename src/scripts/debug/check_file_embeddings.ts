import { initDb } from "@/store/instance";
import { logger } from "@/services/logger";
import { config } from "@/config";
import { Pool } from "pg";

async function checkFileEmbeddings() {
  await initDb();

  // Create a direct database connection to query the embeddings table
  const pool = new Pool({
    connectionString: config.databaseUrl,
  });

  try {
    logger.info("Checking for file embeddings...");

    // Check for recent embeddings (last 2 hours)
    const recentResult = await pool.query(`
      SELECT COUNT(*) as count 
      FROM embeddings 
      WHERE created_at > NOW() - INTERVAL '2 hours'
    `);
    const recentEmbeddings = parseInt(recentResult.rows[0].count);
    logger.info(`Recent embeddings (last 2 hours): ${recentEmbeddings}`);

    // Check for embeddings that might be from files
    const fileEmbeddingsResult = await pool.query(`
      SELECT COUNT(*) as count 
      FROM embeddings 
      WHERE metadata::text ILIKE '%pdf%' 
         OR metadata::text ILIKE '%myrtle%'
         OR metadata::text ILIKE '%handbook%'
         OR metadata::text ILIKE '%benefit%'
         OR metadata::text ILIKE '%scrs%'
         OR metadata::text ILIKE '%pors%'
         OR content ILIKE '%myrtle%beach%'
    `);
    const fileEmbeddings = parseInt(fileEmbeddingsResult.rows[0].count);
    logger.info(`File-related embeddings found: ${fileEmbeddings}`);

    // Get sample file embeddings
    const sampleResult = await pool.query(`
      SELECT id, content, metadata, created_at
      FROM embeddings 
      WHERE metadata::text ILIKE '%pdf%' 
         OR metadata::text ILIKE '%myrtle%'
         OR metadata::text ILIKE '%handbook%'
         OR content ILIKE '%myrtle%beach%'
      ORDER BY created_at DESC 
      LIMIT 3
    `);

    if (sampleResult.rows.length > 0) {
      logger.info("\n=== SAMPLE FILE EMBEDDINGS ===");
      sampleResult.rows.forEach((row, index) => {
        logger.info(`\n--- File Embedding ${index + 1} ---`);
        logger.info(`ID: ${row.id}`);
        logger.info(`Created: ${row.created_at}`);
        logger.info(`Content preview: ${row.content.substring(0, 150)}...`);
        
        // Try to parse metadata safely
        try {
          const metadata = typeof row.metadata === 'string' ? JSON.parse(row.metadata) : row.metadata;
          logger.info(`Source: ${metadata.sourceUrl || metadata.source || 'Unknown'}`);
          if (metadata.title) logger.info(`Title: ${metadata.title}`);
          if (metadata.numPages) logger.info(`Pages: ${metadata.numPages}`);
        } catch (e) {
          logger.info(`Metadata: ${JSON.stringify(row.metadata)}`);
        }
      });
    } else {
      logger.warn("❌ No file-related embeddings found!");
    }

    // Check for very recent embeddings (last 30 minutes)
    const veryRecentResult = await pool.query(`
      SELECT COUNT(*) as count, MAX(created_at) as latest
      FROM embeddings 
      WHERE created_at > NOW() - INTERVAL '30 minutes'
    `);
    
    const veryRecentCount = parseInt(veryRecentResult.rows[0].count);
    const latestEmbedding = veryRecentResult.rows[0].latest;
    
    logger.info(`\n=== RECENT ACTIVITY ===`);
    logger.info(`Very recent embeddings (last 30 min): ${veryRecentCount}`);
    if (latestEmbedding) {
      logger.info(`Latest embedding created: ${latestEmbedding}`);
    }

    // Check total embeddings count
    const totalResult = await pool.query("SELECT COUNT(*) as count FROM embeddings");
    const totalEmbeddings = parseInt(totalResult.rows[0].count);
    logger.info(`Total embeddings in database: ${totalEmbeddings}`);

    // Summary
    logger.info(`\n=== SUMMARY ===`);
    if (fileEmbeddings > 0) {
      logger.info(`✅ SUCCESS: Found ${fileEmbeddings} file-related embeddings`);
      logger.info(`✅ Your PDF files ARE being processed and stored as embeddings`);
    } else {
      logger.warn(`❌ No file embeddings found`);
      logger.warn(`❌ Your PDF files may not be getting processed correctly`);
    }

    if (veryRecentCount > 0) {
      logger.info(`✅ Recent activity detected - ${veryRecentCount} new embeddings in last 30 minutes`);
    } else {
      logger.info(`ℹ️  No very recent embeddings - last run may have been earlier`);
    }

  } catch (error) {
    logger.error("Error checking file embeddings:", error);
  } finally {
    await pool.end();
  }
}

// Run the check
checkFileEmbeddings()
  .then(() => {
    logger.info("\nFile embeddings check completed");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("Check failed:", error);
    process.exit(1);
  });
