import { Op } from "sequelize";
import { initDb } from "@/store/instance";
import { Site } from "@/store/models/site";
import { FileLink } from "@/store/models/file_link";
import { logger } from "@/services/logger";

async function verifyFileProcessing() {
  await initDb();

  logger.info("Verifying file processing setup...");

  // Find all sites
  const sites = await Site.findAll({
    where: { enabled: true },
  });

  logger.info(`Found ${sites.length} enabled sites`);

  for (const site of sites) {
    const siteUrl = site.getDataValue("url");
    const siteId = site.getDataValue("id");
    
    logger.info(`\n--- Checking site: ${siteUrl} (ID: ${siteId}) ---`);

    // Get file link statistics
    const fileStats = await Promise.all([
      FileLink.count({ where: { siteId } }),
      FileLink.count({ where: { siteId, status: "pending" } }),
      FileLink.count({ where: { siteId, status: "processed" } }),
      FileLink.count({ where: { siteId, status: "failed" } }),
      FileLink.count({ where: { siteId, status: "ignored" } }),
    ]);

    const [total, pending, processed, failed, ignored] = fileStats;

    logger.info(`File Links Summary:`);
    logger.info(`  Total: ${total}`);
    logger.info(`  Pending: ${pending}`);
    logger.info(`  Processed: ${processed}`);
    logger.info(`  Failed: ${failed}`);
    logger.info(`  Ignored: ${ignored}`);

    if (pending > 0) {
      logger.info(`✅ ${pending} files will be processed when you run the embeddings script`);
      
      // Show sample pending files
      const pendingFiles = await FileLink.findAll({
        where: { siteId, status: "pending" },
        limit: 5,
      });

      logger.info("Sample pending files:");
      pendingFiles.forEach((file, index) => {
        const url = file.getDataValue("url");
        const fileType = file.getDataValue("fileType");
        logger.info(`  ${index + 1}. ${url} (${fileType})`);
      });

      if (pending > 5) {
        logger.info(`  ... and ${pending - 5} more`);
      }
    } else if (total > 0) {
      logger.info(`ℹ️  All ${total} files have already been processed`);
    } else {
      logger.info(`⚠️  No file links found for this site`);
    }
  }

  // Overall summary
  const overallStats = await Promise.all([
    FileLink.count(),
    FileLink.count({ where: { status: "pending" } }),
    FileLink.count({ where: { status: "processed" } }),
    FileLink.count({ where: { status: "failed" } }),
  ]);

  const [totalFiles, totalPending, totalProcessed, totalFailed] = overallStats;

  logger.info(`\n=== OVERALL SUMMARY ===`);
  logger.info(`Total files across all sites: ${totalFiles}`);
  logger.info(`Pending files that will be processed: ${totalPending}`);
  logger.info(`Already processed files: ${totalProcessed}`);
  logger.info(`Failed files: ${totalFailed}`);

  if (totalPending > 0) {
    logger.info(`\n✅ CONFIRMATION: ${totalPending} files will be scraped when you run the create embeddings script`);
    logger.info(`The files will be processed by the processFileLinksForSite() function`);
  } else {
    logger.info(`\nℹ️  No pending files to process`);
  }

  // Show the exact command that will process the files
  logger.info(`\n📋 To process these files, run:`);
  logger.info(`npm run ts-node src/scripts/embeddings/create_embeddings_multiple.ts`);
  logger.info(`\nOr with specific parameters:`);
  logger.info(`npm run ts-node src/scripts/embeddings/create_embeddings_multiple.ts -- --urlIncludesFilter=kirkland`);
}

// Run the verification
verifyFileProcessing()
  .then(() => {
    logger.info("\nVerification completed");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("Verification failed:", error);
    process.exit(1);
  });
