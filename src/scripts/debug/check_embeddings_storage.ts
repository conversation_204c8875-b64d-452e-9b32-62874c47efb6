import { initDb } from "@/store/instance";
import { logger } from "@/services/logger";
import { config } from "@/config";
import { Pool } from "pg";

async function checkEmbeddingsStorage() {
  await initDb();

  // Create a direct database connection to query the embeddings table
  const pool = new Pool({
    connectionString: config.databaseUrl,
  });

  try {
    logger.info("Checking embeddings storage...");

    // Check total embeddings count
    const totalResult = await pool.query("SELECT COUNT(*) as count FROM embeddings");
    const totalEmbeddings = parseInt(totalResult.rows[0].count);
    logger.info(`Total embeddings in database: ${totalEmbeddings}`);

    // Check recent embeddings (last 24 hours)
    const recentResult = await pool.query(`
      SELECT COUNT(*) as count 
      FROM embeddings 
      WHERE created_at > NOW() - INTERVAL '24 hours'
    `);
    const recentEmbeddings = parseInt(recentResult.rows[0].count);
    logger.info(`Recent embeddings (last 24 hours): ${recentEmbeddings}`);

    // Check embeddings with file-related metadata
    const fileEmbeddingsResult = await pool.query(`
      SELECT COUNT(*) as count 
      FROM embeddings 
      WHERE metadata::text ILIKE '%pdf%' 
         OR metadata::text ILIKE '%file%'
         OR metadata::text ILIKE '%myrtle%'
    `);
    const fileEmbeddings = parseInt(fileEmbeddingsResult.rows[0].count);
    logger.info(`File-related embeddings: ${fileEmbeddings}`);

    // Get sample embeddings to see their structure
    const sampleResult = await pool.query(`
      SELECT id, content, metadata, created_at
      FROM embeddings 
      WHERE metadata::text ILIKE '%pdf%' 
         OR metadata::text ILIKE '%myrtle%'
      ORDER BY created_at DESC 
      LIMIT 5
    `);

    if (sampleResult.rows.length > 0) {
      logger.info("\nSample file embeddings:");
      sampleResult.rows.forEach((row, index) => {
        logger.info(`\n--- Embedding ${index + 1} ---`);
        logger.info(`ID: ${row.id}`);
        logger.info(`Created: ${row.created_at}`);
        logger.info(`Content preview: ${row.content.substring(0, 100)}...`);
        logger.info(`Metadata: ${JSON.stringify(row.metadata, null, 2)}`);
      });
    } else {
      logger.warn("No file-related embeddings found");
    }

    // Check if embeddings have the vector column populated
    const vectorCheckResult = await pool.query(`
      SELECT COUNT(*) as count 
      FROM embeddings 
      WHERE embedding IS NOT NULL
    `);
    const embeddingsWithVectors = parseInt(vectorCheckResult.rows[0].count);
    logger.info(`\nEmbeddings with vector data: ${embeddingsWithVectors}`);

    // Check embeddings by source type
    const sourceTypesResult = await pool.query(`
      SELECT 
        CASE 
          WHEN metadata::text ILIKE '%pdf%' THEN 'PDF'
          WHEN metadata::text ILIKE '%html%' THEN 'HTML'
          WHEN metadata::text ILIKE '%sourceUrl%' THEN 'Web Page'
          ELSE 'Other'
        END as source_type,
        COUNT(*) as count
      FROM embeddings 
      GROUP BY source_type
      ORDER BY count DESC
    `);

    logger.info("\nEmbeddings by source type:");
    sourceTypesResult.rows.forEach(row => {
      logger.info(`  ${row.source_type}: ${row.count}`);
    });

    // Check for embeddings from the specific PDFs we just processed
    const myrtleBeachResult = await pool.query(`
      SELECT COUNT(*) as count, MAX(created_at) as latest
      FROM embeddings 
      WHERE metadata::text ILIKE '%myrtle%beach%'
         OR content ILIKE '%myrtle%beach%'
    `);

    if (myrtleBeachResult.rows[0].count > 0) {
      logger.info(`\n✅ Found ${myrtleBeachResult.rows[0].count} embeddings related to Myrtle Beach`);
      logger.info(`Latest Myrtle Beach embedding: ${myrtleBeachResult.rows[0].latest}`);
    } else {
      logger.warn("\n⚠️  No Myrtle Beach embeddings found");
    }

    // Summary
    logger.info(`\n=== SUMMARY ===`);
    logger.info(`Total embeddings: ${totalEmbeddings}`);
    logger.info(`Recent embeddings: ${recentEmbeddings}`);
    logger.info(`File-related embeddings: ${fileEmbeddings}`);
    logger.info(`Embeddings with vectors: ${embeddingsWithVectors}`);

    if (totalEmbeddings > 0) {
      logger.info(`\n✅ Chunks ARE being stored in the embeddings table`);
      logger.info(`The 'embedding' column contains the vector data (binary format)`);
      logger.info(`The 'content' column contains the actual text chunks`);
      logger.info(`The 'metadata' column contains source information`);
    } else {
      logger.warn(`\n⚠️  No embeddings found - there may be an issue with storage`);
    }

  } catch (error) {
    logger.error("Error checking embeddings storage:", error);
  } finally {
    await pool.end();
  }
}

// Run the check
checkEmbeddingsStorage()
  .then(() => {
    logger.info("\nEmbeddings storage check completed");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("Check failed:", error);
    process.exit(1);
  });
