import { Op } from "sequelize";
import { initDb } from "@/store/instance";
import { Site } from "@/store/models/site";
import { SiteLink } from "@/store/models/site_link";
import { logger } from "@/services/logger";
import { documentLoader } from "@/services/embeddings/document_loaders";
import { loadJavaScriptHeavySite } from "@/services/embeddings/browser-loader";
import { extractDomainAndTld } from "@/utils/url-utils";

async function investigateKirklandCrawling() {
  await initDb();

  // Find the Kirkland site
  const site = await Site.findOne({
    where: {
      url: {
        [Op.iLike]: "%kirkland%",
      },
    },
  });

  if (!site) {
    logger.error("Kirkland site not found");
    return;
  }

  const siteUrl = site.getDataValue("url");
  const siteId = site.getDataValue("id");

  logger.info(`Investigating site: ${siteUrl} (ID: ${siteId})`);

  // Get all site links
  const siteLinks = await SiteLink.findAll({
    where: { siteId },
    order: [["createdAt", "ASC"]],
  });

  logger.info(`Total links found in database: ${siteLinks.length}`);

  // Analyze each link's discovered links
  let totalLinksDiscovered = 0;
  let totalUniqueLinks = new Set<string>();

  for (const link of siteLinks) {
    const url = link.getDataValue("url");
    const status = link.getDataValue("status");
    const linksOnPage = link.getDataValue("linksOnPage") || [];
    const fileLinksOnPage = link.getDataValue("fileLinksOnPage") || [];

    logger.info(`\n--- Analyzing: ${url} ---`);
    logger.info(`Status: ${status}`);
    logger.info(`Links found on page: ${linksOnPage.length}`);
    logger.info(`File links found on page: ${fileLinksOnPage.length}`);

    if (linksOnPage.length > 0) {
      logger.info("Sample links found:");
      linksOnPage.slice(0, 5).forEach((link: string, index: number) => {
        logger.info(`  ${index + 1}. ${link}`);
        totalUniqueLinks.add(link);
      });
      if (linksOnPage.length > 5) {
        logger.info(`  ... and ${linksOnPage.length - 5} more`);
      }
    }

    totalLinksDiscovered += linksOnPage.length;
    linksOnPage.forEach((link: string) => totalUniqueLinks.add(link));
  }

  logger.info(`\n=== SUMMARY ===`);
  logger.info(`Total links discovered across all pages: ${totalLinksDiscovered}`);
  logger.info(`Total unique links discovered: ${totalUniqueLinks.size}`);
  logger.info(`Links actually stored in database: ${siteLinks.length}`);

  // Test fresh crawl of homepage
  logger.info(`\n=== TESTING FRESH CRAWL OF HOMEPAGE ===`);
  try {
    // Test with standard loader
    logger.info("Testing with standard document loader...");
    const standardDoc = await documentLoader.fromHTMLPage(siteUrl);
    logger.info(`Standard loader found ${standardDoc.linksOnPage?.length || 0} links`);

    if (standardDoc.linksOnPage && standardDoc.linksOnPage.length > 0) {
      logger.info("Sample links from standard loader:");
      standardDoc.linksOnPage.slice(0, 10).forEach((link, index) => {
        logger.info(`  ${index + 1}. ${link}`);
      });
    }

    // Test with JavaScript loader
    logger.info("\nTesting with JavaScript-heavy site loader...");
    const jsDoc = await loadJavaScriptHeavySite(siteUrl);
    logger.info(`JavaScript loader found ${jsDoc.linksOnPage?.length || 0} links`);

    if (jsDoc.linksOnPage && jsDoc.linksOnPage.length > 0) {
      logger.info("Sample links from JavaScript loader:");
      jsDoc.linksOnPage.slice(0, 10).forEach((link, index) => {
        logger.info(`  ${index + 1}. ${link}`);
      });
    }

    // Compare the results
    const standardLinks = new Set(standardDoc.linksOnPage || []);
    const jsLinks = new Set(jsDoc.linksOnPage || []);
    const onlyInStandard = [...standardLinks].filter(link => !jsLinks.has(link));
    const onlyInJS = [...jsLinks].filter(link => !standardLinks.has(link));

    logger.info(`\n=== COMPARISON ===`);
    logger.info(`Links only found by standard loader: ${onlyInStandard.length}`);
    logger.info(`Links only found by JS loader: ${onlyInJS.length}`);
    logger.info(`Common links: ${[...standardLinks].filter(link => jsLinks.has(link)).length}`);

  } catch (error) {
    logger.error("Error during fresh crawl test:", error);
  }

  // Analyze domain filtering
  logger.info(`\n=== DOMAIN FILTERING ANALYSIS ===`);
  const siteDomain = extractDomainAndTld(siteUrl);
  logger.info(`Site domain: ${siteDomain}`);

  // Check if any discovered links are being filtered out
  const allDiscoveredLinks = Array.from(totalUniqueLinks);
  const internalLinks = allDiscoveredLinks.filter(link => {
    try {
      if (link.startsWith("/") || !link.includes("://")) {
        return true; // Relative links are internal
      }
      const linkDomain = extractDomainAndTld(link);
      return linkDomain === siteDomain;
    } catch (error) {
      return false;
    }
  });

  const externalLinks = allDiscoveredLinks.filter(link => {
    try {
      if (link.startsWith("/") || !link.includes("://")) {
        return false; // Relative links are internal
      }
      const linkDomain = extractDomainAndTld(link);
      return linkDomain !== siteDomain;
    } catch (error) {
      return true; // If we can't parse it, assume it's external
    }
  });

  logger.info(`Internal links discovered: ${internalLinks.length}`);
  logger.info(`External links discovered: ${externalLinks.length}`);

  if (externalLinks.length > 0) {
    logger.info("Sample external links (these would be filtered out):");
    externalLinks.slice(0, 5).forEach((link, index) => {
      logger.info(`  ${index + 1}. ${link}`);
    });
  }

  // Check for potential issues with link processing
  logger.info(`\n=== LINK PROCESSING ANALYSIS ===`);

  // Find links that should have been added but weren't
  const existingUrls = new Set(siteLinks.map(link => link.getDataValue("url")));
  const missingLinks = internalLinks.filter(link => {
    try {
      // Resolve relative URLs to absolute
      let absoluteUrl = link;
      if (link.startsWith("/")) {
        absoluteUrl = new URL(link, siteUrl).href;
      } else if (!link.includes("://")) {
        absoluteUrl = new URL(link, siteUrl).href;
      }

      // Remove fragments
      if (absoluteUrl.includes("#")) {
        absoluteUrl = absoluteUrl.split("#")[0];
      }

      return !existingUrls.has(absoluteUrl);
    } catch (error) {
      return false;
    }
  });

  logger.info(`Links that should have been added but weren't: ${missingLinks.length}`);
  if (missingLinks.length > 0) {
    logger.info("Sample missing links:");
    missingLinks.slice(0, 10).forEach((link, index) => {
      logger.info(`  ${index + 1}. ${link}`);
    });
  }

  // Check for file URLs that might be incorrectly stored as SiteLinks
  logger.info(`\n=== FILE URL ANALYSIS ===`);
  const fileUrlsInSiteLinks = siteLinks.filter(link => {
    const url = link.getDataValue("url");
    return url.toLowerCase().includes(".pdf") ||
           url.toLowerCase().includes(".doc") ||
           url.toLowerCase().includes(".xls") ||
           url.toLowerCase().includes(".ppt");
  });

  logger.info(`File URLs found in SiteLinks: ${fileUrlsInSiteLinks.length}`);
  if (fileUrlsInSiteLinks.length > 0) {
    logger.info("File URLs that should be in FileLinks instead:");
    fileUrlsInSiteLinks.forEach((link, index) => {
      const url = link.getDataValue("url");
      const status = link.getDataValue("status");
      logger.info(`  ${index + 1}. ${url} (status: ${status})`);
    });
  }
}

// Run the investigation
investigateKirklandCrawling()
  .then(() => {
    logger.info("Investigation completed");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("Investigation failed:", error);
    process.exit(1);
  });
