import { Op } from "sequelize";
import { initDb } from "@/store/instance";
import { SiteLink } from "@/store/models/site_link";
import { FileLink } from "@/store/models/file_link";
import { logger } from "@/services/logger";
import { isFileUrl } from "@/utils/url-utils";
import { getFileType } from "@/utils/file-utils";

async function cleanupFileUrlsInSiteLinks() {
  await initDb();

  logger.info("Starting cleanup of file URLs incorrectly stored as SiteLinks...");

  // Find all SiteLinks that are actually file URLs
  const allSiteLinks = await SiteLink.findAll({
    where: {
      status: {
        [Op.in]: ["pending", "scraped", "failed"],
      },
    },
  });

  logger.info(`Found ${allSiteLinks.length} total site links to check`);

  let fileUrlsFound = 0;
  let fileUrlsMoved = 0;
  let fileUrlsIgnored = 0;

  for (const siteLink of allSiteLinks) {
    const url = siteLink.getDataValue("url");
    const siteId = siteLink.getDataValue("siteId");
    const siteLinkId = siteLink.getDataValue("id");

    if (isFileUrl(url)) {
      fileUrlsFound++;
      logger.info(`Found file URL in SiteLinks: ${url}`);

      const fileType = getFileType(url);

      try {
        // Check if this file URL already exists in FileLinks
        const existingFileLink = await FileLink.findOne({
          where: {
            url,
            siteId,
          },
        });

        if (existingFileLink) {
          logger.info(`File URL already exists in FileLinks, marking SiteLink as ignored: ${url}`);
          await siteLink.update({
            status: "ignored",
            comment: "File URL moved to FileLinks table",
          });
          fileUrlsIgnored++;
        } else {
          // Create new FileLink
          await FileLink.create({
            url,
            fileType,
            siteId,
            siteLinkId,
            status: "pending",
          });

          // Mark the SiteLink as ignored
          await siteLink.update({
            status: "ignored",
            comment: "File URL moved to FileLinks table",
          });

          logger.info(`Moved file URL from SiteLinks to FileLinks: ${url} (type: ${fileType})`);
          fileUrlsMoved++;
        }
      } catch (error) {
        logger.error(`Error processing file URL ${url}:`, error);
      }
    }
  }

  logger.info(`\n=== CLEANUP SUMMARY ===`);
  logger.info(`Total site links checked: ${allSiteLinks.length}`);
  logger.info(`File URLs found in SiteLinks: ${fileUrlsFound}`);
  logger.info(`File URLs moved to FileLinks: ${fileUrlsMoved}`);
  logger.info(`File URLs already in FileLinks (ignored): ${fileUrlsIgnored}`);

  // Now let's check for any remaining file URLs that might need attention
  const remainingFileUrls = await SiteLink.findAll({
    where: {
      status: {
        [Op.notIn]: ["ignored"],
      },
    },
  });

  const stillFileUrls = remainingFileUrls.filter(link => isFileUrl(link.getDataValue("url")));
  
  if (stillFileUrls.length > 0) {
    logger.warn(`WARNING: ${stillFileUrls.length} file URLs still remain as active SiteLinks:`);
    stillFileUrls.forEach(link => {
      logger.warn(`  - ${link.getDataValue("url")} (status: ${link.getDataValue("status")})`);
    });
  } else {
    logger.info("✅ No file URLs remain as active SiteLinks");
  }
}

// Run the cleanup
cleanupFileUrlsInSiteLinks()
  .then(() => {
    logger.info("Cleanup completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("Cleanup failed:", error);
    process.exit(1);
  });
