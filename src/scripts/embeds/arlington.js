export function generateArlingtonEmbed(clientId) {
  // Only generate embed code for Arlington (client ID 211)
  if (clientId !== "211") return null;

  return `(function() {
  const iframe = document.createElement('iframe');
  iframe.id = 'repd-embed';

  // Get the current page URL
  const currentPath = window.location.pathname;
  const referral = window.location.href;

  // Construct embed URL using current path
  const embedUrl = new URL(\`https://embed.repd.us\${currentPath}\`);
  embedUrl.searchParams.append('referrer', encodeURIComponent(referral));
  
  iframe.src = embedUrl.toString();
  iframe.style.cssText = \`
    position: fixed;
    bottom: 0rem;
    right: 0rem;
    border: none;
    z-index: 50;
    transition: all 0.3s ease;
    background: transparent;
    width: 10rem;
    height: 10rem;
  \`;

  // Handle messages from iframe for resize events
  window.addEventListener('message', (event) => {
    if (event.origin !== 'https://app.repd.us') return;
    
    const { type, dimensions } = event.data;
    if (type === 'resize') {
      iframe.style.width = dimensions.width;
      iframe.style.height = dimensions.height;
    }
  });

  document.body.appendChild(iframe);
})();`;
}

export default generateArlingtonEmbed;
