"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn("file_links", "s3_key", {
      type: Sequelize.STRING,
      allowNull: true,
      schema: "public",
    });

    await queryInterface.addColumn("file_links", "s3_url", {
      type: Sequelize.STRING,
      allowNull: true,
      schema: "public",
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn("file_links", "s3_key", { schema: "public" });
    await queryInterface.removeColumn("file_links", "s3_url", { schema: "public" });
  },
};
