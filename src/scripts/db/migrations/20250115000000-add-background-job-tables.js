"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create job_queue table
    await queryInterface.createTable("job_queue", {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      job_type: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      job_data: {
        type: Sequelize.JSONB,
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM("pending", "running", "completed", "failed", "cancelled"),
        defaultValue: "pending",
      },
      priority: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      scheduled_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      started_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      completed_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      worker_id: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      last_heartbeat: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      attempts: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      max_attempts: {
        type: Sequelize.INTEGER,
        defaultValue: 3,
      },
      last_error: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      retry_delay: {
        type: Sequelize.INTEGER,
        defaultValue: 300, // 5 minutes
      },
      next_retry_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      metadata: {
        type: Sequelize.JSONB,
        defaultValue: {},
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
    }, {
      schema: "public",
    });

    // Create scrape_jobs table
    await queryInterface.createTable("scrape_jobs", {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      site_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      site_url: {
        type: Sequelize.STRING(2000),
        allowNull: false,
      },
      job_type: {
        type: Sequelize.ENUM("sitemap", "embeddings", "full_scrape"),
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM("pending", "running", "completed", "failed", "paused"),
        defaultValue: "pending",
      },
      priority: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      current_batch: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      total_batches: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      batch_size: {
        type: Sequelize.INTEGER,
        defaultValue: 100,
      },
      processed_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      total_count: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      last_processed_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      config: {
        type: Sequelize.JSONB,
        defaultValue: {},
      },
      started_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      completed_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      last_heartbeat: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      error_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      last_error: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      retry_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      max_retries: {
        type: Sequelize.INTEGER,
        defaultValue: 3,
      },
      next_retry_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      metadata: {
        type: Sequelize.JSONB,
        defaultValue: {},
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
    }, {
      schema: "public",
    });

    // Create indexes for job_queue
    await queryInterface.addIndex("job_queue", ["job_type"], {
      name: "idx_job_queue_job_type",
    });
    await queryInterface.addIndex("job_queue", ["status"], {
      name: "idx_job_queue_status",
    });
    await queryInterface.addIndex("job_queue", ["priority", "created_at"], {
      name: "idx_job_queue_priority_created",
    });
    await queryInterface.addIndex("job_queue", ["scheduled_at"], {
      name: "idx_job_queue_scheduled_at",
    });
    await queryInterface.addIndex("job_queue", ["next_retry_at"], {
      name: "idx_job_queue_next_retry_at",
    });
    await queryInterface.addIndex("job_queue", ["worker_id"], {
      name: "idx_job_queue_worker_id",
    });

    // Create indexes for scrape_jobs
    await queryInterface.addIndex("scrape_jobs", ["site_id"], {
      name: "idx_scrape_jobs_site_id",
    });
    await queryInterface.addIndex("scrape_jobs", ["status"], {
      name: "idx_scrape_jobs_status",
    });
    await queryInterface.addIndex("scrape_jobs", ["job_type"], {
      name: "idx_scrape_jobs_job_type",
    });
    await queryInterface.addIndex("scrape_jobs", ["priority", "created_at"], {
      name: "idx_scrape_jobs_priority_created",
    });
    await queryInterface.addIndex("scrape_jobs", ["next_retry_at"], {
      name: "idx_scrape_jobs_next_retry_at",
    });

    // Add foreign key constraint for scrape_jobs.site_id
    await queryInterface.addConstraint("scrape_jobs", {
      fields: ["site_id"],
      type: "foreign key",
      name: "fk_scrape_jobs_site_id",
      references: {
        table: "sites",
        field: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Remove foreign key constraint
    await queryInterface.removeConstraint("scrape_jobs", "fk_scrape_jobs_site_id");

    // Drop indexes for scrape_jobs
    await queryInterface.removeIndex("scrape_jobs", "idx_scrape_jobs_site_id");
    await queryInterface.removeIndex("scrape_jobs", "idx_scrape_jobs_status");
    await queryInterface.removeIndex("scrape_jobs", "idx_scrape_jobs_job_type");
    await queryInterface.removeIndex("scrape_jobs", "idx_scrape_jobs_priority_created");
    await queryInterface.removeIndex("scrape_jobs", "idx_scrape_jobs_next_retry_at");

    // Drop indexes for job_queue
    await queryInterface.removeIndex("job_queue", "idx_job_queue_job_type");
    await queryInterface.removeIndex("job_queue", "idx_job_queue_status");
    await queryInterface.removeIndex("job_queue", "idx_job_queue_priority_created");
    await queryInterface.removeIndex("job_queue", "idx_job_queue_scheduled_at");
    await queryInterface.removeIndex("job_queue", "idx_job_queue_next_retry_at");
    await queryInterface.removeIndex("job_queue", "idx_job_queue_worker_id");

    // Drop tables
    await queryInterface.dropTable("scrape_jobs");
    await queryInterface.dropTable("job_queue");
  },
};
