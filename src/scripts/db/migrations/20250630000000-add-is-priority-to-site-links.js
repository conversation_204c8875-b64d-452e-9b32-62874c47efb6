"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn("site_links", "is_priority", {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      schema: "public",
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn("site_links", "is_priority", { schema: "public" });
  },
};
