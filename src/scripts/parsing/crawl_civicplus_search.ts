import fetch from "node-fetch";

import { logger } from "@/services/logger";
import { initDb } from "@/store/instance";
import { Site } from "@/store/models/site";
import { SiteLink } from "@/store/models/site_link";
import { trackProgress } from "@/services/logger";
import { extractDomainAndTld } from "@/utils/helpers";

// Common search terms that will likely return many results
const SEARCH_TERMS = [
  "city",
  "council",
  "meeting",
  "minutes",
  "agenda",
  "ordinance",
  "resolution",
  "budget",
  "plan",
  "policy",
  "department",
  "service",
  "permit",
  "application",
  "form",
  "report",
  "community",
  "development",
  "project",
  "event",
  "park",
  "recreation",
  "public",
  "works",
  "police",
  "fire",
  "emergency",
  "utility",
  "water",
  "trash",
  "recycling",
  "street",
  "sidewalk",
  "tax",
  "fee",
  "payment",
  "contact",
  "staff",
  "directory",
  "code",
  "regulation",
  "zoning",
  "building",
  "business",
  "license",
  "document",
  "publication",
  "newsletter",
  "announcement",
  "notice",
];

async function crawlCivicPlusSearch(siteUrl: string, maxResults = 1000) {
  try {
    await initDb();

    logger.info(`Starting CivicPlus Search API crawl for ${siteUrl}, targeting up to ${maxResults} results`);

    // Find or create the site
    const [site] = await Site.findOrCreate({
      where: { url: siteUrl },
      defaults: {
        url: siteUrl,
        enabled: true,
      },
    });

    const siteId = site.getDataValue("id");

    // Extract domain from site URL
    const siteDomain = extractDomainAndTld(siteUrl);
    logger.info(`Site domain: ${siteDomain}`);

    // Set to track processed URLs to avoid duplicates
    const processedUrls = new Set<string>();
    const pendingUrls = new Set<string>();

    let totalResultsAdded = 0;
    let totalSearchesProcessed = 0;

    // Process each search term
    for (const term of SEARCH_TERMS) {
      if (totalResultsAdded >= maxResults) {
        logger.info(`Reached maximum results limit of ${maxResults}`);
        break;
      }

      totalSearchesProcessed++;
      const stopTracking = trackProgress(
        `Searching for term: "${term}" (${totalSearchesProcessed}/${SEARCH_TERMS.length})`,
      );

      try {
        // CivicPlus sites typically use this search API endpoint
        const searchApiUrl = `${siteUrl}/api/Search/Search?searchText=${encodeURIComponent(term)}&pageSize=100&page=1&searchType=Website`;

        logger.info(`Using CivicPlus search API: ${searchApiUrl}`);

        // Fetch the search results
        const response = await fetch(searchApiUrl, {
          headers: {
            Accept: "application/json",
            "User-Agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
          },
        });

        if (!response.ok) {
          logger.error(`Search API returned error: ${response.status} ${response.statusText}`);
          continue;
        }

        const searchResults = await response.json();

        // Process search results
        let newLinksAdded = 0;

        // Handle different response formats
        const results = searchResults.Results || searchResults.results || [];

        if (Array.isArray(results)) {
          for (const result of results) {
            try {
              // Extract URL from result
              const resultUrl = result.Url || result.url || result.Link || result.link;

              if (!resultUrl) {
                continue;
              }

              // Normalize URL
              let normalizedUrl = resultUrl;
              if (resultUrl.startsWith("/")) {
                normalizedUrl = `${siteUrl}${resultUrl}`;
              } else if (!resultUrl.startsWith("http")) {
                normalizedUrl = `${siteUrl}/${resultUrl}`;
              }

              // Skip if already processed or pending
              if (processedUrls.has(normalizedUrl) || pendingUrls.has(normalizedUrl)) {
                continue;
              }

              // Make sure it's from the same domain
              try {
                const linkDomain = extractDomainAndTld(normalizedUrl);
                if (linkDomain !== siteDomain) {
                  continue;
                }
              } catch (error) {
                continue;
              }

              // Add to pending URLs
              pendingUrls.add(normalizedUrl);
              processedUrls.add(normalizedUrl); // Mark as processed immediately to avoid duplicates
              newLinksAdded++;

              // Also add to database as pending
              await SiteLink.findOrCreate({
                where: {
                  url: normalizedUrl,
                  siteId,
                },
                defaults: {
                  url: normalizedUrl,
                  siteId,
                  status: "pending",
                },
              });

              totalResultsAdded++;

              // Check if we've reached the limit
              if (totalResultsAdded >= maxResults) {
                logger.info(`Reached maximum results limit of ${maxResults}`);
                break;
              }
            } catch (error) {
              logger.error(`Error processing search result:`, error);
            }
          }
        }

        logger.info(`Found ${newLinksAdded} new links from search term "${term}"`);

        // Add a delay to avoid being rate-limited
        await new Promise((resolve) => setTimeout(resolve, 1000));

        stopTracking(`Completed search for term "${term}", total results so far: ${totalResultsAdded}`);
      } catch (error) {
        logger.error(`Error searching for term "${term}":`, error);
        stopTracking(`Error: ${error}`);

        // Add a longer delay if there was an error (might be rate limiting)
        await new Promise((resolve) => setTimeout(resolve, 5000));
      }
    }

    // Get final counts
    const totalLinks = await SiteLink.count({
      where: {
        siteId,
      },
    });

    const pendingLinks = await SiteLink.count({
      where: {
        siteId,
        status: "pending",
      },
    });

    logger.info(`CivicPlus Search API crawl completed for ${siteUrl}`);
    logger.info(`Total pages in database: ${totalLinks}`);
    logger.info(`Pending pages: ${pendingLinks}`);
    logger.info(`Search terms processed: ${totalSearchesProcessed}`);
    logger.info(`New results added: ${totalResultsAdded}`);

    return {
      success: true,
      totalLinks,
      pendingLinks,
      searchesProcessed: totalSearchesProcessed,
      resultsAdded: totalResultsAdded,
    };
  } catch (error) {
    logger.error(`Error crawling CivicPlus Search API for ${siteUrl}:`, error);
    return { success: false, error };
  }
}

// Get command line arguments
const args = process.argv.slice(2);
if (args.length < 1) {
  logger.error("Usage: npm run crawl-civicplus-search <site-url> [max-results]");
  process.exit(1);
}

const siteUrl = args[0];
const maxResults = args[1] ? parseInt(args[1], 10) : 1000;

void crawlCivicPlusSearch(siteUrl, maxResults)
  .then((result) => {
    if (result.success) {
      logger.info(`Successfully crawled CivicPlus Search API for ${siteUrl}`);
      process.exit(0);
    } else {
      logger.error(`Failed to crawl CivicPlus Search API for ${siteUrl}`);
      process.exit(1);
    }
  })
  .catch((error) => {
    logger.error(`Error in crawl_civicplus_search script:`, error);
    process.exit(1);
  });
