import * as cheerio from "cheerio";

import { documentLoader } from "@/services/embeddings/document_loaders";
import { logger } from "@/services/logger";
import { initDb } from "@/store/instance";
import { Site } from "@/store/models/site";
import { SiteLink } from "@/store/models/site_link";
import { FileLink } from "@/store/models/file_link";
import { trackProgress } from "@/services/logger";
import { fetchWithUserHeaders } from "@/utils/helpers";

async function crawlDocumentCenter(siteUrl: string, maxPages = 100) {
  try {
    await initDb();

    logger.info(`Starting DocumentCenter crawl for ${siteUrl}, targeting up to ${maxPages} pages`);

    // Find or create the site
    const [site] = await Site.findOrCreate({
      where: { url: siteUrl },
      defaults: {
        url: siteUrl,
        enabled: true,
      },
    });

    const siteId = site.getDataValue("id");

    // Common DocumentCenter paths
    const documentCenterPaths = [
      "/DocumentCenter",
      "/documents",
      "/documents/index",
      "/documents/browse",
      "/DocumentCenter/Index",
      "/DocumentCenter/Browse",
    ];

    let totalPagesProcessed = 0;
    let totalDocumentsFound = 0;
    let documentCenterFound = false;

    // Try each potential DocumentCenter path
    for (const path of documentCenterPaths) {
      if (totalPagesProcessed >= maxPages) {
        logger.info(`Reached maximum pages limit of ${maxPages}`);
        break;
      }

      const documentCenterUrl = `${siteUrl}${path}`;
      logger.info(`Checking DocumentCenter at: ${documentCenterUrl}`);

      try {
        // Fetch the DocumentCenter page
        const response = await fetchWithUserHeaders(documentCenterUrl);

        if (!response.ok) {
          logger.info(`DocumentCenter not found at ${documentCenterUrl}: ${response.status} ${response.statusText}`);
          continue;
        }

        const html = await response.text();
        const $ = cheerio.load(html);

        // Look for document categories/folders
        const categories = [];

        // Different sites have different structures, so we need to try multiple selectors
        const potentialCategorySelectors = [
          '.DocumentCenter a[href*="/DocumentCenter/Index/"]',
          '.DocumentCenter a[href*="/DocumentCenter/Browse/"]',
          '.document-center a[href*="/DocumentCenter/Index/"]',
          '.document-center a[href*="/DocumentCenter/Browse/"]',
          'a[href*="/DocumentCenter/Index/"]',
          'a[href*="/DocumentCenter/Browse/"]',
          'a[href*="/documents/index/"]',
          'a[href*="/documents/browse/"]',
        ];

        for (const selector of potentialCategorySelectors) {
          $(selector).each((_, element) => {
            const href = $(element).attr("href");
            if (href) {
              // Convert relative URLs to absolute
              const categoryUrl = href.startsWith("http")
                ? href
                : `${siteUrl}${href.startsWith("/") ? "" : "/"}${href}`;
              categories.push(categoryUrl);
            }
          });

          if (categories.length > 0) {
            break; // Found categories with this selector, no need to try others
          }
        }

        if (categories.length > 0) {
          documentCenterFound = true;
          logger.info(`Found DocumentCenter at ${documentCenterUrl} with ${categories.length} categories`);

          // Process each category
          for (const categoryUrl of categories) {
            if (totalPagesProcessed >= maxPages) {
              logger.info(`Reached maximum pages limit of ${maxPages}`);
              break;
            }

            try {
              logger.info(`Processing category: ${categoryUrl}`);
              totalPagesProcessed++;

              // Fetch the category page
              const categoryResponse = await fetchWithUserHeaders(categoryUrl);

              if (!categoryResponse.ok) {
                logger.error(`Failed to fetch category: ${categoryResponse.status} ${categoryResponse.statusText}`);
                continue;
              }

              const categoryHtml = await categoryResponse.text();
              const $category = cheerio.load(categoryHtml);

              // Look for document links
              const documentLinks = [];

              // Different sites have different structures, so we need to try multiple selectors
              const potentialDocumentSelectors = [
                'a[href*="/DocumentCenter/View/"]',
                'a[href*="/Archive.aspx?ADID="]',
                ".document a[href]",
                ".document-link a[href]",
                ".document-item a[href]",
              ];

              for (const selector of potentialDocumentSelectors) {
                $category(selector).each((_, element) => {
                  const href = $category(element).attr("href");
                  if (href) {
                    // Convert relative URLs to absolute
                    const documentUrl = href.startsWith("http")
                      ? href
                      : `${siteUrl}${href.startsWith("/") ? "" : "/"}${href}`;

                    // Only add DocumentCenter View links
                    if (documentUrl.includes("/DocumentCenter/View/") || documentUrl.includes("/Archive.aspx?ADID=")) {
                      documentLinks.push(documentUrl);
                    }
                  }
                });

                if (documentLinks.length > 0) {
                  break; // Found documents with this selector, no need to try others
                }
              }

              logger.info(`Found ${documentLinks.length} documents in category ${categoryUrl}`);

              // Process each document link
              for (const documentUrl of documentLinks) {
                try {
                  // Find the parent SiteLink for this document
                  const [parentLink] = await SiteLink.findOrCreate({
                    where: {
                      url: categoryUrl,
                      siteId,
                    },
                    defaults: {
                      url: categoryUrl,
                      siteId,
                      status: "pending",
                    },
                  });

                  // Add document to FileLinks
                  const [fileLink, created] = await FileLink.findOrCreate({
                    where: {
                      url: documentUrl,
                      siteId,
                    },
                    defaults: {
                      url: documentUrl,
                      siteId,
                      siteLinkId: parentLink.getDataValue("id"),
                      status: "pending",
                      fileType: "pdf", // Default to PDF, will be updated during processing
                    },
                  });

                  if (created) {
                    totalDocumentsFound++;
                    logger.info(`Added document: ${documentUrl}`);
                  }
                } catch (error) {
                  logger.error(`Error adding document ${documentUrl}:`, error);
                }
              }
            } catch (error) {
              logger.error(`Error processing category ${categoryUrl}:`, error);
            }
          }
        } else {
          // If no categories found, look for direct document links
          const documentLinks = [];

          $('a[href*="/DocumentCenter/View/"]').each((_, element) => {
            const href = $(element).attr("href");
            if (href) {
              // Convert relative URLs to absolute
              const documentUrl = href.startsWith("http")
                ? href
                : `${siteUrl}${href.startsWith("/") ? "" : "/"}${href}`;
              documentLinks.push(documentUrl);
            }
          });

          $('a[href*="/Archive.aspx?ADID="]').each((_, element) => {
            const href = $(element).attr("href");
            if (href) {
              // Convert relative URLs to absolute
              const documentUrl = href.startsWith("http")
                ? href
                : `${siteUrl}${href.startsWith("/") ? "" : "/"}${href}`;
              documentLinks.push(documentUrl);
            }
          });

          if (documentLinks.length > 0) {
            documentCenterFound = true;
            logger.info(`Found ${documentLinks.length} direct document links at ${documentCenterUrl}`);

            // Process each document link
            for (const documentUrl of documentLinks) {
              try {
                // Find the parent SiteLink for this document
                const [parentLink] = await SiteLink.findOrCreate({
                  where: {
                    url: documentCenterUrl,
                    siteId,
                  },
                  defaults: {
                    url: documentCenterUrl,
                    siteId,
                    status: "pending",
                  },
                });

                // Add document to FileLinks
                const [fileLink, created] = await FileLink.findOrCreate({
                  where: {
                    url: documentUrl,
                    siteId,
                  },
                  defaults: {
                    url: documentUrl,
                    siteId,
                    siteLinkId: parentLink.getDataValue("id"),
                    status: "pending",
                    fileType: "pdf", // Default to PDF, will be updated during processing
                  },
                });

                if (created) {
                  totalDocumentsFound++;
                  logger.info(`Added document: ${documentUrl}`);
                }
              } catch (error) {
                logger.error(`Error adding document ${documentUrl}:`, error);
              }
            }
          }
        }
      } catch (error) {
        logger.error(`Error checking DocumentCenter at ${documentCenterUrl}:`, error);
      }
    }
  } catch (error) {
    logger.error(`Error crawling DocumentCenter for ${siteUrl}:`, error);
  }
}
