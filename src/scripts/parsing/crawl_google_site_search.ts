import { documentLoader } from "@/services/embeddings/document_loaders";
import { logger } from "@/services/logger";
import { initDb } from "@/store/instance";
import { Site } from "@/store/models/site";
import { SiteLink } from "@/store/models/site_link";
import { trackProgress } from "@/services/logger";
import { extractDomainAndTld } from "@/utils/helpers";

// Common search terms that will likely return many results
const SEARCH_TERMS = [
  "city",
  "council",
  "meeting",
  "minutes",
  "agenda",
  "ordinance",
  "resolution",
  "budget",
  "plan",
  "policy",
  "department",
  "service",
  "permit",
  "application",
  "form",
  "report",
  "community",
  "development",
  "project",
  "event",
  "park",
  "recreation",
  "public",
  "works",
  "police",
  "fire",
  "emergency",
  "utility",
  "water",
  "trash",
  "recycling",
  "street",
  "sidewalk",
  "tax",
  "fee",
  "payment",
  "contact",
  "staff",
  "directory",
  "code",
  "regulation",
  "zoning",
  "building",
  "business",
  "license",
  "document",
  "publication",
  "newsletter",
  "announcement",
  "notice",
];

async function crawlGoogleSiteSearch(siteUrl: string, maxResults = 1000) {
  try {
    await initDb();

    logger.info(`Starting Google Site Search crawl for ${siteUrl}, targeting up to ${maxResults} results`);

    // Find or create the site
    const [site] = await Site.findOrCreate({
      where: { url: siteUrl },
      defaults: {
        url: siteUrl,
        enabled: true,
      },
    });

    const siteId = site.getDataValue("id");

    // Extract domain from site URL
    const siteDomain = extractDomainAndTld(siteUrl);
    logger.info(`Site domain: ${siteDomain}`);

    // Set to track processed URLs to avoid duplicates
    const processedUrls = new Set<string>();
    const pendingUrls = new Set<string>();

    let totalResultsAdded = 0;
    let totalSearchesProcessed = 0;

    // Process each search term
    for (const term of SEARCH_TERMS) {
      if (totalResultsAdded >= maxResults) {
        logger.info(`Reached maximum results limit of ${maxResults}`);
        break;
      }

      totalSearchesProcessed++;
      const stopTracking = trackProgress(
        `Searching for term: "${term}" (${totalSearchesProcessed}/${SEARCH_TERMS.length})`,
      );

      try {
        // Construct Google Site Search URL
        // Format: https://www.google.com/search?q=site:domain.com+search+term
        const googleSearchUrl = `https://www.google.com/search?q=site:${siteDomain}+${encodeURIComponent(term)}&num=100`;

        logger.info(`Using Google search URL: ${googleSearchUrl}`);

        // Fetch the search results page
        const document = await documentLoader.fromHTMLPage(googleSearchUrl);

        // Extract links from the search results
        const linksOnPage = document.linksOnPage || [];

        // Add search results to pending queue
        let newLinksAdded = 0;
        for (const resultUrl of linksOnPage) {
          try {
            // Skip invalid URLs, anchors, javascript, mailto, tel links
            if (
              !resultUrl ||
              resultUrl.startsWith("#") ||
              resultUrl.startsWith("javascript:") ||
              resultUrl.startsWith("mailto:") ||
              resultUrl.startsWith("tel:") ||
              resultUrl.includes("google.com") ||
              resultUrl.includes("?calendar") ||
              resultUrl.includes("?print=yes")
            ) {
              continue;
            }

            // Make sure it's from the same domain
            try {
              const linkDomain = extractDomainAndTld(resultUrl);
              if (linkDomain !== siteDomain) {
                continue;
              }
            } catch (error) {
              continue;
            }

            // Skip if already processed or pending
            if (processedUrls.has(resultUrl) || pendingUrls.has(resultUrl)) {
              continue;
            }

            // Add to pending URLs
            pendingUrls.add(resultUrl);
            processedUrls.add(resultUrl); // Mark as processed immediately to avoid duplicates
            newLinksAdded++;

            // Also add to database as pending
            await SiteLink.findOrCreate({
              where: {
                url: resultUrl,
                siteId,
              },
              defaults: {
                url: resultUrl,
                siteId,
                status: "pending",
              },
            });

            totalResultsAdded++;

            // Check if we've reached the limit
            if (totalResultsAdded >= maxResults) {
              logger.info(`Reached maximum results limit of ${maxResults}`);
              break;
            }
          } catch (error) {
            logger.error(`Error processing search result link ${resultUrl}:`, error);
          }
        }

        logger.info(`Found ${newLinksAdded} new links from search term "${term}"`);

        // Add a delay to avoid being blocked by Google
        await new Promise((resolve) => setTimeout(resolve, 3000));

        stopTracking(`Completed search for term "${term}", total results so far: ${totalResultsAdded}`);
      } catch (error) {
        logger.error(`Error searching for term "${term}":`, error);
        stopTracking(`Error: ${error}`);

        // Add a longer delay if there was an error (might be rate limiting)
        await new Promise((resolve) => setTimeout(resolve, 10000));
      }
    }

    // Get final counts
    const totalLinks = await SiteLink.count({
      where: {
        siteId,
      },
    });

    const pendingLinks = await SiteLink.count({
      where: {
        siteId,
        status: "pending",
      },
    });

    logger.info(`Google Site Search crawl completed for ${siteUrl}`);
    logger.info(`Total pages in database: ${totalLinks}`);
    logger.info(`Pending pages: ${pendingLinks}`);
    logger.info(`Search terms processed: ${totalSearchesProcessed}`);
    logger.info(`New results added: ${totalResultsAdded}`);

    return {
      success: true,
      totalLinks,
      pendingLinks,
      searchesProcessed: totalSearchesProcessed,
      resultsAdded: totalResultsAdded,
    };
  } catch (error) {
    logger.error(`Error crawling Google Site Search for ${siteUrl}:`, error);
    return { success: false, error };
  }
}

// Get command line arguments
const args = process.argv.slice(2);
if (args.length < 1) {
  logger.error("Usage: npm run crawl-google-site-search <site-url> [max-results]");
  process.exit(1);
}

const siteUrl = args[0];
const maxResults = args[1] ? parseInt(args[1], 10) : 1000;

void crawlGoogleSiteSearch(siteUrl, maxResults)
  .then((result) => {
    if (result.success) {
      logger.info(`Successfully crawled Google Site Search for ${siteUrl}`);
      process.exit(0);
    } else {
      logger.error(`Failed to crawl Google Site Search for ${siteUrl}`);
      process.exit(1);
    }
  })
  .catch((error) => {
    logger.error(`Error in crawl_google_site_search script:`, error);
    process.exit(1);
  });
