import fetch from "node-fetch";

import { logger } from "@/services/logger";
import { initDb } from "@/store/instance";
import { Site } from "@/store/models/site";
import { SiteLink } from "@/store/models/site_link";
import { trackProgress } from "@/services/logger";
import { extractDomainAndTld } from "@/utils/helpers";

// Common search terms that will likely return many results
const SEARCH_TERMS = [
  "city",
  "council",
  "meeting",
  "minutes",
  "agenda",
  "ordinance",
  // ... other terms remain the same
];

// Different CivicPlus search API endpoints to try
const CIVICPLUS_SEARCH_ENDPOINTS = [
  "/api/Search/Search?searchText={term}&pageSize=100&page=1&searchType=Website",
  "/search/autocomplete?term={term}",
  "/search?searchText={term}",
  "/search/search-results?searchText={term}",
  "/search-results?term={term}",
];

async function crawlCivicPlusSearch(siteUrl: string, maxResults = 1000) {
  try {
    await initDb();

    logger.info(`Starting CivicPlus Search API crawl for ${siteUrl}, targeting up to ${maxResults} results`);

    // Find or create the site
    const [site] = await Site.findOrCreate({
      where: { url: siteUrl },
      defaults: {
        url: siteUrl,
        enabled: true,
      },
    });

    const siteId = site.getDataValue("id");

    // Extract domain from site URL
    const siteDomain = extractDomainAndTld(siteUrl);
    logger.info(`Site domain: ${siteDomain}`);

    // Set to track processed URLs to avoid duplicates
    const processedUrls = new Set<string>();
    const pendingUrls = new Set<string>();

    let totalResultsAdded = 0;
    let totalSearchesProcessed = 0;

    // First, detect which search API endpoint works
    let workingEndpoint = null;

    logger.info("Detecting working CivicPlus search API endpoint...");
    for (const endpoint of CIVICPLUS_SEARCH_ENDPOINTS) {
      const testTerm = "city";
      const searchApiUrl = `${siteUrl}${endpoint.replace("{term}", encodeURIComponent(testTerm))}`;

      logger.info(`Testing endpoint: ${searchApiUrl}`);

      try {
        const response = await fetch(searchApiUrl, {
          headers: {
            Accept: "application/json",
            "User-Agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
          },
        });

        if (response.ok) {
          workingEndpoint = endpoint;
          logger.info(`Found working search API endpoint: ${endpoint}`);
          break;
        } else {
          logger.info(`Endpoint returned ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        logger.info(`Error testing endpoint: ${error.message}`);
      }

      // Add a small delay between tests
      await new Promise((resolve) => setTimeout(resolve, 500));
    }

    if (!workingEndpoint) {
      logger.warn("No working CivicPlus search API endpoint found. Falling back to Google site search.");
      // You could import and call the Google site search function here
      return { success: false, error: "No working CivicPlus search API endpoint found" };
    }

    // Process each search term using the working endpoint
    for (const term of SEARCH_TERMS) {
      if (totalResultsAdded >= maxResults) {
        logger.info(`Reached maximum results limit of ${maxResults}`);
        break;
      }

      totalSearchesProcessed++;
      const stopTracking = trackProgress(
        `Searching for term: "${term}" (${totalSearchesProcessed}/${SEARCH_TERMS.length})`,
      );

      try {
        const searchApiUrl = `${siteUrl}${workingEndpoint.replace("{term}", encodeURIComponent(term))}`;
        logger.info(`Using CivicPlus search API: ${searchApiUrl}`);

        // Fetch the search results
        const response = await fetch(searchApiUrl, {
          headers: {
            Accept: "application/json",
            "User-Agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
          },
        });

        if (!response.ok) {
          logger.error(`Search API returned error: ${response.status} ${response.statusText}`);
          continue;
        }

        const searchResults = await response.json();
        let newLinksAdded = 0;

        // Handle different response formats
        let results = [];

        if (searchResults.Results) {
          results = searchResults.Results;
        } else if (searchResults.results) {
          results = searchResults.results;
        } else if (Array.isArray(searchResults)) {
          results = searchResults;
        } else if (searchResults.suggestions) {
          results = searchResults.suggestions;
        }

        if (Array.isArray(results)) {
          for (const result of results) {
            try {
              // Extract URL from result based on different possible formats
              let resultUrl = null;

              if (result.Url) {
                resultUrl = result.Url;
              } else if (result.url) {
                resultUrl = result.url;
              } else if (result.Link) {
                resultUrl = result.Link;
              } else if (result.link) {
                resultUrl = result.link;
              } else if (result.path) {
                resultUrl = result.path;
              } else if (result.href) {
                resultUrl = result.href;
              } else if (typeof result === "string" && (result.startsWith("http") || result.startsWith("/"))) {
                resultUrl = result;
              }

              if (!resultUrl) {
                continue;
              }

              // Process URL as in the original script
              // ... rest of the URL processing logic

              // Normalize URL
              let normalizedUrl = resultUrl;
              if (resultUrl.startsWith("/")) {
                normalizedUrl = `${siteUrl}${resultUrl}`;
              } else if (!resultUrl.startsWith("http")) {
                normalizedUrl = `${siteUrl}/${resultUrl}`;
              }

              // Skip if already processed or pending
              if (processedUrls.has(normalizedUrl) || pendingUrls.has(normalizedUrl)) {
                continue;
              }

              // Make sure it's from the same domain
              try {
                const linkDomain = extractDomainAndTld(normalizedUrl);
                if (linkDomain !== siteDomain) {
                  continue;
                }
              } catch (error) {
                continue;
              }

              // Add to pending URLs
              pendingUrls.add(normalizedUrl);
              processedUrls.add(normalizedUrl); // Mark as processed immediately to avoid duplicates
              newLinksAdded++;

              // Also add to database as pending
              await SiteLink.findOrCreate({
                where: {
                  url: normalizedUrl,
                  siteId,
                },
                defaults: {
                  url: normalizedUrl,
                  siteId,
                  status: "pending",
                },
              });

              totalResultsAdded++;

              // Check if we've reached the limit
              if (totalResultsAdded >= maxResults) {
                logger.info(`Reached maximum results limit of ${maxResults}`);
                break;
              }
            } catch (error) {
              logger.error(`Error processing search result:`, error);
            }
          }
        }

        logger.info(`Found ${newLinksAdded} new links from search term "${term}"`);

        // Add a delay to avoid being rate-limited
        await new Promise((resolve) => setTimeout(resolve, 1000));

        stopTracking(`Completed search for term "${term}", total results so far: ${totalResultsAdded}`);
      } catch (error) {
        logger.error(`Error searching for term "${term}":`, error);
        stopTracking(`Error: ${error}`);

        // Add a longer delay if there was an error (might be rate limiting)
        await new Promise((resolve) => setTimeout(resolve, 5000));
      }
    }

    // Get final counts
    const totalLinks = await SiteLink.count({
      where: {
        siteId,
      },
    });

    const pendingLinks = await SiteLink.count({
      where: {
        siteId,
        status: "pending",
      },
    });

    logger.info(`CivicPlus Search API crawl completed for ${siteUrl}`);
    logger.info(`Total pages in database: ${totalLinks}`);
    logger.info(`Pending pages: ${pendingLinks}`);
    logger.info(`Search terms processed: ${totalSearchesProcessed}`);
    logger.info(`New results added: ${totalResultsAdded}`);

    return {
      success: true,
      totalLinks,
      pendingLinks,
      searchesProcessed: totalSearchesProcessed,
      resultsAdded: totalResultsAdded,
    };
  } catch (error) {
    logger.error(`Error crawling CivicPlus Search API for ${siteUrl}:`, error);
    return { success: false, error };
  }
}

// Get command line arguments
const args = process.argv.slice(2);
if (args.length < 1) {
  logger.error("Usage: npm run crawl-civicplus-search-v2 <site-url> [max-results]");
  process.exit(1);
}

const siteUrl = args[0];
const maxResults = args[1] ? parseInt(args[1], 10) : 1000;

void crawlCivicPlusSearch(siteUrl, maxResults)
  .then((result) => {
    if (result.success) {
      logger.info(`Successfully crawled CivicPlus Search API for ${siteUrl}`);
      process.exit(0);
    } else {
      logger.error(`Failed to crawl CivicPlus Search API for ${siteUrl}`);
      process.exit(1);
    }
  })
  .catch((error) => {
    logger.error(`Error in crawl_civicplus_search_v2 script:`, error);
    process.exit(1);
  });
