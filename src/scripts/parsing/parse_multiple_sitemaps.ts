import { processSitesInParallel } from "@/services/scraper";
import { logger } from "@/services/logger";
import { initDb } from "@/store/instance";
import { Site } from "@/store/models/site";

async function loadEnabledSites(): Promise<string[]> {
  await initDb();

  const sites = await Site.findAll({
    where: {
      enabled: true,
    },
    attributes: ["url"],
  });

  return sites.map((site) => site.getDataValue("url"));
}

async function main() {
  try {
    // Load sites dynamically from the database
    const sites = await loadEnabledSites();

    if (sites.length === 0) {
      logger.warn("No enabled sites found in the database");
      process.exit(0);
    }

    logger.info(`Found ${sites.length} enabled sites to process: ${sites.join(", ")}`);

    // Set concurrency level (how many sites to process in parallel)
    const concurrency = 3;

    // Set whether to crawl internal links
    const crawlInternalLinks = true;

    await processSitesInParallel(sites, crawlInternalLinks, concurrency);

    logger.info("All sites processed successfully");
    process.exit(0);
  } catch (error) {
    logger.error("Error processing sites:", error);
    process.exit(1);
  }
}

void main();
