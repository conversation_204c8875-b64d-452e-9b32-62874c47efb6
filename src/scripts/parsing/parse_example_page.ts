import { documentLoader } from "@/services/embeddings/document_loaders";
import { logger } from "@/services/logger";

documentLoader
  .fromHTMLPage("https://www.arlingtontx.gov/City-Services/Animals-Pets/Pet-Information/Responsible-Pet-Ownership")
  .then(({ pageContent, metadata, linksOnPage, fileLinksOnPage }) => {
    logger.info(`Page Content: \n${pageContent}`);
    logger.info(`PDF Links found on page: \n${fileLinksOnPage?.join("\n") || "No file links found"}`);
    logger.info(`Links found on page: \n${linksOnPage?.join("\n") || "No links found"}`);
  })
  .catch((error: unknown) => {
    console.error(error);
  });
