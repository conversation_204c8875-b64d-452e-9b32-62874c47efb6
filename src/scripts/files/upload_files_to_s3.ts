import * as fs from "fs";
import * as path from "path";

import { initDb } from "@/store/instance";
import { FileLink } from "@/store/models/file_link";
import { Site } from "@/store/models/site";
import { logger } from "@/services/logger";
import { uploadFileToS3 } from "@/services/s3";
import { getSiteDomain } from "@/utils/helpers";

const DOWNLOADS_DIR = path.join(process.cwd(), "downloads");

async function uploadExistingFiles() {
  await initDb();

  try {
    // Get all processed file links without S3 URLs
    const fileLinks = await FileLink.findAll({
      where: {
        status: "processed",
        s3Url: null,
      },
      include: [
        {
          model: Site,
          as: "site",
        },
      ],
    });

    if (fileLinks.length === 0) {
      logger.info("No files found that need to be uploaded to S3");
      return;
    }

    logger.info(`Found ${fileLinks.length} files to upload to S3`);

    for (const fileLink of fileLinks) {
      try {
        const fileUrl = fileLink.getDataValue("url");
        const fileType = fileLink.getDataValue("fileType");
        const filename = path.basename(fileUrl).split("?")[0];
        const filePath = path.join(DOWNLOADS_DIR, filename);

        // Check if file exists locally
        if (!fs.existsSync(filePath)) {
          logger.warn(`File not found locally: ${filePath}, skipping`);
          continue;
        }

        // Get site domain
        let siteDomain = "unknown-site";
        const site = fileLink.get("site");
        if (site) {
          siteDomain = getSiteDomain(site.getDataValue("url"));
        } else {
          siteDomain = getSiteDomain(fileUrl);
        }

        // Generate S3 key with file type folder
        const s3Key = `${fileType}s/${siteDomain}/${filename}`;

        // Upload file to S3
        const s3Url = await uploadFileToS3(filePath, s3Key);

        // Update file link with S3 information
        await fileLink.update({
          s3Key,
          s3Url,
        });

        logger.info(`Successfully uploaded ${filename} to S3: ${s3Url}`);
      } catch (error) {
        logger.error(`Error uploading file to S3: ${fileLink.getDataValue("url")}`, error);
      }
    }

    logger.info("Finished uploading files to S3");
  } catch (error) {
    logger.error("Error uploading files to S3:", error);
  }
}

// Execute the function
void uploadExistingFiles()
  .then(() => {
    logger.info("File upload process completed");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("Error in file upload process:", error);
    process.exit(1);
  });
