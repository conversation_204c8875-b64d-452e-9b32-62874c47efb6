import { Op } from "sequelize";

import { createEmbeddingsForMultipleSites } from "@/services/embeddings/create_embeddings";
import { logger } from "@/services/logger";
import { initDb } from "@/store/instance";
import { Site } from "@/store/models/site";
import { parseCommandLineArgs, validateProcessingOptions } from "@/utils/validation";

// Parse command line arguments
const args = process.argv.slice(2);
const parsedArgs = parseCommandLineArgs(args);

// Set default linksLimit based on whether a filter is provided
// When no filter is provided, this will be the limit PER SITE
const defaultLinksLimit = parsedArgs.urlIncludesFilter ? 10000 : 1;

// Validate and set default parameters
const params = validateProcessingOptions({
  linksLimit: parsedArgs.linksLimit || defaultLinksLimit,
  includeScraped: parsedArgs.includeScraped ?? false,
  crawlInternalLinks: parsedArgs.crawlInternalLinks ?? true,
  maxDepth: parsedArgs.maxDepth || 5,
  concurrency: parsedArgs.concurrency || 20,
  urlIncludesFilter: parsedArgs.urlIncludesFilter as string | undefined,
  forceRefresh: parsedArgs.forceRefresh ?? false,
});

logger.info(`Running with parameters: ${JSON.stringify(params)}`);
logger.info(`Note: linksLimit=${params.linksLimit} will be applied PER SITE`);

async function main() {
  try {
    await initDb();

    // Find all enabled sites
    const whereClause: any = { enabled: true };

    // Apply site filter if provided
    if (params.urlIncludesFilter) {
      try {
        // First, find site links that match the filter
        const { SiteLink } = await import("@/store/models/site_link");

        const matchingSiteLinks = await SiteLink.findAll({
          where: {
            url: {
              [Op.iLike]: `%${params.urlIncludesFilter}%`,
            },
          },
          attributes: ["siteId"],
          group: ["siteId"],
        });

        if (matchingSiteLinks.length > 0) {
          const siteIds = matchingSiteLinks.map((link) => link.getDataValue("siteId"));
          whereClause.id = {
            [Op.in]: siteIds,
          };
          logger.info(`Found site links matching filter, filtering by site IDs: ${siteIds.join(", ")}`);
        } else {
          // Fallback to original URL matching logic
          const filterText = params.urlIncludesFilter.startsWith("http")
            ? new URL(params.urlIncludesFilter).hostname
            : params.urlIncludesFilter;

          whereClause.url = {
            [Op.iLike]: `%${filterText}%`,
          };
          logger.info(`No matching site links found, filtering by site URL: %${filterText}%`);
        }
      } catch (error) {
        // If URL parsing fails, use the original filter text
        whereClause.url = {
          [Op.iLike]: `%${params.urlIncludesFilter}%`,
        };
        logger.info(`Error in site matching, using fallback URL filter: %${params.urlIncludesFilter}%`);
      }
    }

    const sites = await Site.findAll({
      where: whereClause,
      attributes: ["url"],
    });

    if (sites.length === 0) {
      logger.warn("No enabled sites found matching the criteria");
      process.exit(0);
    }

    const siteUrls = sites.map((site) => site.getDataValue("url"));
    logger.info(`Found ${siteUrls.length} sites to process: ${siteUrls.join(", ")}`);

    // Process embeddings for all sites
    await createEmbeddingsForMultipleSites({
      urlIncludesFilter: params.urlIncludesFilter,
      sites: siteUrls,
      linksLimit: params.linksLimit,
      includeScraped: params.includeScraped,
      crawlInternalLinks: params.crawlInternalLinks,
      concurrency: params.concurrency,
      maxDepth: params.maxDepth,
      forceRefresh: params.forceRefresh,
    });

    logger.info("All sites processed successfully");
    process.exit(0);
  } catch (error) {
    logger.error("Error processing sites:", error);
    process.exit(1);
  }
}

void main();
