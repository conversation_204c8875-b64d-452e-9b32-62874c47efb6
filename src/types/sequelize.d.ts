// import { DataTypes } from "sequelize";

// declare module "sequelize" {
//   namespace DataTypes {
//     export const VECTOR: VectorConstructor;

//     interface VectorConstructor {
//       (dimensions: number): VectorType;
//       new (dimensions: number): VectorType;
//       key: string;
//       types: {
//         postgres: string[];
//       };
//       parse: (value: string) => number[];
//     }

//     interface VectorType {
//       dimensions: number;
//       key: string;
//       toSql(): string;
//       validate(value: any): boolean;
//       _stringify(value: number[]): string;
//     }
//   }
// }
