import { Op } from "sequelize";

import { logger } from "@/services/logger";
import { Site } from "@/store/models/site";
import { SiteLink } from "@/store/models/site_link";
import { documentLoader } from "@/services/embeddings/document_loaders";
import { loadJavaScriptHeavySite } from "@/services/embeddings/browser-loader";
import { getVectorStore } from "@/services/embeddings/create_embeddings";
import { processNewlyDiscoveredLinks } from "@/services/embeddings/link-processor";
import { processFileLinksForSite } from "@/services/embeddings/file-processor";
import { delay } from "@/utils/async-utils";

export type ScrapingMode = "unscraped" | "rescraped" | "priority";

interface ScrapingConfig {
  mode: ScrapingMode;
  maxSites: number;
  linksPerSite: number;
  runDurationMinutes: number;
}

class AutomatedScrapingModes {
  private isRunning = false;
  private shouldStop = false;
  private config: ScrapingConfig;

  constructor(config: ScrapingConfig) {
    this.config = config;
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn("Automated scraping is already running");
      return;
    }

    logger.info(`Starting automated scraping in ${this.config.mode} mode...`);
    this.isRunning = true;
    this.shouldStop = false;

    const startTime = Date.now();
    const endTime = startTime + this.config.runDurationMinutes * 60 * 1000;

    try {
      while (Date.now() < endTime && !this.shouldStop) {
        await this.runScrapingBatch();

        // Check if we should continue
        if (Date.now() < endTime && !this.shouldStop) {
          logger.info("Waiting 30 seconds before next batch...");
          await delay(30000);
        }
      }

      logger.info(`Scraping completed after ${this.config.runDurationMinutes} minutes`);
    } catch (error) {
      logger.error("Error in scraping process:", error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  async stop(): Promise<void> {
    logger.info("Stopping automated scraping...");
    this.shouldStop = true;
  }

  private async runScrapingBatch(): Promise<void> {
    logger.info(`Running ${this.config.mode} scraping batch...`);

    const siteLinks = await this.getSiteLinksForMode();

    if (siteLinks.length === 0) {
      logger.info("No site links found for current mode, waiting...");
      await delay(60000); // Wait 1 minute if no links found
      return;
    }

    logger.info(`Processing ${siteLinks.length} site links`);
    await this.processSiteLinks(siteLinks);
  }

  private async getSiteLinksForMode(): Promise<any[]> {
    const { maxSites, linksPerSite } = this.config;

    switch (this.config.mode) {
      case "unscraped":
        return this.getUnscrapedSiteLinks(maxSites, linksPerSite);

      case "rescraped":
        return this.getRescrapedSiteLinks(maxSites, linksPerSite);

      case "priority":
        return this.getPrioritySiteLinks(maxSites, linksPerSite);

      default:
        throw new Error(`Unknown scraping mode: ${this.config.mode}`);
    }
  }

  private async getUnscrapedSiteLinks(maxSites: number, linksPerSite: number): Promise<any[]> {
    logger.info("Getting unscraped site links (never scraped before)");

    // Get sites that have unscraped links
    const sitesWithUnscrapedLinks = await Site.findAll({
      where: {
        enabled: true,
      },
      include: [
        {
          model: SiteLink,
          as: "siteLinks",
          where: {
            lastScrapedAt: null,
            status: "pending",
          },
          required: true,
          limit: 1, // Just to check if site has unscraped links
        },
      ],
      limit: maxSites,
    });

    logger.info(`Found ${sitesWithUnscrapedLinks.length} sites with unscraped links`);

    const allSiteLinks = [];

    for (const site of sitesWithUnscrapedLinks) {
      const siteLinks = await SiteLink.findAll({
        where: {
          siteId: site.getDataValue("id"),
          lastScrapedAt: null,
          status: "pending",
        },
        order: [["createdAt", "ASC"]], // Oldest first
        limit: linksPerSite,
      });

      logger.info(
        `Site ${site.getDataValue("name")} (ID: ${site.getDataValue("id")}): ${siteLinks.length} unscraped links`,
      );
      allSiteLinks.push(...siteLinks);
    }

    logger.info(`Total unscraped links to process: ${allSiteLinks.length}`);
    return allSiteLinks;
  }

  private async getRescrapedSiteLinks(maxSites: number, linksPerSite: number): Promise<any[]> {
    logger.info("Getting site links for re-scraping (oldest lastScrapedAt first)");

    // Get sites that have previously scraped links
    const sitesWithScrapedLinks = await Site.findAll({
      where: {
        enabled: true,
      },
      include: [
        {
          model: SiteLink,
          as: "siteLinks",
          where: {
            lastScrapedAt: { [Op.not]: null },
            status: { [Op.in]: ["scraped", "failed"] },
          },
          required: true,
          limit: 1, // Just to check if site has scraped links
        },
      ],
      limit: maxSites,
    });

    logger.info(`Found ${sitesWithScrapedLinks.length} sites with previously scraped links`);

    const allSiteLinks = [];

    for (const site of sitesWithScrapedLinks) {
      const siteLinks = await SiteLink.findAll({
        where: {
          siteId: site.getDataValue("id"),
          lastScrapedAt: { [Op.not]: null },
          status: { [Op.in]: ["scraped", "failed"] },
        },
        order: [["lastScrapedAt", "ASC"]], // Oldest scraped first
        limit: linksPerSite,
      });

      logger.info(
        `Site ${site.getDataValue("name")} (ID: ${site.getDataValue("id")}): ${siteLinks.length} links for re-scraping`,
      );
      allSiteLinks.push(...siteLinks);
    }

    logger.info(`Total links to re-scrape: ${allSiteLinks.length}`);
    return allSiteLinks;
  }

  private async getPrioritySiteLinks(maxSites: number, linksPerSite: number): Promise<any[]> {
    logger.info("Getting priority site links (unscraped first, then oldest scraped)");

    // Get sites that have priority links
    const sitesWithPriorityLinks = await Site.findAll({
      where: {
        enabled: true,
      },
      include: [
        {
          model: SiteLink,
          as: "siteLinks",
          where: {
            isPriority: true,
          },
          required: true,
          limit: 1, // Just to check if site has priority links
        },
      ],
      limit: maxSites,
    });

    logger.info(`Found ${sitesWithPriorityLinks.length} sites with priority links`);

    const allSiteLinks = [];

    for (const site of sitesWithPriorityLinks) {
      const siteLinks = await SiteLink.findAll({
        where: {
          siteId: site.getDataValue("id"),
          isPriority: true,
        },
        order: [
          ["lastScrapedAt", "ASC NULLS FIRST"], // Never scraped first, then oldest scraped
          ["createdAt", "ASC"],
        ],
        limit: linksPerSite,
      });

      logger.info(
        `Site ${site.getDataValue("name")} (ID: ${site.getDataValue("id")}): ${siteLinks.length} priority links`,
      );
      allSiteLinks.push(...siteLinks);
    }

    logger.info(`Total priority links to process: ${allSiteLinks.length}`);
    return allSiteLinks;
  }

  private async processSiteLinks(siteLinks: any[]): Promise<void> {
    const vectorStore = await getVectorStore();
    const BATCH_SIZE = 5; // Process 5 links at a time

    for (let i = 0; i < siteLinks.length; i += BATCH_SIZE) {
      if (this.shouldStop) {
        logger.info("Stopping processing due to stop signal");
        break;
      }

      const batch = siteLinks.slice(i, i + BATCH_SIZE);
      logger.info(`Processing batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(siteLinks.length / BATCH_SIZE)}`);

      await Promise.all(
        batch.map(async (link) => {
          try {
            await this.processIndividualLink(link, vectorStore);
          } catch (error) {
            logger.error(`Error processing link ${link.getDataValue("url")}:`, error);
            await link.update({
              status: "failed",
              lastScrapedAt: new Date(),
              comment: (error as Error).message,
            });
          }
        }),
      );

      // Small delay between batches
      if (i + BATCH_SIZE < siteLinks.length && !this.shouldStop) {
        await delay(2000);
      }
    }
  }

  private async processIndividualLink(link: any, vectorStore: any): Promise<void> {
    const url = link.getDataValue("url");
    const siteLinkId = link.getDataValue("id");
    const siteId = link.getDataValue("siteId");

    logger.info(`Processing link: ${url}`);

    try {
      // Get the site to check if it requires JavaScript
      const site = await Site.findByPk(siteId);
      const requiresJavaScript = site?.getDataValue("requiresJavascript") || false;

      // Load document
      let document;
      try {
        if (requiresJavaScript) {
          document = await loadJavaScriptHeavySite(url);
        } else {
          document = await documentLoader(url);
        }
      } catch (error) {
        // If standard loader fails, try with Puppeteer
        if (!requiresJavaScript) {
          logger.warn(`Standard loader failed for ${url}, trying with Puppeteer`);
          document = await loadJavaScriptHeavySite(url);

          // Mark site as requiring JavaScript
          if (document && site) {
            await site.update({ requiresJavascript: true });
          }
        } else {
          throw error;
        }
      }

      if (!document) {
        throw new Error("Failed to create document");
      }

      // Store the document content and metadata
      await link.update({
        pageContent: document.pageContent,
        originalHtml: document.metadata?.originalHtml || null,
        linksOnPage: document.metadata?.linksOnPage || [],
        fileLinksOnPage: document.metadata?.fileLinksOnPage || [],
        status: "scraped",
        lastScrapedAt: new Date(),
        timeElapsed: Date.now() - Date.now(), // This would be calculated properly in real implementation
        comment: "Processed successfully",
      });

      // Add document to vector store
      await vectorStore.addDocuments([document]);

      // Process newly discovered links if enabled
      const newLinksAdded = await processNewlyDiscoveredLinks(link, {
        crawlInternalLinks: true,
        maxDepth: 2,
      });

      // Process file links for this site
      await processFileLinksForSite(siteId);

      logger.info(`Successfully processed ${url}, added ${newLinksAdded} new links`);
    } catch (error) {
      logger.error(`Failed to process ${url}:`, error);
      throw error;
    }
  }

  getStatus(): any {
    return {
      isRunning: this.isRunning,
      shouldStop: this.shouldStop,
      config: this.config,
    };
  }
}

export { AutomatedScrapingModes };
