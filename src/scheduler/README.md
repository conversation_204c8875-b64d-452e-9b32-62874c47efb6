# Automated Scraping Modes

This directory contains the new automated scraping system with three distinct modes for processing site links.

## TL;DR

# Run database migration first
npm run migrate

# Basic usage (30 minutes, 10 sites, 10 links per site)
npm run scrape-unscraped
npm run scrape-rescraped  
npm run scrape-priority

# Custom parameters: mode maxSites linksPerSite runDurationMinutes
npx tsx src/scripts/automation/run_scraping_modes.ts priority 8 12 45

# Manage priority links
npx tsx src/scripts/automation/manage_priority_links.ts set --site-id 5
npx tsx src/scripts/automation/manage_priority_links.ts list



## Overview

The system provides three scraping modes:

1. **Unscraped Mode** - Scrapes site links that have never been scraped before
2. **Rescraped Mode** - Re-scrapes site links that were previously scraped (oldest first)
3. **Priority Mode** - Scrapes site links marked with `isPriority=true` flag

Each mode processes up to 10 site links from up to 10 sites, runs for 30 minutes, then automatically restarts.

## Files

- `automated_scraping_modes.ts` - Core scraping logic with three modes
- `run_scraping_modes.ts` - Main CLI script to run scraping modes
- `manage_priority_links.ts` - Utility to manage priority flags on site links
- `README.md` - This documentation

## Database Changes

A new `isPriority` boolean field has been added to the `site_links` table:

```sql
ALTER TABLE site_links ADD COLUMN is_priority BOOLEAN DEFAULT FALSE;
```

Run the migration:
```bash
# The migration file is: src/scripts/db/migrations/20250630000000-add-is-priority-to-site-links.js
```

## Usage

### Running Scraping Modes

```bash
# Basic usage - scrape unscraped links for 30 minutes
tsx src/scripts/automation/run_scraping_modes.ts unscraped

# Re-scrape old links from 5 sites, 15 links per site, for 60 minutes  
tsx src/scripts/automation/run_scraping_modes.ts rescraped 5 15 60

# Scrape priority links from 8 sites, 12 links per site, for 45 minutes
tsx src/scripts/automation/run_scraping_modes.ts priority 8 12 45
```

### Managing Priority Links

```bash
# Set priority on all links for site ID 5
tsx src/scripts/automation/manage_priority_links.ts set --site-id 5

# Set priority on links containing "important" in URL for all sites
tsx src/scripts/automation/manage_priority_links.ts set --url-pattern important --all

# List all current priority links
tsx src/scripts/automation/manage_priority_links.ts list

# Count priority links by site
tsx src/scripts/automation/manage_priority_links.ts count

# Remove priority from all links for site with "example" in name
tsx src/scripts/automation/manage_priority_links.ts unset --site-name example
```

## Scraping Mode Details

### 1. Unscraped Mode (`unscraped`)

- **Target**: Site links where `lastScrapedAt` is `null` and `status` is `'pending'`
- **Selection**: Up to 10 links per site from up to 10 sites
- **Ordering**: Oldest created links first (`createdAt ASC`)
- **Use Case**: Process completely new links that have never been scraped

### 2. Rescraped Mode (`rescraped`)

- **Target**: Site links where `lastScrapedAt` is not `null` and `status` is `'scraped'` or `'failed'`
- **Selection**: Up to 10 links per site from up to 10 sites
- **Ordering**: Oldest scraped links first (`lastScrapedAt ASC`)
- **Use Case**: Refresh content from previously scraped links, starting with the oldest

### 3. Priority Mode (`priority`)

- **Target**: Site links where `isPriority` is `true`
- **Selection**: Up to 10 links per site from up to 10 sites
- **Ordering**: Never scraped first (`lastScrapedAt ASC NULLS FIRST`), then oldest scraped
- **Use Case**: Process high-priority links regardless of previous scraping status

## Process Flow

1. **Initialization**: Database connection and service setup
2. **Site Selection**: Find enabled sites with matching links for the selected mode
3. **Link Selection**: Get up to 10 links per site based on mode criteria
4. **Batch Processing**: Process links in batches of 5 with error handling
5. **Content Processing**: 
   - Load document content (with JavaScript support if needed)
   - Store content and metadata in database
   - Add to vector store for embeddings
   - Process newly discovered links
   - Process file links
6. **Cycle Management**: Run for specified duration, then restart

## Configuration

Default configuration:
- **Max Sites**: 10 sites per cycle
- **Links Per Site**: 10 links per site
- **Run Duration**: 30 minutes per cycle
- **Batch Size**: 5 links processed concurrently
- **Restart**: Automatic restart after each cycle

## Monitoring

The system provides detailed logging:
- Site and link selection statistics
- Processing progress and batch information
- Error handling and failed link tracking
- Performance metrics and timing

## Error Handling

- Individual link failures don't stop the batch
- Failed links are marked with `status='failed'` and error message in `comment`
- Graceful shutdown on SIGTERM/SIGINT signals
- Automatic retry logic for document loading (standard → JavaScript-heavy)

## Examples

### Typical Workflow

1. **Set up priority links**:
   ```bash
   # Mark important pages as priority
   tsx src/scripts/automation/manage_priority_links.ts set --url-pattern "/important/" --all
   ```

2. **Run priority scraping**:
   ```bash
   # Process priority links for 60 minutes
   tsx src/scripts/automation/run_scraping_modes.ts priority 10 10 60
   ```

3. **Process unscraped content**:
   ```bash
   # Handle new links discovered during priority scraping
   tsx src/scripts/automation/run_scraping_modes.ts unscraped
   ```

4. **Refresh old content**:
   ```bash
   # Update oldest scraped content
   tsx src/scripts/automation/run_scraping_modes.ts rescraped
   ```

### Production Deployment

For production use, consider running different modes on separate schedules:

```bash
# Morning: Priority links
0 6 * * * tsx src/scripts/automation/run_scraping_modes.ts priority 15 8 120

# Afternoon: New unscraped links  
0 14 * * * tsx src/scripts/automation/run_scraping_modes.ts unscraped 12 10 90

# Evening: Refresh old content
0 22 * * * tsx src/scripts/automation/run_scraping_modes.ts rescraped 8 15 60
```
