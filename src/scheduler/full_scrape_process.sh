#!/bin/bash

# Configuration
DB_USER="postgres"
DB_NAME="marvin"
SLEEP_MINUTES=30

echo "Starting automated scraping process"

while true; do
  echo "===== Starting scraping run at $(date) ====="

  # Step 1: Parse sitemaps for all enabled sites
  echo "Parsing sitemaps..."
  npm run parse-multiple-sitemaps

  # Step 2: Create embeddings with internal link crawling
  echo "Creating embeddings..."
  npm run create-embeddings -- --crawlInternalLinks=true --maxDepth=5

  # Step 3: Update materialized views for client schemas
  echo "Updating materialized views..."
  psql -U $DB_USER -d $DB_NAME -f src/scripts/psql/migrate-to-schemas.sql
  psql -U $DB_USER -d $DB_NAME -f src/scripts/psql/create-materialized-views-direct.sql

  # Step 4: Upload files to S3
  echo "Uploading files to S3..."
  npm run upload-files

  echo "===== Finished scraping run at $(date). Waiting ${SLEEP_MINUTES} minutes... ====="
  sleep $(($SLEEP_MINUTES * 60))
done