import { exec } from "child_process";
import { promisify } from "util";

import { logger } from "@/services/logger";
import { backgroundJobService } from "@/services/jobs/background-job-service";
import { delay } from "@/utils/async-utils";

const execAsync = promisify(exec);

interface AutomationConfig {
  intervalMinutes: number;
  enableSitemapJobs: boolean;
  enableEmbeddingsJobs: boolean;
  enableSqlMigration: boolean;
  enableS3Upload: boolean;
  batchSize: number;
  maxConcurrentJobs: number;
}

class AutomatedScrapingService {
  private config: AutomationConfig;
  private isRunning = false;
  private intervalTimer?: NodeJS.Timeout;

  constructor(config: Partial<AutomationConfig> = {}) {
    this.config = {
      intervalMinutes: 30,
      enableSitemapJobs: true,
      enableEmbeddingsJobs: true,
      enableSqlMigration: true,
      enableS3Upload: true,
      batchSize: 100,
      maxConcurrentJobs: 3,
      ...config,
    };
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn("Automated scraping service is already running");
      return;
    }

    logger.info("Starting automated scraping service...");
    this.isRunning = true;

    // Initialize background job service
    await backgroundJobService.initialize();

    // Run initial scraping cycle
    await this.runScrapingCycle();

    // Schedule periodic runs
    this.intervalTimer = setInterval(
      async () => {
        try {
          await this.runScrapingCycle();
        } catch (error) {
          logger.error("Error in automated scraping cycle:", error);
        }
      },
      this.config.intervalMinutes * 60 * 1000,
    );

    logger.info(`Automated scraping service started with ${this.config.intervalMinutes} minute intervals`);
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    logger.info("Stopping automated scraping service...");
    this.isRunning = false;

    if (this.intervalTimer) {
      clearInterval(this.intervalTimer);
    }

    logger.info("Automated scraping service stopped");
  }

  private async runScrapingCycle(): Promise<void> {
    const cycleStartTime = new Date();
    logger.info(`===== Starting automated scraping cycle at ${cycleStartTime} =====`);

    try {
      // Step 1: Schedule sitemap jobs if enabled
      if (this.config.enableSitemapJobs) {
        await this.scheduleSitemapJobs();
      }

      // Step 2: Wait for sitemap jobs to complete, then schedule embeddings
      if (this.config.enableEmbeddingsJobs) {
        await this.scheduleEmbeddingsJobs();
      }

      // Step 3: Wait for all jobs to complete
      await this.waitForJobsToComplete();

      // Step 4: Run SQL migration if enabled
      if (this.config.enableSqlMigration) {
        await this.runSqlMigration();
      }

      // Step 5: Upload files to S3 if enabled
      if (this.config.enableS3Upload) {
        await this.uploadFilesToS3();
      }

      const cycleEndTime = new Date();
      const duration = Math.round((cycleEndTime.getTime() - cycleStartTime.getTime()) / 1000 / 60);
      logger.info(`===== Completed scraping cycle in ${duration} minutes =====`);
    } catch (error) {
      logger.error("Error in scraping cycle:", error);
      throw error;
    }
  }

  private async scheduleSitemapJobs(): Promise<void> {
    logger.info("Scheduling sitemap jobs for all enabled sites...");

    const scheduler = backgroundJobService.getScheduler();
    const jobIds = await scheduler.scheduleJobsForAllSites("sitemap", {
      priority: 2,
      batchSize: this.config.batchSize,
    });

    logger.info(`Scheduled ${jobIds.length} sitemap jobs`);
  }

  private async scheduleEmbeddingsJobs(): Promise<void> {
    logger.info("Waiting for sitemap jobs to complete before scheduling embeddings...");

    // Wait for sitemap jobs to complete
    await this.waitForJobsOfType("sitemap");

    logger.info("Scheduling embeddings jobs for all enabled sites...");

    const scheduler = backgroundJobService.getScheduler();
    const jobIds = await scheduler.scheduleJobsForAllSites("embeddings", {
      priority: 1,
      batchSize: this.config.batchSize,
    });

    logger.info(`Scheduled ${jobIds.length} embeddings jobs`);
  }

  private async waitForJobsOfType(jobType: string): Promise<void> {
    const { ScrapeJob } = await import("@/store/models/scrape_job");

    while (true) {
      const activeJobs = await ScrapeJob.count({
        where: {
          jobType: jobType,
          status: ["pending", "running"],
        },
      });

      if (activeJobs === 0) {
        logger.info(`All ${jobType} jobs completed`);
        break;
      }

      logger.info(`Waiting for ${activeJobs} ${jobType} jobs to complete...`);
      await delay(30000); // Check every 30 seconds
    }
  }

  private async waitForJobsToComplete(): Promise<void> {
    logger.info("Waiting for all scraping jobs to complete...");

    const { ScrapeJob } = await import("@/store/models/scrape_job");

    while (true) {
      const activeJobs = await ScrapeJob.count({
        where: {
          status: ["pending", "running"],
        },
      });

      if (activeJobs === 0) {
        logger.info("All scraping jobs completed");
        break;
      }

      logger.info(`Waiting for ${activeJobs} jobs to complete...`);
      await delay(60000); // Check every minute
    }
  }

  private async runSqlMigration(): Promise<void> {
    logger.info("Running SQL migration scripts...");

    try {
      // Run create-materialized-views-direct.sql
      logger.info("Creating materialized views...");
      await execAsync(`psql -U postgres -d marvin -f src/scripts/psql/create-materialized-views-direct.sql`);

      // Run migrate-to-schemas.sql
      logger.info("Migrating to schemas...");
      await execAsync(`psql -U postgres -d marvin -f src/scripts/psql/migrate-to-schemas.sql`);

      logger.info("SQL migration completed successfully");
    } catch (error) {
      logger.error("SQL migration failed:", error);
      throw error;
    }
  }

  private async uploadFilesToS3(): Promise<void> {
    logger.info("Uploading files to S3...");

    try {
      await execAsync(`npm run upload-files`);
      logger.info("S3 upload completed successfully");
    } catch (error) {
      logger.error("S3 upload failed:", error);
      throw error;
    }
  }

  async getStatus(): Promise<any> {
    const health = await backgroundJobService.healthCheck();
    const activeJobs = await backgroundJobService.getActiveJobsStatus();

    return {
      isRunning: this.isRunning,
      config: this.config,
      health,
      activeJobs: activeJobs.length,
      jobDetails: activeJobs,
    };
  }
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || "start";

  const automationService = new AutomatedScrapingService({
    intervalMinutes: parseInt(args[1]) || 30,
    batchSize: parseInt(args[2]) || 100,
  });

  // Handle graceful shutdown
  const shutdown = async (signal: string) => {
    logger.info(`Received ${signal}, shutting down gracefully...`);
    try {
      await automationService.stop();
      await backgroundJobService.stop();
      process.exit(0);
    } catch (error) {
      logger.error("Error during shutdown:", error);
      process.exit(1);
    }
  };

  process.on("SIGTERM", () => shutdown("SIGTERM"));
  process.on("SIGINT", () => shutdown("SIGINT"));

  try {
    switch (command) {
      case "start":
        await automationService.start();
        logger.info("Automated scraping service is running. Press Ctrl+C to stop.");
        break;

      case "status":
        const status = await automationService.getStatus();
        console.log(JSON.stringify(status, null, 2));
        process.exit(0);
        break;

      default:
        console.log(`
Usage: tsx src/scripts/automation/automated_scraping_with_jobs.ts [command] [interval_minutes] [batch_size]

Commands:
  start   - Start the automated scraping service (default)
  status  - Show current status

Examples:
  tsx src/scripts/automation/automated_scraping_with_jobs.ts start 30 100
  tsx src/scripts/automation/automated_scraping_with_jobs.ts status
`);
        process.exit(1);
    }
  } catch (error) {
    logger.error("Failed to run automated scraping service:", error);
    process.exit(1);
  }
}

void main();
