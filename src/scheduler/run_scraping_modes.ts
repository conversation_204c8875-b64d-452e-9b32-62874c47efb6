#!/usr/bin/env tsx

import { logger } from "@/services/logger";
import { initDb } from "@/store/instance";
import { AutomatedScrapingModes, ScrapingMode } from "./automated_scraping_modes";

interface CliOptions {
  mode: ScrapingMode;
  maxSites: number;
  linksPerSite: number;
  runDurationMinutes: number;
}

function parseArguments(): CliOptions {
  const args = process.argv.slice(2);

  if (args.length === 0 || args.includes("--help") || args.includes("-h")) {
    showHelp();
    process.exit(0);
  }

  const mode = args[0] as ScrapingMode;
  const maxSites = parseInt(args[1]) || 10;
  const linksPerSite = parseInt(args[2]) || 10;
  const runDurationMinutes = parseInt(args[3]) || 30;

  // Validate mode
  if (!["unscraped", "rescraped", "priority"].includes(mode)) {
    console.error(`Invalid mode: ${mode}`);
    console.error("Valid modes are: unscraped, rescraped, priority");
    process.exit(1);
  }

  return {
    mode,
    maxSites,
    linksPerSite,
    runDurationMinutes,
  };
}

function showHelp(): void {
  console.log(`
Automated Scraping Modes

Usage: tsx src/scripts/automation/run_scraping_modes.ts <mode> [maxSites] [linksPerSite] [runDurationMinutes]

Modes:
  unscraped   - Scrape site links that have never been scraped (lastScrapedAt is null)
  rescraped   - Re-scrape site links that were previously scraped (oldest lastScrapedAt first)
  priority    - Scrape priority site links (isPriority=true, unscraped first then oldest)

Parameters:
  mode               - Required. One of: unscraped, rescraped, priority
  maxSites           - Optional. Maximum number of sites to process (default: 10)
  linksPerSite       - Optional. Maximum links per site to process (default: 10)
  runDurationMinutes - Optional. How long to run in minutes (default: 30)

Examples:
  # Scrape unscraped links from up to 10 sites, 10 links per site, for 30 minutes
  tsx src/scripts/automation/run_scraping_modes.ts unscraped

  # Re-scrape old links from 5 sites, 15 links per site, for 60 minutes
  tsx src/scripts/automation/run_scraping_modes.ts rescraped 5 15 60

  # Scrape priority links from 8 sites, 12 links per site, for 45 minutes
  tsx src/scripts/automation/run_scraping_modes.ts priority 8 12 45

Description:
  This script runs automated scraping in one of three modes:

  1. UNSCRAPED MODE:
     - Finds site links that have never been scraped (lastScrapedAt is null)
     - Takes up to 10 links per site from up to 10 sites
     - Processes oldest created links first

  2. RESCRAPED MODE:
     - Finds site links that have been scraped before
     - Takes up to 10 links per site from up to 10 sites  
     - Processes links with oldest lastScrapedAt first

  3. PRIORITY MODE:
     - Finds site links marked with isPriority=true
     - Takes up to 10 links per site from up to 10 sites
     - Processes unscraped priority links first, then oldest scraped priority links

  The process runs for 30 minutes by default, then automatically restarts.
  Press Ctrl+C to stop gracefully.
`);
}

async function main(): Promise<void> {
  const options = parseArguments();

  logger.info(`Starting automated scraping with options:`, options);

  // Initialize database
  await initDb();

  const scrapingService = new AutomatedScrapingModes({
    mode: options.mode,
    maxSites: options.maxSites,
    linksPerSite: options.linksPerSite,
    runDurationMinutes: options.runDurationMinutes,
  });

  // Handle graceful shutdown
  const shutdown = async (signal: string) => {
    logger.info(`Received ${signal}, shutting down gracefully...`);
    try {
      await scrapingService.stop();
      process.exit(0);
    } catch (error) {
      logger.error("Error during shutdown:", error);
      process.exit(1);
    }
  };

  process.on("SIGTERM", () => shutdown("SIGTERM"));
  process.on("SIGINT", () => shutdown("SIGINT"));

  try {
    // Run the scraping process
    while (true) {
      logger.info(`Starting ${options.runDurationMinutes}-minute scraping cycle...`);
      await scrapingService.start();

      logger.info(`Completed ${options.runDurationMinutes}-minute cycle. Restarting...`);

      // Brief pause before restarting
      await new Promise((resolve) => setTimeout(resolve, 5000));
    }
  } catch (error) {
    logger.error("Failed to run automated scraping:", error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on("unhandledRejection", (reason, promise) => {
  logger.error("Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

process.on("uncaughtException", (error) => {
  logger.error("Uncaught Exception:", error);
  process.exit(1);
});

void main();
