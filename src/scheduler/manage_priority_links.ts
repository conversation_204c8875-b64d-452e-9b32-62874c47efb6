#!/usr/bin/env tsx

import { Op } from "sequelize";

import { logger } from "@/services/logger";
import { initDb } from "@/store/instance";
import { Site } from "@/store/models/site";
import { SiteLink } from "@/store/models/site_link";

interface CliOptions {
  action: "set" | "unset" | "list" | "count";
  siteId?: number;
  siteName?: string;
  urlPattern?: string;
  all?: boolean;
}

function parseArguments(): CliOptions {
  const args = process.argv.slice(2);

  if (args.length === 0 || args.includes("--help") || args.includes("-h")) {
    showHelp();
    process.exit(0);
  }

  const action = args[0] as "set" | "unset" | "list" | "count";
  const options: CliOptions = { action };

  for (let i = 1; i < args.length; i++) {
    const arg = args[i];
    const nextArg = args[i + 1];

    switch (arg) {
      case "--site-id":
        options.siteId = parseInt(nextArg);
        i++;
        break;
      case "--site-name":
        options.siteName = nextArg;
        i++;
        break;
      case "--url-pattern":
        options.urlPattern = nextArg;
        i++;
        break;
      case "--all":
        options.all = true;
        break;
    }
  }

  // Validate action
  if (!["set", "unset", "list", "count"].includes(action)) {
    console.error(`Invalid action: ${action}`);
    console.error("Valid actions are: set, unset, list, count");
    process.exit(1);
  }

  return options;
}

function showHelp(): void {
  console.log(`
Priority Links Management

Usage: tsx src/scripts/automation/manage_priority_links.ts <action> [options]

Actions:
  set     - Set isPriority=true on matching site links
  unset   - Set isPriority=false on matching site links  
  list    - List all priority site links
  count   - Count priority site links by site

Options:
  --site-id <id>        - Target specific site by ID
  --site-name <name>    - Target specific site by name (partial match)
  --url-pattern <pattern> - Target URLs containing this pattern
  --all                 - Apply to all sites (use with caution)

Examples:
  # Set priority on all links for site ID 5
  tsx src/scripts/automation/manage_priority_links.ts set --site-id 5

  # Set priority on links containing "important" in URL for all sites
  tsx src/scripts/automation/manage_priority_links.ts set --url-pattern important --all

  # List all current priority links
  tsx src/scripts/automation/manage_priority_links.ts list

  # Count priority links by site
  tsx src/scripts/automation/manage_priority_links.ts count

  # Remove priority from all links for site with "example" in name
  tsx src/scripts/automation/manage_priority_links.ts unset --site-name example
`);
}

async function main(): Promise<void> {
  const options = parseArguments();

  logger.info(`Running priority links management with options:`, options);

  // Initialize database
  await initDb();

  switch (options.action) {
    case "set":
      await setPriorityLinks(options);
      break;
    case "unset":
      await unsetPriorityLinks(options);
      break;
    case "list":
      await listPriorityLinks();
      break;
    case "count":
      await countPriorityLinks();
      break;
  }
}

async function setPriorityLinks(options: CliOptions): Promise<void> {
  const whereClause = await buildWhereClause(options);

  const [updatedCount] = await SiteLink.update({ isPriority: true }, { where: whereClause });

  logger.info(`Set isPriority=true on ${updatedCount} site links`);
}

async function unsetPriorityLinks(options: CliOptions): Promise<void> {
  const whereClause = await buildWhereClause(options);

  const [updatedCount] = await SiteLink.update({ isPriority: false }, { where: whereClause });

  logger.info(`Set isPriority=false on ${updatedCount} site links`);
}

async function listPriorityLinks(): Promise<void> {
  const priorityLinks = await SiteLink.findAll({
    where: { isPriority: true },
    include: [
      {
        model: Site,
        as: "site",
        attributes: ["id", "name", "url"],
      },
    ],
    order: [
      ["siteId", "ASC"],
      ["createdAt", "ASC"],
    ],
    limit: 100, // Limit to avoid overwhelming output
  });

  console.log(`\nFound ${priorityLinks.length} priority links:\n`);

  for (const link of priorityLinks) {
    const site = link.getDataValue("site") as any;
    console.log(`Site: ${site?.name || "Unknown"} (ID: ${link.getDataValue("siteId")})`);
    console.log(`URL: ${link.getDataValue("url")}`);
    console.log(`Status: ${link.getDataValue("status")}`);
    console.log(`Last Scraped: ${link.getDataValue("lastScrapedAt") || "Never"}`);
    console.log("---");
  }
}

async function countPriorityLinks(): Promise<void> {
  const counts = await SiteLink.findAll({
    where: { isPriority: true },
    attributes: ["siteId", [SiteLink.sequelize!.fn("COUNT", "*"), "count"]],
    include: [
      {
        model: Site,
        as: "site",
        attributes: ["name", "url"],
      },
    ],
    group: ["siteId", "site.id", "site.name", "site.url"],
    order: [[SiteLink.sequelize!.fn("COUNT", "*"), "DESC"]],
  });

  console.log(`\nPriority links count by site:\n`);

  for (const count of counts) {
    const site = count.getDataValue("site") as any;
    console.log(
      `${site?.name || "Unknown"} (ID: ${count.getDataValue("siteId")}): ${count.getDataValue("count")} priority links`,
    );
  }
}

async function buildWhereClause(options: CliOptions): Promise<any> {
  const whereClause: any = {};

  if (options.siteId) {
    whereClause.siteId = options.siteId;
  } else if (options.siteName) {
    // Find sites matching the name pattern
    const sites = await Site.findAll({
      where: {
        name: { [Op.iLike]: `%${options.siteName}%` },
      },
      attributes: ["id"],
    });

    if (sites.length === 0) {
      throw new Error(`No sites found matching name pattern: ${options.siteName}`);
    }

    whereClause.siteId = { [Op.in]: sites.map((site) => site.getDataValue("id")) };
  } else if (!options.all) {
    throw new Error("Must specify --site-id, --site-name, or --all");
  }

  if (options.urlPattern) {
    whereClause.url = { [Op.iLike]: `%${options.urlPattern}%` };
  }

  return whereClause;
}

// Handle unhandled promise rejections
process.on("unhandledRejection", (reason, promise) => {
  logger.error("Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

process.on("uncaughtException", (error) => {
  logger.error("Uncaught Exception:", error);
  process.exit(1);
});

void main();
