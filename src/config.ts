import "dotenv/config";
import z from "zod";

const ConfigSchema = z.strictObject({
  openaiApiKey: z.string(),
  databaseUrl: z.string(),

  port: z.coerce.number().int().positive().default(3001),
  nodeEnv: z.enum(["development", "production", "test"]).default("development"),
  logLevel: z.string().default("info"),
  logSQL: z.boolean().default(false),

  siteUrl: z.string().url(),

  chunkSize: z.number().default(4000),
  chunkOverlap: z.number().default(400),

  completionsModel: z.string().default("gpt-4o"),
  embeddingModel: z.string().default("text-embedding-3-small"),
  chromiumPath: z.string().optional(), // /snap/bin/chromium

  temperature: z.number().default(0.2),

  aws: z.object({
    region: z.string().default("us-east-1"),
    accessKeyId: z.string(),
    secretAccessKey: z.string(),
    s3Bucket: z.string(),
  }),
});

const validateConfigFromEnv = () => {
  const result = ConfigSchema.safeParse({
    openaiApiKey: process.env.OPENAI_API_KEY,
    databaseUrl: process.env.DATABASE_URL,

    logLevel: process.env.LOG_LEVEL,
    logSQL: process.env.LOG_SQL === "true",

    siteUrl: process.env.SITE_URL,

    chunkSize: process.env.CHUNK_SIZE,
    chunkOverlap: process.env.CHUNK_OVERLAP,

    completionsModel: process.env.COMPLETIONS_MODEL,
    embeddingModel: process.env.EMBEDDING_MODEL,
    chromiumPath: process.env.CHROMIUM_PATH,

    temperature: process.env.TEMPERATURE,

    aws: {
      region: process.env.AWS_REGION,
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      s3Bucket: process.env.AWS_S3_BUCKET,
    },
  });

  if (!result.success) {
    console.error("Invalid configuration:", result.error.format());
    process.exit(1);
  }

  return result.data;
};

export const config = validateConfigFromEnv();
