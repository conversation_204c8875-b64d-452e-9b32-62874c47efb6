// import { DataTypes, Utils } from "sequelize";

// export function registerVectorType() {
//   if (DataTypes.VECTOR) {
//     return;
//   }

//   class VECTOR extends DataTypes.ABSTRACT {
//     dimensions: number;

//     constructor(dimensions: number) {
//       super();
//       this.dimensions = dimensions;
//     }

//     toSql() {
//       return `VECTOR(${String(this.dimensions)})`;
//     }

//     validate(value: any) {
//       if (!Array.isArray(value)) {
//         return false;
//       }
//       if (value.length !== this.dimensions) {
//         return false;
//       }
//       return value.every((n) => typeof n === "number" && !Number.isNaN(n));
//     }

//     _stringify(value: number[]) {
//       if (!Array.isArray(value)) {
//         return "[]";
//       }
//       return `[${value.join(",")}]`;
//     }

//     static parse(value: string) {
//       if (typeof value === "string") {
//         if (value.startsWith("[") && value.endsWith("]")) {
//           return value.slice(1, -1).split(",").map(Number);
//         }
//       } else if (Array.isArray(value)) {
//         return value;
//       }
//       return [];
//     }
//   }

//   VECTOR.prototype.key = VECTOR.key = "VECTOR";

//   DataTypes.VECTOR = Utils.classToInvokable(VECTOR);

//   const PgTypes = DataTypes.postgres;

//   DataTypes.VECTOR.types = {
//     postgres: ["vector"],
//   };

//   PgTypes.VECTOR = function PgVECTOR(dimensions: number) {
//     if (!(this instanceof PgTypes.VECTOR)) {
//       return new (PgTypes.VECTOR as any)(dimensions);
//     }
//     DataTypes.VECTOR.call(this, dimensions);
//   };

//   Utils.inherits(PgTypes.VECTOR, DataTypes.VECTOR);
//   PgTypes.VECTOR.parse = DataTypes.VECTOR.parse;
// }
