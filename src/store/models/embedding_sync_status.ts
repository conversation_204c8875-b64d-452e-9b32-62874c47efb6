import { DataTypes, Model } from "sequelize";

import { sequelize } from "@/store/instance";

export interface EmbeddingSyncStatusAttributes {
  id: number;
  embeddingType: string;
  lastSyncedAt: Date;
  recordsSynced: number;
  createdAt?: Date;
}

export type EmbeddingSyncStatusModel = Model<EmbeddingSyncStatusAttributes, Partial<EmbeddingSyncStatusAttributes>>;

export const EmbeddingSyncStatus = sequelize.define<EmbeddingSyncStatusModel>(
  "embedding_sync_status",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    embeddingType: {
      type: DataTypes.STRING(50),
      allowNull: false,
      field: "embedding_type",
    },
    lastSyncedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: "last_synced_at",
    },
    recordsSynced: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: "records_synced",
    },
    createdAt: {
      type: DataTypes.DATE,
      field: "created_at",
      defaultValue: DataTypes.NOW,
    },
  },
  {
    schema: "public",
    tableName: "embedding_sync_status",
    timestamps: false,
  },
);
