import { DataTypes, Model } from "sequelize";

import { sequelize } from "@/store/instance";

export interface SourceOverrideAttributes {
  id: number;
  clientId: number;
  keywords: string[];
  overrideText: string;
  enabled: boolean;

  createdAt?: Date;
  updatedAt?: Date;
}

export type SourceOverrideModel = Model<SourceOverrideAttributes, Partial<SourceOverrideAttributes>>;

export const SourceOverride = sequelize.define<SourceOverrideModel>(
  "source_override",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    clientId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    keywords: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: "",
    },
    overrideText: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
  },
  {
    schema: "public",
    tableName: "source_overrides",
    underscored: true,
  },
);
