import { DataTypes, Model } from "sequelize";

import { sequelize } from "@/store/instance";

interface TokenUsageMetadata {
  source?: string;
  context?: string;
  success?: boolean;
  error?: string;

  additionalInfo?: Record<string, string | number | boolean>;
}

interface TokenUsageAttributes {
  id: number;
  operationType: "embedding" | "completion" | "other";

  modelName: string;

  promptTokens: number;
  completionTokens: number;
  totalTokens: number;

  estimatedCost: number;

  metadata?: TokenUsageMetadata;

  createdAt?: Date;
  updatedAt?: Date;
}

export const TokenUsage = sequelize.define<Model<TokenUsageAttributes>>(
  "token_usage",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    operationType: {
      type: DataTypes.ENUM("embedding", "completion", "other"),
      allowNull: false,
    },
    modelName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    promptTokens: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    completionTokens: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    totalTokens: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    estimatedCost: {
      type: DataTypes.DECIMAL(10, 6),
      allowNull: false,
    },
    metadata: {
      type: DataTypes.JSONB,
    },
  },
  {
    schema: "public", // Set the schema here
  },
);

export type TokenUsageModel = typeof TokenUsage;
