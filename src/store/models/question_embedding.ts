import { DataTypes, Model } from "sequelize";

import { sequelize } from "@/store/instance";

interface QuestionEmbeddingAttributes {
  id: number;
  clientId: number;
  userId?: number | null;
  question: string;
  answer: string | null;
  embedding?: number[];
  relatedEmbeddings: string;
  questionEmbeddingTokenUsage: number;
  answerTokenUsage: number;
  searchTime: number;
  getQuestionEmbeddingTime: number;
  answeringTime: number;
  sources?: Record<string, any>[]; // Add sources field
}

export const QuestionEmbedding = sequelize.define<
  Model<QuestionEmbeddingAttributes, Partial<QuestionEmbeddingAttributes>>
>(
  "question_embedding",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    question: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    answer: {
      type: DataTypes.TEXT,
    },
    clientId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    // embedding: {
    // we do not manage this table in sequelize, we only manage it by direct SQL queries
    // },
    relatedEmbeddings: {
      type: DataTypes.TEXT,
    },
    questionEmbeddingTokenUsage: {
      type: DataTypes.INTEGER,
    },
    answerTokenUsage: {
      type: DataTypes.INTEGER,
    },
    searchTime: {
      type: DataTypes.INTEGER,
    },
    getQuestionEmbeddingTime: {
      type: DataTypes.INTEGER,
    },
    answeringTime: {
      type: DataTypes.INTEGER,
    },
    sources: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
  },
  {
    schema: "public", // Set the schema here
  },
);

export type QuestionEmbeddingModel = typeof QuestionEmbedding;
