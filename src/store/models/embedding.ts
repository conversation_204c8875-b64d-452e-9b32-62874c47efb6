import { DataTypes, Model } from "sequelize";

import { sequelize } from "@/store/instance";

interface EmbeddingAttributes {
  id: number;

  content: string;
  metadata: Record<string, unknown>;
  fromSiteLinkId?: number;
  siteId?: number; // Add siteId field

  createdAt?: Date;
  updatedAt?: Date;
}

export const Embedding = sequelize.define<Model<EmbeddingAttributes>>(
  "embedding",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    // embedding: {
    // we do not manage this table in sequelize, we only manage it by direct SQL queries
    // },
    metadata: {
      type: DataTypes.JSONB,
    },
    fromSiteLinkId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    siteId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
  },
  {
    schema: "public", // Set the schema here
  },
);

export type EmbeddingModel = typeof Embedding;
