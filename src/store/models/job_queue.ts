import { DataTypes, Model } from "sequelize";

import { sequelize } from "@/store/instance";

export interface JobQueueAttributes {
  id: number;
  jobType: string;
  jobData: Record<string, unknown>;
  status: "pending" | "running" | "completed" | "failed" | "cancelled";
  priority: number;
  
  // Scheduling
  scheduledAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
  
  // Worker tracking
  workerId?: string;
  lastHeartbeat?: Date;
  
  // Error handling
  attempts: number;
  maxAttempts: number;
  lastError?: string;
  
  // Retry logic
  retryDelay: number; // in seconds
  nextRetryAt?: Date;
  
  // Metadata
  metadata?: Record<string, unknown>;
  
  createdAt?: Date;
  updatedAt?: Date;
}

export type JobQueueModel = Model<JobQueueAttributes, Partial<JobQueueAttributes>>;

export const JobQueue = sequelize.define<JobQueueModel>(
  "job_queue",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    jobType: {
      type: DataTypes.STRING(100),
      allowNull: false,
      field: "job_type",
    },
    jobData: {
      type: DataTypes.JSONB,
      allowNull: false,
      field: "job_data",
    },
    status: {
      type: DataTypes.ENUM("pending", "running", "completed", "failed", "cancelled"),
      defaultValue: "pending",
    },
    priority: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    scheduledAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: "scheduled_at",
    },
    startedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: "started_at",
    },
    completedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: "completed_at",
    },
    workerId: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: "worker_id",
    },
    lastHeartbeat: {
      type: DataTypes.DATE,
      allowNull: true,
      field: "last_heartbeat",
    },
    attempts: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    maxAttempts: {
      type: DataTypes.INTEGER,
      defaultValue: 3,
      field: "max_attempts",
    },
    lastError: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: "last_error",
    },
    retryDelay: {
      type: DataTypes.INTEGER,
      defaultValue: 300, // 5 minutes
      field: "retry_delay",
    },
    nextRetryAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: "next_retry_at",
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
  },
  {
    schema: "public",
    tableName: "job_queue",
    underscored: true,
    indexes: [
      {
        fields: ["job_type"],
      },
      {
        fields: ["status"],
      },
      {
        fields: ["priority", "created_at"],
      },
      {
        fields: ["scheduled_at"],
      },
      {
        fields: ["next_retry_at"],
      },
      {
        fields: ["worker_id"],
      },
    ],
  },
);
