import { Site } from "./site";
import { SiteLink } from "./site_link";
import { FileLink } from "./file_link";
import { Embedding } from "./embedding";
import { QuestionEmbedding } from "./question_embedding";
import { EmbeddingSyncStatus } from "./embedding_sync_status";
import { AnswerEmbedding } from "./answer_embedding";
import { ScrapeJob } from "./scrape_job";
import { JobQueue } from "./job_queue";
import { config } from "@/config";
import { logger } from "@/services/logger";

export async function initModels(): Promise<boolean> {
  try {
    // Define associations
    Site.hasMany(SiteLink, {
      foreignKey: "siteId",
      as: "siteLinks",
    });

    SiteLink.belongsTo(Site, {
      foreignKey: "siteId",
      as: "site",
    });

    SiteLink.hasMany(FileLink, {
      foreignKey: "siteLinkId",
      as: "fileLinks",
    });

    FileLink.belongsTo(SiteLink, {
      foreignKey: "siteLinkId",
      as: "siteLink",
    });

    FileLink.belongsTo(Site, {
      foreignKey: "siteId",
      as: "site",
    });

    SiteLink.hasMany(Embedding, {
      foreignKey: "fromSiteLinkId",
      as: "embeddings",
    });

    Embedding.belongsTo(SiteLink, {
      foreignKey: "fromSiteLinkId",
      as: "siteLink",
    });

    SiteLink.addHook("afterCreate", async (instance) => {
      const siteId = instance.getDataValue("siteId");
      if (siteId) {
        await Site.update({ lastScrapedAt: new Date() }, { where: { id: siteId } });
      }
    });

    SiteLink.addHook("afterUpdate", async (instance) => {
      const siteId = instance.getDataValue("siteId");
      if (siteId) {
        await Site.update({ lastScrapedAt: new Date() }, { where: { id: siteId } });
      }
    });

    const modelsToSync = [
      Site,
      SiteLink,
      FileLink,
      Embedding,
      QuestionEmbedding,
      // New models
      EmbeddingSyncStatus,
      AnswerEmbedding,
      // Background job models
      ScrapeJob,
      JobQueue,
    ];

    for (const model of modelsToSync) {
      await model.sync({
        alter: config.nodeEnv === "production" ? false : { drop: false },
      });
    }

    logger.info("Database models synchronized");

    return true;
  } catch (error) {
    logger.error("Error setting up database:", error);
    process.exit(1);
  }
}
