import { DataTypes, Model } from "sequelize";

import { sequelize } from "@/store/instance";

export interface FileLinkAttributes {
  id: number;
  url: string;
  fileType: string;
  siteLinkId: number;
  siteId: number;
  status: "pending" | "processed" | "failed" | "ignored" | "download_failed";
  processedAt?: Date;
  comment?: string;
  s3Key?: string;
  s3Url?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export type FileLinkModel = Model<FileLinkAttributes, Partial<FileLinkAttributes>>;

export const FileLink = sequelize.define<FileLinkModel>(
  "file_links",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    url: {
      type: DataTypes.STRING(2000), // Increase length to 2000 characters
      allowNull: false,
    },
    fileType: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    siteLinkId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: "site_link_id",
    },
    siteId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: "site_id",
    },
    status: {
      type: DataTypes.ENUM("pending", "processed", "failed", "ignored", "download_failed"), // Add download_failed status
      allowNull: false,
      defaultValue: "pending",
    },
    processedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: "processed_at",
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    s3Key: {
      type: DataTypes.STRING,
      allowNull: true,
      field: "s3_key",
    },
    s3Url: {
      type: DataTypes.STRING,
      allowNull: true,
      field: "s3_url",
    },
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
  },
  {
    schema: "public",
    tableName: "file_links",
    underscored: true,
  },
);
