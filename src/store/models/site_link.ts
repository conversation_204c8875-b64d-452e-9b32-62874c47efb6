import { DataTypes, Model } from "sequelize";

import { sequelize } from "@/store/instance";

export interface SiteLinkAttributes {
  id: number;

  url: string;
  siteId: number;
  status: "pending" | "scraped" | "failed" | "ignored";
  lastScrapedAt?: Date | null;
  timeElapsed?: number | null;
  comment?: string;
  linksOnPage?: string[];
  fileLinksOnPage?: string[];
  originalHtml?: string;
  pageContent?: string;
  isPriority?: boolean;

  metadata?: Record<string, unknown>;

  createdAt?: Date;
  updatedAt?: Date;
}

export type SiteLinkModel = Model<SiteLinkAttributes, Partial<SiteLinkAttributes>>;

export const SiteLink = sequelize.define<SiteLinkModel>(
  "site_link",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    url: {
      type: DataTypes.STRING(2000),
      allowNull: false,
      unique: true,
    },
    siteId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM("pending", "scraped", "failed", "ignored"),
      defaultValue: "pending",
    },
    lastScrapedAt: {
      type: DataTypes.DATE,
    },
    timeElapsed: {
      type: DataTypes.FLOAT,
    },
    comment: {
      type: DataTypes.TEXT, // Change from STRING to TEXT to allow unlimited length
    },
    linksOnPage: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    fileLinksOnPage: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    originalHtml: {
      type: DataTypes.TEXT,
    },
    pageContent: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    isPriority: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: "is_priority",
    },
  },
  {
    schema: "public", // Set the schema here
  },
);
