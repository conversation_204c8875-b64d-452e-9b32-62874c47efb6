import { Model, DataTypes } from "sequelize";

import { sequelize } from "@/store/instance";

export interface SiteAttributes {
  id: number;
  url: string;
  name?: string;
  enabled: boolean;
  clientId?: string;
  schema?: string;
  lastScrapedAt?: Date;
  createdAt?: Date;
  updatedAt?: Date;
  requiresJavascript: boolean;
}

export const Site = sequelize.define<Model<SiteAttributes>>(
  "site",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    url: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    clientId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: "client_id",
    },
    lastScrapedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: "last_scraped_at",
    },
    schema: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: "public",
    },
    requiresJavascript: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: "requires_javascript",
    },
  },
  {
    schema: "public",
    tableName: "sites",
    underscored: true,
  },
);

export type SiteModel = Model<SiteAttributes>;
