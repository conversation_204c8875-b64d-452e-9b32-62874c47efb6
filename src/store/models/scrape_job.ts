import { DataTypes, Model } from "sequelize";

import { sequelize } from "@/store/instance";

export interface ScrapeJobAttributes {
  id: number;
  siteId: number;
  siteUrl: string;
  jobType: "sitemap" | "embeddings" | "full_scrape";
  status: "pending" | "running" | "completed" | "failed" | "paused";
  priority: number;
  
  // Pagination state
  currentBatch: number;
  totalBatches?: number;
  batchSize: number;
  processedCount: number;
  totalCount?: number;
  lastProcessedId?: number;
  
  // Job configuration
  config: Record<string, unknown>;
  
  // Progress tracking
  startedAt?: Date;
  completedAt?: Date;
  lastHeartbeat?: Date;
  
  // Error handling
  errorCount: number;
  lastError?: string;
  retryCount: number;
  maxRetries: number;
  nextRetryAt?: Date;
  
  // Metadata
  metadata?: Record<string, unknown>;
  
  createdAt?: Date;
  updatedAt?: Date;
}

export type ScrapeJobModel = Model<ScrapeJobAttributes, Partial<ScrapeJobAttributes>>;

export const ScrapeJob = sequelize.define<ScrapeJobModel>(
  "scrape_job",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    siteId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: "site_id",
    },
    siteUrl: {
      type: DataTypes.STRING(2000),
      allowNull: false,
      field: "site_url",
    },
    jobType: {
      type: DataTypes.ENUM("sitemap", "embeddings", "full_scrape"),
      allowNull: false,
      field: "job_type",
    },
    status: {
      type: DataTypes.ENUM("pending", "running", "completed", "failed", "paused"),
      defaultValue: "pending",
    },
    priority: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    currentBatch: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: "current_batch",
    },
    totalBatches: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: "total_batches",
    },
    batchSize: {
      type: DataTypes.INTEGER,
      defaultValue: 100,
      field: "batch_size",
    },
    processedCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: "processed_count",
    },
    totalCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: "total_count",
    },
    lastProcessedId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: "last_processed_id",
    },
    config: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    startedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: "started_at",
    },
    completedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: "completed_at",
    },
    lastHeartbeat: {
      type: DataTypes.DATE,
      allowNull: true,
      field: "last_heartbeat",
    },
    errorCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: "error_count",
    },
    lastError: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: "last_error",
    },
    retryCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: "retry_count",
    },
    maxRetries: {
      type: DataTypes.INTEGER,
      defaultValue: 3,
      field: "max_retries",
    },
    nextRetryAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: "next_retry_at",
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
  },
  {
    schema: "public",
    tableName: "scrape_jobs",
    underscored: true,
    indexes: [
      {
        fields: ["site_id"],
      },
      {
        fields: ["status"],
      },
      {
        fields: ["job_type"],
      },
      {
        fields: ["priority", "created_at"],
      },
      {
        fields: ["next_retry_at"],
      },
    ],
  },
);
