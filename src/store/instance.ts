import { readFileSync } from "fs";
import { join } from "path";

import { Sequelize } from "sequelize";

import { logger } from "@/services/logger";
import { config } from "@/config";

let isInitialized = false;

export const sequelize = new Sequelize(config.databaseUrl, {
  dialect: "postgres",

  dialectOptions: {
    ssl:
      config.nodeEnv === "production"
        ? {
            rejectUnauthorized: false,
          }
        : false,
  },

  // Replace the current logging with a conditional log
  logging: config.logSQL ? (msg) => logger.debug(msg) : false,

  define: {
    timestamps: true,
    underscored: true,
  },

  sync: {
    alter: config.nodeEnv === "production" ? false : { drop: false },
  },
});

const runInitSql = async () => {
  try {
    const sql = readFileSync(join(__dirname, "../../docker/init.sql"), "utf8");
    const statements = sql
      .split(";")
      .map((s) => s.trim())
      .filter((s) => s.length > 0);

    for (const statement of statements) {
      try {
        await sequelize.query(statement);
      } catch (error) {
        logger.warn(`Error executing SQL statement: ${statement}`, error);
      }
    }
  } catch (error) {
    logger.warn("Error running init.sql:", error);
  }
};

export const initDb = async () => {
  if (isInitialized) {
    return;
  }

  try {
    await sequelize.authenticate();
    logger.info("Database connection established");

    await runInitSql();

    const { initModels } = await import("@/store/models/init");
    await initModels();

    isInitialized = true;
  } catch (error) {
    logger.error("Unable to connect to the database:", error);
    throw error;
  }
};
