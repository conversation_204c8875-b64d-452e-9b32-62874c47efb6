// import 'module-alias/register';
import express from "express";
import cors from "cors";

// eslint-disable-next-line import/order
import { config } from "./config";

import { questionRouter } from "./routes/question";

const port = config.port || 3001;

const app = express();

process.on("uncaughtException", (error) => {
  console.error("Uncaught Exception:", error);
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection at:", promise, "reason:", reason);
});

app.use(cors({
  origin: true, // Allow all origins for development
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept']
}));
app.use(express.json());
app.use("/marvin", questionRouter);

// Add immediate console output
console.log(`Attempting to start server on port ${port}`);

app.listen(port, () => {
  console.log(`The API has started. Listening on port ${port}`);
});

export { app };
