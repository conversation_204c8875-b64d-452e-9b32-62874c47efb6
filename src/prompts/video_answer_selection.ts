export const videoAnswerSelectionPrompt = (question: string, candidatesText: string): string => {
  return `You are helping to select the most relevant video answer for a user's question.

User's Question: "${question}"

Available Video Answers:
${candidatesText}

Please select which video answer is most relevant to the user's question. Consider:
1. How well the title matches the question
2. How well the transcript content addresses the question
3. Whether the answer actually provides useful information for the question

Respond with ONLY the Answer ID number (e.g., just the number like "12345") of the most relevant answer, or respond with "NONE" if none of the answers are truly relevant to the question.

Do not provide any explanation, just the Answer ID number or "NONE".`;
};
