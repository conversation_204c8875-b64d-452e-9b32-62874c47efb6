import { logger } from "@/services/logger";
import { JobProcessor } from "./job-processor";
import { ScrapingJobHandler } from "./scraping-job-handler";
import { JobScheduler } from "./job-scheduler";
import { initDb } from "@/store/instance";

export class BackgroundJobService {
  private jobProcessor: JobProcessor;
  private jobScheduler: JobScheduler;
  private isInitialized = false;

  constructor() {
    this.jobProcessor = new JobProcessor({
      concurrency: 2, // Process 2 jobs concurrently
      pollInterval: 10000, // Poll every 10 seconds
      heartbeatInterval: 30000, // Heartbeat every 30 seconds
      jobTimeout: 600000, // 10 minute timeout per job
    });
    this.jobScheduler = new JobScheduler();
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    logger.info("Initializing background job service...");

    // Initialize database
    await initDb();

    // Register job handlers
    this.jobProcessor.registerHandler("scraping", new ScrapingJobHandler());

    this.isInitialized = true;
    logger.info("Background job service initialized");
  }

  async start(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    logger.info("Starting background job service...");
    await this.jobProcessor.start();
  }

  async stop(): Promise<void> {
    logger.info("Stopping background job service...");
    await this.jobProcessor.stop();
  }

  getScheduler(): JobScheduler {
    return this.jobScheduler;
  }

  /**
   * Schedule automated scraping for all enabled sites
   */
  async scheduleAutomatedScraping(): Promise<void> {
    logger.info("Scheduling automated scraping for all enabled sites...");

    try {
      // Schedule sitemap jobs first
      const sitemapJobs = await this.jobScheduler.scheduleJobsForAllSites("sitemap", {
        priority: 1,
        batchSize: 100,
      });

      // Schedule embeddings jobs with delay
      const embeddingsJobs = await this.jobScheduler.scheduleJobsForAllSites("embeddings", {
        priority: 0,
        batchSize: 100,
        scheduledAt: new Date(Date.now() + 300000), // 5 minutes delay
      });

      logger.info(`Scheduled ${sitemapJobs.length} sitemap jobs and ${embeddingsJobs.length} embeddings jobs`);

    } catch (error) {
      logger.error("Failed to schedule automated scraping:", error);
      throw error;
    }
  }

  /**
   * Get status of all active jobs
   */
  async getActiveJobsStatus(): Promise<any[]> {
    const { ScrapeJob } = await import("@/store/models/scrape_job");
    
    const activeJobs = await ScrapeJob.findAll({
      where: {
        status: ["pending", "running"],
      },
      order: [["createdAt", "DESC"]],
    });

    const jobStatuses = await Promise.all(
      activeJobs.map(async (job) => {
        const jobId = job.getDataValue("id");
        return await this.jobScheduler.getJobStatus(jobId);
      })
    );

    return jobStatuses;
  }

  /**
   * Clean up old completed jobs
   */
  async cleanupOldJobs(olderThanDays: number = 7): Promise<void> {
    const { ScrapeJob } = await import("@/store/models/scrape_job");
    const { JobQueue } = await import("@/store/models/job_queue");

    const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);

    // Clean up completed scrape jobs
    const deletedScrapeJobs = await ScrapeJob.destroy({
      where: {
        status: ["completed", "failed"],
        updatedAt: { [require("sequelize").Op.lt]: cutoffDate },
      },
    });

    // Clean up completed queue jobs
    const deletedQueueJobs = await JobQueue.destroy({
      where: {
        status: ["completed", "failed", "cancelled"],
        updatedAt: { [require("sequelize").Op.lt]: cutoffDate },
      },
    });

    logger.info(`Cleaned up ${deletedScrapeJobs} scrape jobs and ${deletedQueueJobs} queue jobs older than ${olderThanDays} days`);
  }

  /**
   * Health check for the job service
   */
  async healthCheck(): Promise<{
    status: string;
    activeJobs: number;
    pendingJobs: number;
    failedJobs: number;
  }> {
    const { ScrapeJob } = await import("@/store/models/scrape_job");
    const { JobQueue } = await import("@/store/models/job_queue");

    const [activeJobs, pendingJobs, failedJobs] = await Promise.all([
      ScrapeJob.count({ where: { status: "running" } }),
      JobQueue.count({ where: { status: "pending" } }),
      ScrapeJob.count({ where: { status: "failed" } }),
    ]);

    return {
      status: this.isInitialized ? "healthy" : "not_initialized",
      activeJobs,
      pendingJobs,
      failedJobs,
    };
  }
}

// Singleton instance
export const backgroundJobService = new BackgroundJobService();
