import { Op } from "sequelize";
import { logger } from "@/services/logger";
import { ScrapeJob } from "@/store/models/scrape_job";
import { JobQueue } from "@/store/models/job_queue";
import { Site } from "@/store/models/site";
import { SiteLink } from "@/store/models/site_link";

export interface ScheduleJobOptions {
  priority?: number;
  scheduledAt?: Date;
  batchSize?: number;
  config?: Record<string, unknown>;
}

export class JobScheduler {
  /**
   * Schedule a sitemap parsing job for a site
   */
  async scheduleSitemapJob(siteUrl: string, options: ScheduleJobOptions = {}): Promise<number> {
    const site = await Site.findOne({ where: { url: siteUrl } });
    if (!site) {
      throw new Error(`Site not found: ${siteUrl}`);
    }

    const siteId = site.getDataValue("id");

    // Create scrape job record
    const scrapeJob = await ScrapeJob.create({
      siteId: siteId,
      siteUrl: siteUrl,
      jobType: "sitemap",
      status: "pending",
      priority: options.priority || 0,
      batchSize: options.batchSize || 100,
      currentBatch: 0,
      processedCount: 0,
      errorCount: 0,
      retryCount: 0,
      maxRetries: 3,
      config: options.config || { crawlInternalLinks: true },
    });

    // Schedule the job in the queue
    await JobQueue.create({
      jobType: "scraping",
      jobData: {
        scrapeJobId: scrapeJob.getDataValue("id"),
        action: "sitemap",
      },
      priority: options.priority || 0,
      scheduledAt: options.scheduledAt || new Date(),
    });

    logger.info(`Scheduled sitemap job for site: ${siteUrl} (Job ID: ${scrapeJob.getDataValue("id")})`);
    return scrapeJob.getDataValue("id");
  }

  /**
   * Schedule an embeddings job for a site
   */
  async scheduleEmbeddingsJob(siteUrl: string, options: ScheduleJobOptions = {}): Promise<number> {
    const site = await Site.findOne({ where: { url: siteUrl } });
    if (!site) {
      throw new Error(`Site not found: ${siteUrl}`);
    }

    const siteId = site.getDataValue("id");

    // Count total pending links
    const totalCount = await SiteLink.count({
      where: {
        siteId: siteId,
        status: "pending",
      },
    });

    if (totalCount === 0) {
      logger.info(`No pending links found for site: ${siteUrl}`);
      return 0;
    }

    const batchSize = options.batchSize || 100;
    const totalBatches = Math.ceil(totalCount / batchSize);

    // Create scrape job record
    const scrapeJob = await ScrapeJob.create({
      siteId: siteId,
      siteUrl: siteUrl,
      jobType: "embeddings",
      status: "pending",
      priority: options.priority || 0,
      batchSize: batchSize,
      currentBatch: 0,
      totalBatches: totalBatches,
      processedCount: 0,
      totalCount: totalCount,
      errorCount: 0,
      retryCount: 0,
      maxRetries: 3,
      config: options.config || {
        crawlInternalLinks: true,
        maxDepth: 5,
        includeScraped: false,
      },
    });

    // Schedule the first batch
    await JobQueue.create({
      jobType: "scraping",
      jobData: {
        scrapeJobId: scrapeJob.getDataValue("id"),
        action: "embeddings",
      },
      priority: options.priority || 0,
      scheduledAt: options.scheduledAt || new Date(),
    });

    logger.info(`Scheduled embeddings job for site: ${siteUrl} (Job ID: ${scrapeJob.getDataValue("id")}, ${totalBatches} batches)`);
    return scrapeJob.getDataValue("id");
  }

  /**
   * Schedule a full scrape job (sitemap + embeddings)
   */
  async scheduleFullScrapeJob(siteUrl: string, options: ScheduleJobOptions = {}): Promise<number> {
    const site = await Site.findOne({ where: { url: siteUrl } });
    if (!site) {
      throw new Error(`Site not found: ${siteUrl}`);
    }

    const siteId = site.getDataValue("id");

    // Create scrape job record
    const scrapeJob = await ScrapeJob.create({
      siteId: siteId,
      siteUrl: siteUrl,
      jobType: "full_scrape",
      status: "pending",
      priority: options.priority || 0,
      batchSize: options.batchSize || 100,
      currentBatch: 0,
      processedCount: 0,
      errorCount: 0,
      retryCount: 0,
      maxRetries: 3,
      config: options.config || {
        crawlInternalLinks: true,
        maxDepth: 5,
        includeScraped: false,
      },
      metadata: {
        sitemapCompleted: false,
      },
    });

    // Schedule the job
    await JobQueue.create({
      jobType: "scraping",
      jobData: {
        scrapeJobId: scrapeJob.getDataValue("id"),
        action: "full_scrape",
      },
      priority: options.priority || 0,
      scheduledAt: options.scheduledAt || new Date(),
    });

    logger.info(`Scheduled full scrape job for site: ${siteUrl} (Job ID: ${scrapeJob.getDataValue("id")})`);
    return scrapeJob.getDataValue("id");
  }

  /**
   * Schedule jobs for all enabled sites
   */
  async scheduleJobsForAllSites(jobType: "sitemap" | "embeddings" | "full_scrape", options: ScheduleJobOptions = {}): Promise<number[]> {
    const sites = await Site.findAll({
      where: { enabled: true },
    });

    const jobIds: number[] = [];
    let delay = 0;

    for (const site of sites) {
      const siteUrl = site.getDataValue("url");
      const scheduledAt = new Date(Date.now() + delay);

      try {
        let jobId: number;
        switch (jobType) {
          case "sitemap":
            jobId = await this.scheduleSitemapJob(siteUrl, { ...options, scheduledAt });
            break;
          case "embeddings":
            jobId = await this.scheduleEmbeddingsJob(siteUrl, { ...options, scheduledAt });
            break;
          case "full_scrape":
            jobId = await this.scheduleFullScrapeJob(siteUrl, { ...options, scheduledAt });
            break;
        }
        
        if (jobId > 0) {
          jobIds.push(jobId);
        }
      } catch (error) {
        logger.error(`Failed to schedule ${jobType} job for site ${siteUrl}:`, error);
      }

      // Add 30 second delay between sites to avoid overwhelming the system
      delay += 30000;
    }

    logger.info(`Scheduled ${jobIds.length} ${jobType} jobs for enabled sites`);
    return jobIds;
  }

  /**
   * Get job status and progress
   */
  async getJobStatus(jobId: number): Promise<any> {
    const scrapeJob = await ScrapeJob.findByPk(jobId);
    if (!scrapeJob) {
      throw new Error(`Job ${jobId} not found`);
    }

    const jobData = scrapeJob.toJSON();
    
    // Calculate progress percentage
    let progressPercentage = 0;
    if (jobData.totalCount && jobData.totalCount > 0) {
      progressPercentage = Math.round((jobData.processedCount / jobData.totalCount) * 100);
    } else if (jobData.totalBatches && jobData.totalBatches > 0) {
      progressPercentage = Math.round((jobData.currentBatch / jobData.totalBatches) * 100);
    }

    return {
      ...jobData,
      progressPercentage,
    };
  }

  /**
   * Cancel a job
   */
  async cancelJob(jobId: number): Promise<void> {
    await ScrapeJob.update(
      { status: "failed" },
      { where: { id: jobId } }
    );

    // Cancel any pending queue items for this job
    await JobQueue.update(
      { status: "cancelled" },
      {
        where: {
          jobData: {
            scrapeJobId: jobId,
          },
          status: "pending",
        },
      }
    );

    logger.info(`Cancelled job ${jobId}`);
  }
}
