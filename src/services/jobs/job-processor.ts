import { Op } from "sequelize";
import { logger } from "@/services/logger";
import { JobQueue, JobQueueAttributes } from "@/store/models/job_queue";
import { delay } from "@/utils/async-utils";

export interface JobHandler {
  handle(jobData: Record<string, unknown>): Promise<void>;
}

export interface JobProcessorOptions {
  concurrency?: number;
  pollInterval?: number; // in milliseconds
  heartbeatInterval?: number; // in milliseconds
  jobTimeout?: number; // in milliseconds
  workerId?: string;
}

export class JobProcessor {
  private handlers: Map<string, JobHandler> = new Map();
  private isRunning = false;
  private activeJobs = new Set<number>();
  private options: Required<JobProcessorOptions>;
  private heartbeatTimer?: NodeJS.Timeout;

  constructor(options: JobProcessorOptions = {}) {
    this.options = {
      concurrency: options.concurrency || 3,
      pollInterval: options.pollInterval || 5000,
      heartbeatInterval: options.heartbeatInterval || 30000,
      jobTimeout: options.jobTimeout || 300000, // 5 minutes
      workerId: options.workerId || `worker-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  registerHandler(jobType: string, handler: JobHandler): void {
    this.handlers.set(jobType, handler);
    logger.info(`Registered handler for job type: ${jobType}`);
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn("Job processor is already running");
      return;
    }

    this.isRunning = true;
    logger.info(`Starting job processor with worker ID: ${this.options.workerId}`);

    // Start heartbeat timer
    this.startHeartbeat();

    // Start processing loop
    this.processJobs().catch((error) => {
      logger.error("Job processing loop failed:", error);
      this.stop();
    });
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    logger.info("Stopping job processor...");
    this.isRunning = false;

    // Stop heartbeat
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
    }

    // Wait for active jobs to complete
    while (this.activeJobs.size > 0) {
      logger.info(`Waiting for ${this.activeJobs.size} active jobs to complete...`);
      await delay(1000);
    }

    logger.info("Job processor stopped");
  }

  private async processJobs(): Promise<void> {
    while (this.isRunning) {
      try {
        // Check if we can process more jobs
        if (this.activeJobs.size >= this.options.concurrency) {
          await delay(this.options.pollInterval);
          continue;
        }

        // Get next job
        const job = await this.getNextJob();
        if (!job) {
          await delay(this.options.pollInterval);
          continue;
        }

        // Process job asynchronously
        this.processJob(job).catch((error) => {
          logger.error(`Error processing job ${job.id}:`, error);
        });

      } catch (error) {
        logger.error("Error in job processing loop:", error);
        await delay(this.options.pollInterval);
      }
    }
  }

  private async getNextJob(): Promise<JobQueueAttributes | null> {
    const now = new Date();

    const job = await JobQueue.findOne({
      where: {
        status: "pending",
        [Op.or]: [
          { scheduledAt: { [Op.lte]: now } },
          { scheduledAt: { [Op.is]: null } },
        ],
        [Op.or]: [
          { nextRetryAt: { [Op.lte]: now } },
          { nextRetryAt: { [Op.is]: null } },
        ],
      },
      order: [
        ["priority", "DESC"],
        ["createdAt", "ASC"],
      ],
    });

    if (!job) {
      return null;
    }

    // Mark job as running
    await job.update({
      status: "running",
      startedAt: now,
      workerId: this.options.workerId,
      lastHeartbeat: now,
    });

    return job.toJSON() as JobQueueAttributes;
  }

  private async processJob(jobData: JobQueueAttributes): Promise<void> {
    const jobId = jobData.id;
    this.activeJobs.add(jobId);

    try {
      logger.info(`Processing job ${jobId} of type ${jobData.jobType}`);

      const handler = this.handlers.get(jobData.jobType);
      if (!handler) {
        throw new Error(`No handler registered for job type: ${jobData.jobType}`);
      }

      // Set up job timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Job ${jobId} timed out after ${this.options.jobTimeout}ms`));
        }, this.options.jobTimeout);
      });

      // Process the job
      await Promise.race([
        handler.handle(jobData.jobData),
        timeoutPromise,
      ]);

      // Mark job as completed
      await JobQueue.update(
        {
          status: "completed",
          completedAt: new Date(),
        },
        {
          where: { id: jobId },
        }
      );

      logger.info(`Job ${jobId} completed successfully`);

    } catch (error) {
      logger.error(`Job ${jobId} failed:`, error);
      await this.handleJobFailure(jobId, error as Error);
    } finally {
      this.activeJobs.delete(jobId);
    }
  }

  private async handleJobFailure(jobId: number, error: Error): Promise<void> {
    const job = await JobQueue.findByPk(jobId);
    if (!job) {
      logger.error(`Job ${jobId} not found when handling failure`);
      return;
    }

    const attempts = job.getDataValue("attempts") + 1;
    const maxAttempts = job.getDataValue("maxAttempts");
    const retryDelay = job.getDataValue("retryDelay");

    if (attempts >= maxAttempts) {
      // Mark as failed permanently
      await job.update({
        status: "failed",
        attempts,
        lastError: error.message,
        completedAt: new Date(),
      });
      logger.error(`Job ${jobId} failed permanently after ${attempts} attempts`);
    } else {
      // Schedule for retry
      const nextRetryAt = new Date(Date.now() + retryDelay * 1000);
      await job.update({
        status: "pending",
        attempts,
        lastError: error.message,
        nextRetryAt,
        workerId: null,
        startedAt: null,
        lastHeartbeat: null,
      });
      logger.info(`Job ${jobId} scheduled for retry at ${nextRetryAt} (attempt ${attempts}/${maxAttempts})`);
    }
  }

  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(async () => {
      if (this.activeJobs.size > 0) {
        try {
          await JobQueue.update(
            { lastHeartbeat: new Date() },
            {
              where: {
                workerId: this.options.workerId,
                status: "running",
              },
            }
          );
        } catch (error) {
          logger.error("Failed to update heartbeat:", error);
        }
      }
    }, this.options.heartbeatInterval);
  }
}
