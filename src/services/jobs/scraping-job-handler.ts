import { Op } from "sequelize";

import { logger } from "@/services/logger";
import { JobHandler } from "./job-processor";
import { ScrapeJob, ScrapeJobAttributes } from "@/store/models/scrape_job";
import { Site } from "@/store/models/site";
import { SiteLink } from "@/store/models/site_link";
import { getSitemapUrls } from "@/services/scraper";
import { getVectorStore } from "@/services/embeddings/create_embeddings";
import { documentLoader } from "@/services/embeddings/document_loaders";
import { loadJavaScriptHeavySite } from "@/services/embeddings/browser-loader";
import { processNewlyDiscoveredLinks } from "@/services/embeddings/link-processor";
import { processFileLinksForSite } from "@/services/embeddings/file-processor";
import { createChunks } from "@/services/embeddings/document-processor";
import { associateEmbeddingsWithSiteLink } from "@/services/embeddings/database-operations";
import { delay } from "@/utils/async-utils";

export interface ScrapingJobData {
  scrapeJobId: number;
  action: "sitemap" | "embeddings" | "full_scrape";
}

export class ScrapingJobHandler implements JobHandler {
  async handle(jobData: Record<string, unknown>): Promise<void> {
    const { scrapeJobId, action } = jobData as ScrapingJobData;

    logger.info(`Processing scraping job ${scrapeJobId} with action: ${action}`);

    const scrapeJob = await ScrapeJob.findByPk(scrapeJobId);
    if (!scrapeJob) {
      throw new Error(`Scrape job ${scrapeJobId} not found`);
    }

    // Update job status to running
    await scrapeJob.update({
      status: "running",
      startedAt: new Date(),
      lastHeartbeat: new Date(),
    });

    try {
      switch (action) {
        case "sitemap":
          await this.processSitemapJob(scrapeJob);
          break;
        case "embeddings":
          await this.processEmbeddingsJob(scrapeJob);
          break;
        case "full_scrape":
          await this.processFullScrapeJob(scrapeJob);
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }

      // Mark job as completed
      await scrapeJob.update({
        status: "completed",
        completedAt: new Date(),
      });

      logger.info(`Scraping job ${scrapeJobId} completed successfully`);
    } catch (error) {
      logger.error(`Scraping job ${scrapeJobId} failed:`, error);

      // Update error information
      await scrapeJob.update({
        status: "failed",
        errorCount: scrapeJob.getDataValue("errorCount") + 1,
        lastError: (error as Error).message,
      });

      throw error;
    }
  }

  private async processSitemapJob(scrapeJob: ScrapeJobAttributes): Promise<void> {
    const siteUrl = scrapeJob.siteUrl;
    const config = scrapeJob.config as { crawlInternalLinks?: boolean };

    logger.info(`Processing sitemap for site: ${siteUrl}`);

    // Parse sitemap and discover URLs
    await getSitemapUrls(siteUrl, config.crawlInternalLinks || true);

    logger.info(`Sitemap processing completed for site: ${siteUrl}`);
  }

  private async processEmbeddingsJob(scrapeJob: ScrapeJobAttributes): Promise<void> {
    const siteId = scrapeJob.siteId;
    const batchSize = scrapeJob.batchSize;
    const currentBatch = scrapeJob.currentBatch;
    const config = scrapeJob.config as {
      crawlInternalLinks?: boolean;
      maxDepth?: number;
      includeScraped?: boolean;
    };

    logger.info(`Processing embeddings batch ${currentBatch} for site ID: ${siteId}`);

    // Get pending links for this batch
    const offset = currentBatch * batchSize;
    const pendingLinks = await SiteLink.findAll({
      where: {
        siteId: siteId,
        status: config.includeScraped ? { [Op.in]: ["pending", "scraped"] } : "pending",
      },
      order: [
        ["status", "ASC"],
        ["lastScrapedAt", "ASC"],
        ["id", "ASC"],
      ],
      limit: batchSize,
      offset: offset,
    });

    if (pendingLinks.length === 0) {
      logger.info(`No more links to process for site ID: ${siteId}`);
      return;
    }

    logger.info(`Processing ${pendingLinks.length} links in batch ${currentBatch}`);

    // Process embeddings for this batch using our custom function
    await this.processEmbeddingsBatch(pendingLinks, config);

    // Update job progress
    const processedCount = scrapeJob.processedCount + pendingLinks.length;
    const lastProcessedId = pendingLinks[pendingLinks.length - 1].getDataValue("id");

    await ScrapeJob.update(
      {
        currentBatch: currentBatch + 1,
        processedCount: processedCount,
        lastProcessedId: lastProcessedId,
        lastHeartbeat: new Date(),
      },
      {
        where: { id: scrapeJob.id },
      },
    );

    // Check if there are more links to process
    const remainingLinks = await SiteLink.count({
      where: {
        siteId: siteId,
        status: config.includeScraped ? { [Op.in]: ["pending", "scraped"] } : "pending",
        id: { [Op.gt]: lastProcessedId },
      },
    });

    if (remainingLinks > 0) {
      // Schedule next batch
      await this.scheduleNextBatch(scrapeJob.id, "embeddings");
      logger.info(`Scheduled next batch for scrape job ${scrapeJob.id}. Remaining links: ${remainingLinks}`);
    } else {
      logger.info(`All embeddings processed for site ID: ${siteId}`);
    }
  }

  private async processFullScrapeJob(scrapeJob: ScrapeJobAttributes): Promise<void> {
    const siteUrl = scrapeJob.siteUrl;
    const siteId = scrapeJob.siteId;

    logger.info(`Processing full scrape for site: ${siteUrl}`);

    // Step 1: Process sitemap if not already done
    const sitemapCompleted = scrapeJob.metadata?.sitemapCompleted as boolean;
    if (!sitemapCompleted) {
      await this.processSitemapJob(scrapeJob);

      // Update metadata to mark sitemap as completed
      await ScrapeJob.update(
        {
          metadata: { ...scrapeJob.metadata, sitemapCompleted: true },
          lastHeartbeat: new Date(),
        },
        {
          where: { id: scrapeJob.id },
        },
      );
    }

    // Step 2: Process embeddings in batches
    await this.processEmbeddingsJob(scrapeJob);
  }

  private async scheduleNextBatch(scrapeJobId: number, action: string): Promise<void> {
    const { JobQueue } = await import("@/store/models/job_queue");

    // Schedule the next batch with a small delay to avoid overwhelming the system
    const scheduledAt = new Date(Date.now() + 5000); // 5 seconds delay

    await JobQueue.create({
      jobType: "scraping",
      jobData: {
        scrapeJobId: scrapeJobId,
        action: action,
      },
      priority: 1,
      scheduledAt: scheduledAt,
    });
  }

  // Helper method to update job heartbeat
  private async updateHeartbeat(scrapeJobId: number): Promise<void> {
    await ScrapeJob.update({ lastHeartbeat: new Date() }, { where: { id: scrapeJobId } });
  }

  // Process embeddings for a batch of site links
  private async processEmbeddingsBatch(
    pendingLinks: any[],
    config: {
      crawlInternalLinks?: boolean;
      maxDepth?: number;
      includeScraped?: boolean;
    },
  ): Promise<void> {
    const vectorStore = await getVectorStore();
    const BATCH_SIZE = 5; // Process 5 links at a time to avoid overwhelming the system

    for (let i = 0; i < pendingLinks.length; i += BATCH_SIZE) {
      const batch = pendingLinks.slice(i, i + BATCH_SIZE);

      await Promise.all(
        batch.map(async (link) => {
          try {
            await this.processIndividualLink(link, vectorStore, config);
          } catch (error) {
            logger.error(`Error processing link ${link.getDataValue("url")}:`, error);
            // Mark link as failed
            await link.update({
              status: "failed",
              lastScrapedAt: new Date(),
              comment: (error as Error).message,
            });
          }
        }),
      );

      // Small delay between batches
      if (i + BATCH_SIZE < pendingLinks.length) {
        await delay(2000);
      }
    }
  }

  // Process an individual site link
  private async processIndividualLink(
    link: any,
    vectorStore: any,
    config: {
      crawlInternalLinks?: boolean;
      maxDepth?: number;
      includeScraped?: boolean;
    },
  ): Promise<void> {
    const url = link.getDataValue("url");
    const siteLinkId = link.getDataValue("id");
    const siteId = link.getDataValue("siteId");

    logger.info(`Processing link: ${url}`);

    // Check if already processed and not forcing refresh
    if (link.getDataValue("status") === "scraped" && !config.includeScraped) {
      logger.debug(`Skipping already processed link: ${url}`);
      return;
    }

    try {
      // Get the site to check if it requires JavaScript
      const site = await Site.findByPk(siteId);
      const requiresJavaScript = site?.getDataValue("requiresJavascript") || false;

      // Load document
      let document;
      try {
        if (requiresJavaScript) {
          document = await loadJavaScriptHeavySite(url);
        } else {
          document = await documentLoader(url);
        }
      } catch (error) {
        // If standard loader fails, try with Puppeteer
        if (!requiresJavaScript) {
          logger.warn(`Standard loader failed for ${url}, trying with Puppeteer`);
          document = await loadJavaScriptHeavySite(url);

          // Mark site as requiring JavaScript
          if (document && site) {
            await site.update({ requiresJavascript: true });
          }
        } else {
          throw error;
        }
      }

      if (!document) {
        throw new Error("Failed to create document");
      }

      // Process document and create embeddings
      const { pageContent, metadata, originalHtml } = document;
      const linksOnPage = document.linksOnPage || [];
      const fileLinksOnPage = document.fileLinksOnPage || [];

      // Create chunks and add to vector store
      const chunks = await createChunks(pageContent, metadata);
      await vectorStore.addDocuments(chunks);

      // Associate embeddings with site link
      await associateEmbeddingsWithSiteLink(url, siteLinkId, siteId);

      // Update link status
      await link.update({
        status: "scraped",
        lastScrapedAt: new Date(),
        linksOnPage: linksOnPage,
        fileLinksOnPage: fileLinksOnPage,
        originalHtml: originalHtml,
        pageContent: pageContent,
        metadata: metadata,
      });

      // Process newly discovered links if crawling is enabled
      if (config.crawlInternalLinks) {
        await processNewlyDiscoveredLinks(link, site?.getDataValue("url") || "");
      }

      logger.info(`Successfully processed link: ${url}`);
    } catch (error) {
      logger.error(`Failed to process link ${url}:`, error);
      throw error;
    }
  }
}
