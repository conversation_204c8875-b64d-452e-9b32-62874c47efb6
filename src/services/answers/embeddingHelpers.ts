import OpenAI from "openai";
import { logger } from "@/services/logger";
import { config } from "@/config";

/**
 * Gets question embedding from OpenAI
 */
export const getQuestionEmbedding = async (question: string, clientId: number) => {
  const openai = new OpenAI();

  const transformedQuestion = await transformQuestionForVectorSearch(question, clientId);

  logger.info(`Original question: "${question}"`);
  logger.info(`Transformed question: "${transformedQuestion}"`);

  const response = await openai.embeddings.create({
    model: config.embeddingModel,
    input: transformedQuestion,
  });
  return { embedding: response.data[0].embedding, tokenUsage: response.usage.total_tokens || 0 };
};

/**
 * Transforms a user question to optimize for vector search
 * Uses OpenAI to create a more searchable version of the question
 */
export const transformQuestionForVectorSearch = async (question: string, clientId: number): Promise<string> => {
  const words = question.split(/\s+/);
  // Add your transformation logic here
  return question; // Simplified for now
};