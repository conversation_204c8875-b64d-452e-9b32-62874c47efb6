/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import * as xml2js from "xml2js";
import { z } from "zod";
import puppeteer from "puppeteer";

import { logger, trackProgress } from "@/services/logger";
import { initDb } from "@/store/instance";
import { SiteLink } from "@/store/models/site_link";
import { Site } from "@/store/models/site";
import { fetchWithUserHeaders, getRandomUserAgent } from "@/utils/helpers";
import { delay } from "@/utils/async-utils";

const SitemapSchema = z.object({
  urlset: z.object({
    url: z
      .array(
        z.object({
          loc: z.string(),
          lastmod: z.string().optional(),
        }),
      )
      .optional(),
  }),
});

interface UrlEntry {
  loc: string;
  lastmod?: string;
}

const parseSitemapContent = async (xml: string, siteUrl: string) => {
  try {
    logger.info(`Parsing sitemap XML for ${siteUrl}`);

    const parser = new xml2js.Parser({
      explicitArray: false,
      ignoreAttrs: true,
      mergeAttrs: true,
      normalize: true,
      trim: true,
      valueProcessors: [(value) => value.trim()],
    });

    const rawResult = (await parser.parseStringPromise(xml)) as unknown;

    const urls: UrlEntry[] = [];

    try {
      const result = SitemapSchema.parse(rawResult);
      if (result.urlset.url) {
        urls.push(...result.urlset.url);
      }
    } catch (error) {
      logger.error("Failed to validate parsed XML against schema:", error);
      throw new Error("Invalid sitemap format");
    }

    return { urls };
  } catch (error) {
    logger.error("XML parsing error details:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      siteUrl,
    });
    throw error;
  }
};

// Add a function to process a single site
const processSite = async (siteUrl: string, crawlInternalLinks = true): Promise<void> => {
  logger.info(`Processing site: ${siteUrl}`);

  try {
    await initDb();

    const [site] = await Site.findOrCreate({
      where: { url: siteUrl },
      defaults: {
        url: siteUrl,
        enabled: true,
        requiresJavascript: false,
      },
    });

    // Check if site is enabled
    if (!site.getDataValue("enabled")) {
      logger.info(`Site ${siteUrl} is disabled. Skipping sitemap processing.`);
      return;
    }

    const siteId = site.getDataValue("id");

    const requiresJs = site.getDataValue("requiresJavascript");

    // Use different approach based on JavaScript requirements
    if (requiresJs) {
      logger.info(`Site ${siteUrl} requires JavaScript. Using Puppeteer for sitemap processing.`);
      const browser = await puppeteer.default.launch({
        headless: "new",
        args: ["--no-sandbox", "--disable-setuid-sandbox"],
      });

      try {
        const page = await browser.newPage();

        // Set user agent to avoid bot detection
        await page.setUserAgent(getRandomUserAgent());

        // Navigate to the sitemap URL
        await page.goto(`${siteUrl}/sitemap.xml`, {
          waitUntil: "networkidle2",
          timeout: 30000,
        });

        // Get the page content
        const content = await page.content();

        // Check if we got HTML instead of XML
        if (content.includes("<!doctype html>") || content.includes("<html")) {
          logger.info("Received HTML instead of XML, attempting to extract links from homepage");

          // Navigate to homepage to extract links
          await page.goto(siteUrl, {
            waitUntil: "networkidle2",
            timeout: 30000,
          });

          // Extract all links from the page
          const links = await page.evaluate(() => {
            return Array.from(document.querySelectorAll("a[href]"))
              .map((a) => a.href)
              .filter((href) => href && !href.startsWith("#") && !href.startsWith("javascript:"));
          });

          const internalLinks = links.filter(
            (link) => link.startsWith(siteUrl) || new URL(link).hostname === new URL(siteUrl).hostname,
          );

          logger.info(`Found ${internalLinks.length} internal links via Puppeteer`);

          const endProgress = trackProgress("Save JS-extracted links to db");
          let urlsCreated = 0;

          for (const linkUrl of internalLinks) {
            const [link, created] = await SiteLink.findOrCreate({
              where: {
                url: linkUrl,
                siteId,
              },
              defaults: {
                url: linkUrl,
                siteId,
                status: "pending",
                lastScrapedAt: null,
                metadata: { source: "js_extracted" },
              },
            });

            if (created) {
              urlsCreated++;
            }
          }

          site.setDataValue("lastScrapedAt", new Date());
          await site.save();

          endProgress(`Added ${urlsCreated} new links via Puppeteer`);
        } else {
          // Process XML content
          const { urls } = await parseSitemapContent(content, siteUrl);

          if (urls.length > 0) {
            logger.info(`Found ${urls.length} URLs in ${siteUrl}/sitemap.xml via Puppeteer`);

            const endProgress = trackProgress("Save sitemap links to db");
            let urlsCreated = 0;

            for (const urlEntry of urls) {
              const [link, created] = await SiteLink.findOrCreate({
                where: {
                  url: urlEntry.loc,
                  siteId,
                },
                defaults: {
                  url: urlEntry.loc,
                  siteId,
                  status: "pending",
                  lastScrapedAt: null,
                  metadata: urlEntry.lastmod ? { lastModified: urlEntry.lastmod } : {},
                },
              });

              if (created) {
                urlsCreated++;
              }
            }

            site.setDataValue("lastScrapedAt", new Date());
            await site.save();

            endProgress(`Added ${urlsCreated} new links via Puppeteer`);
          }
        }
      } finally {
        await browser.close();
      }
    } else {
      const res = await fetchWithUserHeaders(`${siteUrl}/sitemap.xml`);

      if (!res.ok) {
        logger.error(`Failed to fetch sitemap: ${res.status} ${res.statusText}`);
        throw new Error(`Failed to fetch sitemap: ${res.statusText}`);
      }

      const contentType = res.headers.get("content-type");

      if (!contentType?.includes("xml")) {
        logger.error(`Response is not XML, got content type: ${contentType}`);
      }

      let xml: string | null = null;

      try {
        xml = await res.text();
      } catch (error) {
        logger.error("Failed to fetch sitemap:", error);
        throw new Error("Failed to fetch sitemap");
      }

      if (xml.includes("<!doctype html>") || xml.includes("<html")) {
        logger.error("Received HTML instead of XML, likely anti-bot protection");
        throw new Error("Anti-bot protection detected");
      }

      const { urls } = await parseSitemapContent(xml, siteUrl);

      let urlsCreated = 0;

      if (urls.length > 0) {
        logger.info(`Found ${String(urls.length)} URLs in ${siteUrl}/sitemap.xml`);

        const endProgress = trackProgress("Save sitemap links to db");

        for (const urlEntry of urls) {
          const [link, created] = await SiteLink.findOrCreate({
            where: {
              url: urlEntry.loc,
              siteId,
            },
            defaults: {
              url: urlEntry.loc,
              siteId,
              status: "pending",
              lastScrapedAt: null,
              metadata: urlEntry.lastmod ? { lastModified: urlEntry.lastmod } : {},
            },
          });

          if (created) {
            urlsCreated++;
          }
        }

        site.setDataValue("lastScrapedAt", new Date());
        await site.save();

        endProgress(`Added ${String(urlsCreated)} new links`);
      }

      // Process internal links if enabled
      if (crawlInternalLinks) {
        logger.info(`Starting internal link crawling for ${siteUrl}`);

        // Get the homepage content to find internal links
        try {
          const { documentLoader } = await import("@/services/embeddings/document_loaders");
          const { pageContent, linksOnPage } = await documentLoader.fromHTMLPage(siteUrl);

          if (linksOnPage && linksOnPage.length > 0) {
            const internalLinks = linksOnPage.filter(
              (link) => link.startsWith(siteUrl) || (link.startsWith("/") && !link.startsWith("//")),
            );

            logger.info(`Found ${internalLinks.length} internal links on homepage`);

            const endInternalProgress = trackProgress("Save internal links to db");
            let internalUrlsCreated = 0;

            for (const linkUrl of internalLinks) {
              // Normalize relative URLs
              const fullUrl = linkUrl.startsWith("/") ? `${siteUrl}${linkUrl}` : linkUrl;

              const [link, created] = await SiteLink.findOrCreate({
                where: {
                  url: fullUrl,
                  siteId,
                },
                defaults: {
                  url: fullUrl,
                  siteId,
                  status: "pending",
                  lastScrapedAt: null,
                  metadata: { source: "internal_link" },
                },
              });

              if (created) {
                internalUrlsCreated++;
              }
            }

            endInternalProgress(`Added ${internalUrlsCreated} new internal links`);
          }
        } catch (error) {
          logger.error(`Error crawling internal links for ${siteUrl}:`, error);
        }
      }

      logger.info(`Completed processing site: ${siteUrl}`);
    }
  } catch (error) {
    logger.error(`Error processing site ${siteUrl}:`, error);
  }
};

// delay function is now imported from @/lib/async-utils

// New function to process multiple sites in parallel
export const processSitesInParallel = async (
  siteUrls: string[],
  crawlInternalLinks = true,
  concurrency = 3,
): Promise<void> => {
  logger.info(`Processing ${siteUrls.length} sites with concurrency of ${concurrency}`);

  // Process sites in batches based on concurrency
  for (let i = 0; i < siteUrls.length; i += concurrency) {
    const batch = siteUrls.slice(i, i + concurrency);
    logger.info(`Processing batch ${Math.floor(i / concurrency) + 1} with ${batch.length} sites`);

    const batchPromises = batch.map((siteUrl) => processSite(siteUrl, crawlInternalLinks));
    await Promise.all(batchPromises);

    // Add a small delay between batches to avoid overwhelming resources
    if (i + concurrency < siteUrls.length) {
      logger.info("Waiting between batches...");
      await delay(2000);
    }
  }

  logger.info("Completed processing all sites");
};

// Keep the original function for backward compatibility
export const getSitemapUrls = async (siteUrl: string, crawlInternalLinks = true): Promise<void> => {
  return processSite(siteUrl, crawlInternalLinks);
};
