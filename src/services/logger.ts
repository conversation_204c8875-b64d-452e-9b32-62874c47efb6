import { format, createLogger, transports } from "winston";
import DailyRotateFile from "winston-daily-rotate-file";

import { config } from "@/config";

// Add a custom format to sanitize binary data
const sanitizeFormat = format((info) => {
  // Function to check if a string looks like binary data
  const isBinaryLike = (str: string): boolean => {
    if (typeof str !== "string") return false;

    // Check for high concentration of non-printable characters
    const nonPrintableCount = str.split("").filter((char) => {
      const code = char.charCodeAt(0);
      return code < 32 || code > 126;
    }).length;

    // If more than 10% are non-printable, consider it binary
    return nonPrintableCount > str.length * 0.1;
  };

  // Recursively sanitize objects
  const sanitize = (obj: any): any => {
    if (obj === null || obj === undefined) return obj;

    if (typeof obj === "string") {
      if (isBinaryLike(obj)) {
        return "[Binary data removed]";
      }
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(sanitize);
    }

    if (typeof obj === "object") {
      const result = {};
      for (const key in obj) {
        result[key] = sanitize(obj[key]);
      }
      return result;
    }

    return obj;
  };

  // Sanitize the message and any metadata
  if (typeof info.message === "string" && isBinaryLike(info.message)) {
    info.message = "[Binary data removed]";
  }

  // Sanitize other properties
  for (const key in info) {
    if (key !== "message" && key !== "level") {
      info[key] = sanitize(info[key]);
    }
  }

  return info;
});

const consoleTransport = new transports.Console({
  level: "debug", // Ensure we're capturing all levels
  format: format.combine(
    sanitizeFormat(),
    format.timestamp({
      format: "YYYY-MM-DD HH:mm:ss",
    }),
    format.colorize(),
    format.printf(({ timestamp, level, message }) => `[${String(timestamp)}] [${level}]: ${String(message)}`),
  ),
});

const fileRotateTransport = new DailyRotateFile({
  filename: "logs/%DATE%.log",
  datePattern: "YYYY-MM-DD",
  maxSize: "10m",
  maxFiles: "14d",
  format: format.combine(sanitizeFormat(), format.timestamp(), format.json({ deterministic: true })),
});

export const logger = createLogger({
  level: config.logLevel || "info",
  transports: [consoleTransport, fileRotateTransport],
});

// Add immediate console output for debugging
console.log("Logger initialized");

export const trackProgress = (operationName: string, timeoutMs = 120000) => {
  const startTime = performance.now();
  let dots = 0;

  logger.info(`Started ${operationName}`);

  const interval = setInterval(() => {
    process.stdout.write(`\rProcessing${".".repeat(dots)}${" ".repeat(3 - dots)}`);
    dots = (dots + 1) % 4;
  }, 300);

  const timeout = setTimeout(() => {
    clearInterval(interval);
    process.stdout.write("\r" + " ".repeat("Processing...".length) + "\r");

    logger.warn(`${operationName} timed out after ${timeoutMs}ms`, {
      operationName,
      durationMs: timeoutMs,
    });
  }, timeoutMs);

  return (resultMessage?: string): number => {
    const endTime = performance.now();
    const durationMs = Math.round(endTime - startTime);

    clearInterval(interval);
    clearTimeout(timeout);
    process.stdout.write("\r" + " ".repeat("Processing...".length) + "\r");

    logger.info(`${operationName} finished in ${durationMs}ms, ${resultMessage ?? ""}`, {
      operationName,
      durationMs,
    });

    return durationMs;
  };
};
