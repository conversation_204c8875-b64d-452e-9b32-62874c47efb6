import * as fs from "fs";
import * as path from "path";
import * as url from "url";

import html2pug from "html2pug"; // You'll need to install this package
import * as cheerio from "cheerio";
import pdf from "pdf-parse";
import { htmlToText } from "html-to-text";
import puppeteer from "puppeteer";

import { fetchWithUserHeaders, getRandomUserAgent } from "@/utils/helpers";
import { logger } from "@/services/logger";
import { uploadFileToS3, fileExistsInS3 } from "@/services/s3";
import { Site } from "@/store/models/site";
import { FileLink } from "@/store/models/file_link";
import { config } from "@/config";

const DOWNLOADS_DIR = path.join(process.cwd(), "downloads");
const PUG_DIR = path.join(process.cwd(), "downloads", "pug");
const CLEAN_HTML_DIR = path.join(process.cwd(), "downloads", "clean_pug");

if (!fs.existsSync(DOWNLOADS_DIR)) {
  try {
    fs.mkdirSync(DOWNLOADS_DIR, { recursive: true });
    logger.info(`Created downloads directory: ${DOWNLOADS_DIR}`);
  } catch (error) {
    logger.error(`Error creating downloads directory: ${DOWNLOADS_DIR}`, error);
  }
}

// Create the PUG directory if it doesn't exist
if (!fs.existsSync(PUG_DIR)) {
  try {
    fs.mkdirSync(PUG_DIR, { recursive: true });
    logger.info(`Created PUG directory: ${PUG_DIR}`);
  } catch (error) {
    logger.error(`Error creating PUG directory: ${PUG_DIR}`, error);
  }
}

// Create the CLEAN_HTML directory if it doesn't exist
if (!fs.existsSync(CLEAN_HTML_DIR)) {
  try {
    fs.mkdirSync(CLEAN_HTML_DIR, { recursive: true });
    logger.info(`Created clean HTML directory: ${CLEAN_HTML_DIR}`);
  } catch (error) {
    logger.error(`Error creating clean HTML directory: ${CLEAN_HTML_DIR}`, error);
  }
}

// Function to get site domain from URL
const getSiteDomain = (urlString: string): string => {
  try {
    const parsedUrl = new URL(urlString);
    return parsedUrl.hostname.replace(/^www\./, "");
  } catch (error) {
    logger.error(`Error parsing URL: ${urlString}`, error);
    return "unknown-domain";
  }
};

// Function to generate S3 key for a file
const generateS3Key = async (fileUrl: string, siteId?: number): Promise<string> => {
  // Get filename from URL
  const filename = path.basename(fileUrl).split("?")[0];

  let siteDomain = "unknown-site";

  // If siteId is provided, get the site domain from the database
  if (siteId) {
    try {
      const site = await Site.findByPk(siteId);
      if (site) {
        siteDomain = getSiteDomain(site.getDataValue("url"));
      }
    } catch (error) {
      logger.error(`Error getting site domain for siteId ${siteId}`, error);
    }
  } else {
    // Try to extract domain from the file URL
    siteDomain = getSiteDomain(fileUrl);
  }

  // Generate a unique key with site domain as folder
  return `${siteDomain}/${filename}`;
};

export interface Document {
  pageContent: string;
  metadata: Record<string, unknown>;

  originalHtml?: string;

  linksOnPage?: string[];
  fileLinksOnPage?: string[];
}

const fromLocalFile = (): Document => {
  const contentPath = path.join(process.cwd(), "content", "website_content.txt");
  const text = fs.readFileSync(contentPath, "utf-8");

  return {
    pageContent: text,

    metadata: {
      source: contentPath,
    },
  };
};

const isFileLink = (url: string): boolean => {
  const extensions = [".pdf", ".docx", ".doc", ".csv", ".xlsx", ".xls", ".ppt", ".pptx", ".txt"];
  const lowerUrl = url.toLowerCase();

  // Check for known file extensions
  if (extensions.some((ext) => lowerUrl.endsWith(ext))) {
    return true;
  }

  // Check for relative paths that look like file links
  if (url.startsWith("/") && url.includes("/media/") && extensions.some((ext) => url.includes(ext))) {
    return true;
  }

  // Special handling for CivicPlus DocumentCenter links
  if (lowerUrl.includes("/documentcenter/view/") || lowerUrl.includes("/archive.aspx?adid=")) {
    return true;
  }

  // Remove the problematic logic that treats all extensionless URLs as files
  return false;
};

const NOISE_PHRASES = [
  "opens in new tab or window",
  "Sub-menu",
  "Powered by",
  "CONTACT US",
  "Download",
  "Instagram",
  "home",
  "about",
  "contact",
  "privacy",
  "terms",
  "login",
  "register",
  "dashboard",
  "blog",
  "All Rights Reserved",
];

const NOISE_REGEX = new RegExp(NOISE_PHRASES.join("|"), "gi");

const cleanText = (text: string): string => {
  return text
    .replace(NOISE_REGEX, "")
    .replace(/\*[\s*]*\*/g, "")
    .replace(/^\s*[*\s]+\s*$/gm, "")
    .replace(/\n{3,}/g, "\n\n")
    .trim();
};

// Function to sanitize HTML before Pug conversion
const sanitizeHtmlForPug = (html: string): string => {
  return (
    html
      // Fix common malformed attributes with extra quotes
      .replace(/(\w+="[^"]*")\s*">/g, "$1>")
      // Fix input tags with trailing quotes
      .replace(/<input([^>]*)">/g, "<input$1>")
      // Fix other self-closing tags with trailing quotes
      .replace(/<(img|br|hr|meta|link)([^>]*)">/g, "<$1$2>")
      // Remove any double quotes that appear before closing >
      .replace(/"\s*">/g, '">')
      // Clean up any remaining malformed quote patterns
      .replace(/="\s*"([^>]*)">/g, '="$1">')
  );
};

// Function to save HTML as Pug
const saveAsPug = (html: string, url: string): string | null => {
  try {
    // Generate a filename based on the URL
    const parsedUrl = new URL(url);
    const hostname = parsedUrl.hostname.replace(/^www\./, "");
    const pathname = parsedUrl.pathname.replace(/\//g, "_");
    const filename = `${hostname}${pathname}.pug`;
    const filePath = path.join(PUG_DIR, filename);

    try {
      // Sanitize HTML before conversion
      const sanitizedHtml = sanitizeHtmlForPug(html);

      // Try to convert HTML to Pug
      const pugContent = html2pug(sanitizedHtml, { tabs: true });

      // Save the Pug content to file
      fs.writeFileSync(filePath, pugContent);

      return filePath;
    } catch (pugError) {
      // If HTML-to-Pug conversion fails, log the error and save the original HTML instead
      logger.error(
        `Error converting HTML to Pug for URL: ${url}`,
        pugError?.toString().slice(0, 300) + (pugError?.toString().length > 300 ? "…" : ""),
      );

      // Save the original HTML with a .html extension as a fallback
      const htmlFilename = `${hostname}${pathname}.html`;
      const htmlFilePath = path.join(PUG_DIR, htmlFilename);

      fs.writeFileSync(htmlFilePath, html);
      logger.info(`Saved original HTML instead of Pug for URL: ${url}`);

      return htmlFilePath;
    }
  } catch (error) {
    logger.error(`Error saving Pug for URL: ${url}`, error);
    return null;
  }
};

// Function to save clean HTML
const saveCleanHtml = (html: string, url: string): string | null => {
  try {
    // Generate a filename based on the URL
    const parsedUrl = new URL(url);
    const hostname = parsedUrl.hostname.replace(/^www\./, "");
    const pathname = parsedUrl.pathname.replace(/\//g, "_");
    const filename = `${hostname}${pathname}.html`;
    const filePath = path.join(CLEAN_HTML_DIR, filename);

    // Save the clean HTML content to file
    fs.writeFileSync(filePath, html);

    return filePath;
  } catch (error) {
    logger.error(`Error saving clean HTML for URL: ${url}`, error);
    return null;
  }
};

// Add this function to extract metadata from HTML documents
const extractDocumentMetadata = ($: CheerioStatic, url: string): Record<string, unknown> => {
  const metadata: Record<string, unknown> = {};

  // Try to extract document title
  const title = $('meta[property="og:title"]').attr("content") || $("title").text() || $("h1").first().text();
  if (title) metadata.title = title.trim();

  // Try to extract document description
  const description =
    $('meta[property="og:description"]').attr("content") || $('meta[name="description"]').attr("content");
  if (description) metadata.description = description.trim();

  // Try to extract last modified date
  const lastModified =
    $('meta[property="article:modified_time"]').attr("content") || $("time[datetime]").attr("datetime");
  if (lastModified) metadata.lastModified = lastModified;

  // For CivicPlus sites (like Roeland Park), try to extract specific metadata
  if (url.includes("roelandpark.net")) {
    // Document Center specific metadata
    if (url.includes("/DocumentCenter/View/")) {
      // Extract document ID from URL
      const docIdMatch = url.match(/\/DocumentCenter\/View\/(\d+)/);
      if (docIdMatch && docIdMatch[1]) metadata.documentId = docIdMatch[1];

      // Extract document info from specific elements
      const docTitle = $(".DocumentCenter__Title").text();
      if (docTitle) metadata.documentTitle = docTitle.trim();

      // Extract document details
      $(".DocumentCenter__Details div").each((i, el) => {
        const text = $(el).text().trim();
        if (text.includes("Category:")) {
          metadata.category = text.replace("Category:", "").trim();
        } else if (text.includes("Last Updated:")) {
          metadata.lastUpdated = text.replace("Last Updated:", "").trim();
        }
      });

      // Extract tags if available
      const tags: string[] = [];
      $(".DocumentCenter__Tags a").each((i, el) => {
        tags.push($(el).text().trim());
      });
      if (tags.length > 0) metadata.tags = tags;
    }
  }

  return metadata;
};

// Add this function to detect Cloudflare challenges
const isCloudflareChallenge = (html: string): boolean => {
  return (
    html.includes("Just a moment...") &&
    (html.includes("cf-browser-verification") || html.includes("cf_chl_opt") || html.includes("challenge-platform"))
  );
};

// Add this function to bypass Cloudflare using Puppeteer
const bypassCloudflare = async (url: string): Promise<string | null> => {
  logger.info(`Attempting to bypass Cloudflare for: ${url}`);

  try {
    const browser = await puppeteer.launch({
      headless: "new",
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-web-security",
        "--disable-features=IsolateOrigins,site-per-process",
      ],
    });

    const page = await browser.newPage();

    // Set realistic browser fingerprinting
    await page.setUserAgent(
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    );

    // Set viewport to realistic desktop size
    await page.setViewport({ width: 1920, height: 1080 });

    // Set extra HTTP headers
    await page.setExtraHTTPHeaders({
      "Accept-Language": "en-US,en;q=0.9",
      Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
      "Accept-Encoding": "gzip, deflate, br",
      Connection: "keep-alive",
      "Upgrade-Insecure-Requests": "1",
    });

    // Navigate to the URL with extended timeout
    await page.goto(url, {
      waitUntil: "networkidle2",
      timeout: 60000,
    });

    // Use setTimeout instead of waitForTimeout for older Puppeteer versions
    await new Promise((resolve) => setTimeout(resolve, 10000));

    // Sometimes we need to solve interactive challenges
    try {
      // Check if there's a checkbox to click (common in Cloudflare challenges)
      const checkbox = await page.$('input[type="checkbox"]');
      if (checkbox) {
        await checkbox.click();
        await new Promise((resolve) => setTimeout(resolve, 5000));
      }

      // Check for "I am human" verification
      const verifyButton = await page.$('input[type="button"][value*="Verify"]');
      if (verifyButton) {
        await verifyButton.click();
        await new Promise((resolve) => setTimeout(resolve, 5000));
      }
    } catch (interactionError) {
      logger.warn(`No interactive elements found in Cloudflare challenge: ${interactionError.message}`);
    }

    // Get the page content after the challenge is solved
    const content = await page.content();

    await browser.close();

    // Check if we still have a challenge page
    if (isCloudflareChallenge(content)) {
      logger.warn(`Failed to bypass Cloudflare for: ${url}`);
      return null;
    }

    return content;
  } catch (error) {
    logger.error(`Error bypassing Cloudflare for ${url}:`, error);
    return null;
  }
};

// Add to your imports
import * as fs from "fs";
import * as path from "path";

// Add this function for a more advanced Cloudflare bypass
const bypassCloudflareAdvanced = async (url: string): Promise<string | null> => {
  logger.info(`Attempting advanced Cloudflare bypass for: ${url}`);

  try {
    const browser = await puppeteer.launch({
      headless: "new",
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-web-security",
        "--disable-features=IsolateOrigins,site-per-process",
        "--disable-blink-features=AutomationControlled",
      ],
    });

    const page = await browser.newPage();

    // Inject stealth scripts to avoid detection
    await page.evaluateOnNewDocument(() => {
      // Override the navigator properties
      Object.defineProperty(navigator, "webdriver", {
        get: () => false,
      });

      // Add language plugins to appear more like a real browser
      Object.defineProperty(navigator, "languages", {
        get: () => ["en-US", "en"],
      });

      // Add a fake plugins array
      Object.defineProperty(navigator, "plugins", {
        get: () => [{ name: "Chrome PDF Plugin" }, { name: "Chrome PDF Viewer" }, { name: "Native Client" }],
      });

      // Override the permissions API
      const originalQuery = window.navigator.permissions.query;
      window.navigator.permissions.query = (parameters) =>
        parameters.name === "notifications"
          ? Promise.resolve({ state: Notification.permission })
          : originalQuery(parameters);

      // Override WebGL fingerprinting
      const getParameter = WebGLRenderingContext.prototype.getParameter;
      WebGLRenderingContext.prototype.getParameter = function (parameter) {
        // UNMASKED_VENDOR_WEBGL
        if (parameter === 37445) {
          return "Intel Inc.";
        }
        // UNMASKED_RENDERER_WEBGL
        if (parameter === 37446) {
          return "Intel Iris OpenGL Engine";
        }
        return getParameter.apply(this, arguments);
      };
    });

    // Set realistic browser fingerprinting
    await page.setUserAgent(
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    );

    // Set cookies to appear more like a real browser
    await page.setCookie({
      name: "cf_clearance",
      value: "random_value",
      domain: new URL(url).hostname,
    });

    // Set viewport to realistic desktop size
    await page.setViewport({ width: 1920, height: 1080 });

    // Set extra HTTP headers
    await page.setExtraHTTPHeaders({
      "Accept-Language": "en-US,en;q=0.9",
      Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
      "Accept-Encoding": "gzip, deflate, br",
      Connection: "keep-alive",
      "Upgrade-Insecure-Requests": "1",
      "Sec-Fetch-Dest": "document",
      "Sec-Fetch-Mode": "navigate",
      "Sec-Fetch-Site": "none",
      "Sec-Fetch-User": "?1",
      "sec-ch-ua": '"Google Chrome";v="121", " Not;A Brand";v="99", "Chromium";v="121"',
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": '"Windows"',
    });

    // Navigate to the URL with extended timeout
    await page.goto(url, {
      waitUntil: "networkidle2",
      timeout: 60000,
    });

    // Wait for Cloudflare challenge to be solved
    await new Promise((resolve) => setTimeout(resolve, 15000));

    // Try to interact with Cloudflare elements if present
    try {
      // Check for checkbox
      const checkbox = await page.$('input[type="checkbox"]');
      if (checkbox) {
        await checkbox.click();
        await new Promise((resolve) => setTimeout(resolve, 5000));
      }

      // Check for verification button
      const verifyButton = await page.$('input[type="button"][value*="Verify"]');
      if (verifyButton) {
        await verifyButton.click();
        await new Promise((resolve) => setTimeout(resolve, 5000));
      }

      // Check for "I am human" text
      const humanText = await page.$('div:contains("I am human")');
      if (humanText) {
        await humanText.click();
        await new Promise((resolve) => setTimeout(resolve, 5000));
      }
    } catch (interactionError) {
      logger.warn(`No interactive elements found in Cloudflare challenge: ${interactionError.message}`);
    }

    // Take a screenshot for debugging
    const screenshotDir = path.join(process.cwd(), "screenshots");
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }

    const screenshotPath = path.join(screenshotDir, `cloudflare-${Date.now()}.png`);
    await page.screenshot({ path: screenshotPath, fullPage: true });
    logger.info(`Saved screenshot to ${screenshotPath}`);

    // Get the page content after the challenge is solved
    const content = await page.content();

    await browser.close();

    // Check if we still have a challenge page
    if (isCloudflareChallenge(content)) {
      logger.warn(`Failed to bypass Cloudflare for: ${url}`);
      return null;
    }

    return content;
  } catch (error) {
    logger.error(`Error in advanced Cloudflare bypass for ${url}:`, error);
    return null;
  }
};

// Add this function to detect if a site requires JavaScript
const detectJavaScriptRequirement = (html: string): boolean => {
  return (
    html.includes("JavaScript is disabled") ||
    html.includes("enable JavaScript") ||
    html.includes("requires JavaScript") ||
    html.includes("noscript") ||
    html.includes("js-required")
  );
};

const fromHTMLPage = async (url: string): Promise<Document> => {
  // Special handling for DocumentCenter and Archive URLs
  if (url.includes("/DocumentCenter/View/") || url.includes("/Archive.aspx?ADID=")) {
    try {
      logger.info(`Special handling for document/archive URL: ${url}`);

      const res = await fetchWithUserHeaders(url, {
        // Add additional headers that might be needed
        headers: {
          Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
          "User-Agent": "Mozilla/5.0 (compatible; CityBot/1.0)",
          Cookie: "CivicPlus=accepted",
        },
        redirect: "follow",
      });

      logger.info(`🔍 SPECIAL HANDLING: Response status for ${url}: ${res.status} ${res.statusText}`);
      const contentType = res.headers.get("content-type") || "";
      logger.info(`🔍 SPECIAL HANDLING: Content-Type for ${url}: ${contentType}`);

      // Log all headers for debugging
      const headers = {};
      res.headers.forEach((value, name) => {
        headers[name] = value;
      });
      logger.info(`🔍 SPECIAL HANDLING: Response headers for ${url}: ${JSON.stringify(headers)}`);

      const originalContent = await res.arrayBuffer();
      const buffer = Buffer.from(originalContent);

      // Check if we got a PDF or other file type
      if (
        contentType.includes("application/pdf") ||
        contentType.includes("application/msword") ||
        contentType.includes("application/vnd.openxmlformats") ||
        contentType.includes("application/octet-stream") ||
        buffer.toString("ascii", 0, 1000).includes("%PDF")
      ) {
        // Generate filename from URL
        const parsedUrl = new URL(url);
        const pathname = parsedUrl.pathname.replace(/\//g, "_");
        const filename = `roelandpark${pathname}${contentType.includes("pdf") ? ".pdf" : ".bin"}`;
        const filePath = path.join(DOWNLOADS_DIR, filename);

        // Save the file
        fs.writeFileSync(filePath, buffer);
        logger.info(`Saved document from ${url} to ${filePath}`);

        // Generate S3 key and upload
        const s3Key = await generateS3Key(url);
        const s3Url = await uploadFileToS3(filePath, s3Key);

        // If it's a PDF, try to process it
        if (contentType.includes("pdf") || buffer.toString("ascii", 0, 1000).includes("%PDF")) {
          try {
            const document = await fromPDF(filePath, url);
            return {
              ...document,
              metadata: {
                ...document.metadata,
                sourceUrl: url,
                s3Key,
                s3Url,
                filePath,
              },
            };
          } catch (pdfError) {
            logger.error(`Failed to process content as PDF: ${url}`, pdfError);
          }
        }

        // Return minimal document for non-PDF or failed PDF processing
        return {
          pageContent: `[File downloaded from: ${url}]`,
          metadata: {
            sourceUrl: url,
            isFile: true,
            filePath,
            fileType: contentType.split("/")[1] || "unknown",
            s3Key,
            s3Url,
            isContentTooShort: false,
            linksOnPage: [],
            fileLinksOnPage: [],
          },
          linksOnPage: [],
          fileLinksOnPage: [],
          originalHtml: null,
        };
      }
    } catch (specialHandlingError) {
      logger.error(`Error in special handling for ${url}:`, specialHandlingError);
      // Continue to standard processing as fallback
    }
  }

  // Original implementation continues below...
  const res = await fetchWithUserHeaders(url);
  const contentType = res.headers.get("content-type") || "";
  const originalContent = await res.arrayBuffer();
  const buffer = Buffer.from(originalContent);

  // Check if the content is a PDF or other file type
  if (
    contentType.includes("application/pdf") ||
    contentType.includes("application/msword") ||
    contentType.includes("application/vnd.openxmlformats") ||
    contentType.includes("application/octet-stream") ||
    buffer.toString("ascii", 0, 1000).includes("%PDF") ||
    buffer.toString("ascii", 0, 1000).includes("/Type /Catalog")
  ) {
    logger.info(`Content from ${url} is a file (${contentType}). Saving as file instead of HTML.`);

    // Don't log the binary content
    logger.info(`Detected binary content from ${url}, saving as file`);

    // Determine file extension based on content type
    let fileExtension = ".bin";
    if (contentType.includes("pdf")) fileExtension = ".pdf";
    else if (contentType.includes("msword")) fileExtension = ".doc";
    else if (contentType.includes("openxmlformats") && contentType.includes("wordprocessingml"))
      fileExtension = ".docx";
    else if (contentType.includes("openxmlformats") && contentType.includes("spreadsheetml")) fileExtension = ".xlsx";
    else if (buffer.toString("ascii", 0, 1000).includes("%PDF")) fileExtension = ".pdf";

    // Generate a filename based on the URL
    const parsedUrl = new URL(url);
    const hostname = parsedUrl.hostname.replace(/^www\./, "");
    const pathname = parsedUrl.pathname.replace(/\//g, "_");
    const filename = `${hostname}${pathname}${fileExtension}`;
    const filePath = path.join(DOWNLOADS_DIR, filename);

    // Save the content as a file
    fs.writeFileSync(filePath, buffer);
    logger.info(`Saved file from ${url} to ${filePath}`);

    // If it's a PDF, process it as a PDF
    if (fileExtension === ".pdf") {
      try {
        const document = await fromPDF(filePath, url);

        // Generate S3 key and upload to S3
        const s3Key = await generateS3Key(url);
        const s3Url = await uploadFileToS3(filePath, s3Key);

        return {
          ...document,
          metadata: {
            ...document.metadata,
            sourceUrl: url,
            s3Key,
            s3Url,
            filePath,
          },
        };
      } catch (pdfError) {
        logger.error(`Failed to process content as PDF: ${url}`, pdfError);
        // Continue to the fallback return below
      }
    }

    // For non-PDF files or if PDF processing failed, return a minimal document
    return {
      pageContent: `[File downloaded: ${filename}]`,
      metadata: {
        sourceUrl: url,
        isFile: true,
        filePath,
        fileType: fileExtension.substring(1), // Remove the dot
        isContentTooShort: false,
        linksOnPage: [],
        fileLinksOnPage: [],
      },
      linksOnPage: [],
      fileLinksOnPage: [],
      originalHtml: null,
    };
  }

  // If it's HTML content, process as before
  const originalHtml = buffer.toString("utf-8");

  // Check if the response is a Cloudflare challenge page
  if (isCloudflareChallenge(originalHtml)) {
    logger.warn(`Cloudflare protection detected for URL: ${url}, attempting bypass...`);

    // Try the advanced bypass first
    let bypassedHtml = await bypassCloudflareAdvanced(url);

    // If advanced bypass fails, try the regular bypass
    if (!bypassedHtml || isCloudflareChallenge(bypassedHtml)) {
      logger.info(`Advanced bypass failed, trying regular bypass for: ${url}`);
      bypassedHtml = await bypassCloudflare(url);
    }

    if (bypassedHtml && !isCloudflareChallenge(bypassedHtml)) {
      // Process the bypassed HTML
      logger.info(`Successfully bypassed Cloudflare for: ${url}`);
      originalHtml = bypassedHtml;
      // Continue processing with the new HTML
    } else {
      // Return the Cloudflare detection document
      return {
        pageContent: `[Cloudflare protection detected: ${url}]`,
        metadata: {
          sourceUrl: url,
          isCloudflareProtected: true,
          isContentTooShort: true,
          linksOnPage: [],
          fileLinksOnPage: [],
        },
        linksOnPage: [],
        fileLinksOnPage: [],
        originalHtml,
      };
    }
  }

  // Save the original HTML as Pug
  const pugFilePath = saveAsPug(originalHtml, url);

  const $ = cheerio.load(originalHtml);

  // Extract document metadata
  const documentMetadata = extractDocumentMetadata($, url);

  $("img").remove();

  // Save the clean HTML (after removing images)
  const cleanHtmlFilePath = saveCleanHtml($.html(), url);

  const linksOnPage: string[] = [];
  const fileLinksOnPage: string[] = [];

  const baseUrl = new URL(url);

  $("a[href]").each((_, element) => {
    const href = $(element).attr("href");
    if (href) {
      try {
        const absoluteUrl = new URL(href, baseUrl.origin).href;

        // Check if it's a known file type
        if (isFileLink(absoluteUrl)) {
          fileLinksOnPage.push(absoluteUrl);
          return;
        }

        // Add all other links to linksOnPage
        linksOnPage.push(absoluteUrl);
      } catch (error) {
        logger.error(`Error processing link: ${href}`, error);
      }
    }
    $(element).remove();
  });

  const rawText = htmlToText($("body").html() ?? "", {
    wordwrap: false,
  });

  const text = cleanText(rawText);

  return {
    pageContent: text,
    metadata: {
      sourceUrl: url,
      linksOnPage,
      fileLinksOnPage,
      isContentTooShort: text.length < 100,
      cleanHtmlFilePath, // Add the clean HTML file path to metadata
      pugFilePath, // Add the pug file path to metadata
      ...documentMetadata, // Add the extracted metadata
    },

    originalHtml,
    linksOnPage,
    fileLinksOnPage,
  };
};

const fromExistingHtml = (html: string, url: string): Document => {
  // Handle null or undefined html
  if (!html) {
    logger.warn(`Empty HTML content for URL: ${url}`);
    return {
      pageContent: `[Empty content for: ${url}]`,
      metadata: {
        sourceUrl: url,
        isContentTooShort: true,
        linksOnPage: [],
        fileLinksOnPage: [],
      },
      linksOnPage: [],
      fileLinksOnPage: [],
      originalHtml: null,
    };
  }

  // Check if the content appears to be a file
  const isBinary =
    typeof html === "string" &&
    (html.includes("%PDF") ||
      html.includes("/Type /Catalog") ||
      html.includes("endobj") ||
      /[\x00-\x08\x0B\x0C\x0E-\x1F\x80-\xFF]/.test(html.substring(0, 1000)));

  if (isBinary) {
    logger.info(`Content from ${url} appears to be a binary file. Saving as file instead of HTML.`);

    // Determine if it's likely a PDF
    const isPdf = html.includes("%PDF") || html.includes("/Type /Catalog");
    const fileExtension = isPdf ? ".pdf" : ".bin";

    // Generate a filename based on the URL
    const parsedUrl = new URL(url);
    const hostname = parsedUrl.hostname.replace(/^www\./, "");
    const pathname = parsedUrl.pathname.replace(/\//g, "_");
    const filename = `${hostname}${pathname}${fileExtension}`;
    const filePath = path.join(DOWNLOADS_DIR, filename);

    // Save the content as a file
    fs.writeFileSync(filePath, Buffer.from(html, isPdf ? "binary" : "utf-8"));
    logger.info(`Saved file from ${url} to ${filePath}`);

    // For non-PDF files or if PDF processing failed, return a minimal document
    return {
      pageContent: `[File saved: ${filename}]`,
      metadata: {
        sourceUrl: url,
        isFile: true,
        filePath,
        fileType: fileExtension.substring(1), // Remove the dot
        isContentTooShort: false, // Add this to prevent the error
        linksOnPage: [], // Add these to the metadata
        fileLinksOnPage: [], // Add these to the metadata
      },
      linksOnPage: [],
      fileLinksOnPage: [],
      originalHtml: null,
    };
  }

  const $ = cheerio.load(html);

  // Save the original HTML as Pug
  const pugFilePath = saveAsPug(html, url);

  // Remove images for clean HTML
  $("img").remove();

  // Save the clean HTML (after removing images)
  const cleanHtmlFilePath = saveCleanHtml($.html(), url);

  const linksOnPage: string[] = [];
  const fileLinksOnPage: string[] = [];

  const baseUrl = new URL(url);

  $("a[href]").each((_, element) => {
    const href = $(element).attr("href");
    if (href) {
      try {
        const absoluteUrl = new URL(href, baseUrl.origin).href;

        // Check if it's a known file type
        if (isFileLink(absoluteUrl)) {
          fileLinksOnPage.push(absoluteUrl);
          return;
        }

        // Add all other links to linksOnPage
        linksOnPage.push(absoluteUrl);
      } catch (error) {
        logger.error(`Error processing link: ${href}`, error);
      }
    }
    $(element).remove();
  });

  const rawText = htmlToText($("body").html() ?? "", {
    wordwrap: false,
  });

  const text = cleanText(rawText);

  return {
    pageContent: text,
    metadata: {
      sourceUrl: url,
      linksOnPage,
      fileLinksOnPage,
      isContentTooShort: text.length < 100,
      cleanHtmlFilePath, // Add the clean HTML file path to metadata
      pugFilePath, // Add the pug file path to metadata
    },

    originalHtml: html,
    linksOnPage,
    fileLinksOnPage,
  };
};

export const createDocumentMetadata = (doc: Document) => {
  const { metadata } = doc;
  const source = metadata.sourceUrl || metadata.url || metadata.filename || "Unknown source";
  const sourceStr = typeof source === "object" ? JSON.stringify(source) : String(source);
  return `THIS ARTICLE IS RELATED TO: ${sourceStr}\n\n`;
};

export const createDocumentSources = (doc: Document) => {
  const { metadata } = doc;
  const sourceUrl = typeof metadata.sourceUrl === "string" ? metadata.sourceUrl : "Unknown source";

  let result = `\n\nSOURCES:\n- ${sourceUrl}`;
  if (doc.linksOnPage && doc.linksOnPage.length > 0) {
    result += "\n\nRELATED LINKS:";
    doc.linksOnPage.slice(0, 5).forEach((link) => {
      result += `\n- ${link}`;
    });
    if (doc.linksOnPage.length > 5) {
      result += `\n- Plus ${doc.linksOnPage.length - 5} more links`;
    }
  }

  if (doc.fileLinksOnPage && doc.fileLinksOnPage.length > 0) {
    result += "\n\nRELATED FILES:";
    doc.fileLinksOnPage.forEach((link) => {
      result += `\n- ${link}`;
    });
  }

  return result;
};

export const fromPDF = async (filePath: string, sourceUrl?: string) => {
  try {
    const dataBuffer = fs.readFileSync(filePath);

    // Capture console warnings during PDF parsing
    const originalWarn = console.warn;
    const warnings: string[] = [];

    console.warn = (message: string) => {
      warnings.push(message);
    };

    // Parse the PDF
    let pdfData;
    try {
      pdfData = await pdf(dataBuffer);
    } catch (pdfParseError) {
      // Restore original console.warn
      console.warn = originalWarn;

      logger.error(`Error parsing PDF: ${filePath}`, pdfParseError);

      // Return a minimal document with error information
      return {
        pageContent: `[PDF could not be parsed: ${filePath}]`,
        metadata: {
          sourceUrl: sourceUrl || filePath,
          foundInUrl: sourceUrl,
          title: path.basename(filePath),
          fileType: "pdf",
          isFile: true,
          isContentTooShort: false,
          processingError: pdfParseError instanceof Error ? pdfParseError.message : String(pdfParseError),
          linksOnPage: [],
          fileLinksOnPage: [],
        },
        linksOnPage: [],
        fileLinksOnPage: [],
        originalHtml: null,
      };
    }

    // Restore original console.warn
    console.warn = originalWarn;

    // Log warnings in a more controlled way
    if (warnings.length > 0) {
      const uniqueWarnings = [...new Set(warnings)];
      if (uniqueWarnings.length > 5) {
        logger.debug(
          `PDF parsing produced ${warnings.length} warnings (${uniqueWarnings.length} unique) for ${filePath}`,
        );
      } else {
        logger.debug(`PDF parsing warnings for ${filePath}: ${uniqueWarnings.join(", ")}`);
      }
    }

    const text = cleanText(pdfData.text || "");
    const fileName = path.basename(filePath);

    // Extract more detailed metadata from PDF info
    const enhancedMetadata: Record<string, unknown> = {};

    if (pdfData.info) {
      // Common PDF metadata fields
      if (pdfData.info.Title) enhancedMetadata.title = pdfData.info.Title;
      if (pdfData.info.Author) enhancedMetadata.author = pdfData.info.Author;
      if (pdfData.info.Subject) enhancedMetadata.subject = pdfData.info.Subject;
      if (pdfData.info.Keywords) enhancedMetadata.keywords = pdfData.info.Keywords;
      if (pdfData.info.CreationDate) enhancedMetadata.creationDate = pdfData.info.CreationDate;
      if (pdfData.info.ModDate) enhancedMetadata.lastModified = pdfData.info.ModDate;

      // Add any other metadata fields that might be present
      Object.entries(pdfData.info).forEach(([key, value]) => {
        if (!["Title", "Author", "Subject", "Keywords", "CreationDate", "ModDate"].includes(key)) {
          enhancedMetadata[key] = value;
        }
      });
    }

    return {
      pageContent: text || `[PDF content could not be extracted: ${fileName}]`,
      metadata: {
        sourceUrl: sourceUrl || fileName,
        foundInUrl: sourceUrl,
        title: enhancedMetadata.title || fileName,
        numPages: pdfData.numpages,
        info: pdfData.info,
        fileType: "pdf",
        isFile: true,
        isContentTooShort: text.length < 100,
        linksOnPage: [],
        fileLinksOnPage: [],
        ...enhancedMetadata, // Add the enhanced metadata
      },
      linksOnPage: [],
      fileLinksOnPage: [],
      originalHtml: null,
    };
  } catch (error) {
    logger.error(`Error parsing PDF: ${filePath}`, error);

    // Return a minimal document with error information instead of throwing
    return {
      pageContent: `[Error processing PDF: ${path.basename(filePath)}]`,
      metadata: {
        sourceUrl: sourceUrl || filePath,
        foundInUrl: sourceUrl,
        title: path.basename(filePath),
        fileType: "pdf",
        isFile: true,
        isContentTooShort: false,
        processingError: error instanceof Error ? error.message : String(error),
        linksOnPage: [],
        fileLinksOnPage: [],
      },
      linksOnPage: [],
      fileLinksOnPage: [],
      originalHtml: null,
    };
  }
};

const downloadAndSavePDF = async (
  url: string,
  siteId?: number,
): Promise<{ filePath: string; s3Key?: string; s3Url?: string }> => {
  try {
    logger.info(`Downloading PDF from URL: ${url}`);

    const filename = path.basename(url).split("?")[0];
    const filePath = path.join(DOWNLOADS_DIR, filename);

    if (fs.existsSync(filePath)) {
      logger.info(`PDF already exists at ${filePath}, skipping download`);

      // Generate S3 key
      const s3Key = await generateS3Key(url, siteId);

      // Check if file already exists in S3
      const existsInS3 = await fileExistsInS3(s3Key);

      if (existsInS3) {
        logger.info(`File already exists in S3 at ${s3Key}`);
        const s3Url = `https://${config.aws.s3Bucket}.s3.${config.aws.region}.amazonaws.com/${s3Key}`;
        return { filePath, s3Key, s3Url };
      }

      // Upload to S3
      const s3Url = await uploadFileToS3(filePath, s3Key);
      return { filePath, s3Key, s3Url };
    }

    const response = await fetchWithUserHeaders(url);
    if (!response.ok) {
      throw new Error(`Failed to download PDF: ${response.status} ${response.statusText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    fs.writeFileSync(filePath, buffer);
    logger.info(`Downloaded PDF and saved to ${filePath}`);

    // Generate S3 key
    const s3Key = await generateS3Key(url, siteId);

    // Upload to S3
    const s3Url = await uploadFileToS3(filePath, s3Key);

    return { filePath, s3Key, s3Url };
  } catch (error) {
    logger.error(`Error downloading PDF from URL: ${url}`, error);
    throw error;
  }
};

export const fromPDFUrl = async (url: string, siteId?: number, fileLinkId?: number) => {
  try {
    // Handle relative URLs by converting them to absolute URLs
    if (url.startsWith("/")) {
      // Check if we have a siteId to get the base URL
      if (siteId) {
        try {
          const site = await Site.findByPk(siteId);
          if (site) {
            const siteUrl = site.getDataValue("url");
            const baseUrl = new URL(siteUrl).origin;
            url = `${baseUrl}${url}`;
            logger.info(`Converted relative URL to absolute: ${url}`);
          }
        } catch (siteError) {
          logger.error(`Error getting site info for relative URL: ${siteError}`);
        }
      } else {
        throw new Error(`Cannot process relative URL without siteId: ${url}`);
      }
    }

    // Special handling for DocumentCenter URLs
    if (url.includes("/DocumentCenter/View/") || url.includes("/Archive.aspx?ADID=")) {
      // Determine the actual file type for DocumentCenter links
      const fileType = await getDocumentCenterFileType(url);

      if (fileType !== "pdf") {
        logger.info(`DocumentCenter URL ${url} is not a PDF, but a ${fileType} file`);

        // If we have a fileLinkId, update the FileLink with the correct file type
        if (fileLinkId) {
          try {
            const fileLink = await FileLink.findByPk(fileLinkId);
            if (fileLink) {
              await fileLink.update({
                fileType: fileType,
                status: fileType === "pdf" ? "pending" : "ignored",
                comment: fileType === "pdf" ? null : `Unsupported file type: ${fileType}`,
              });
              logger.info(`Updated FileLink ${fileLinkId} with correct file type: ${fileType}`);
            }
          } catch (updateError) {
            logger.error(`Failed to update FileLink file type: ${updateError}`);
          }
        }

        // If it's not a PDF, throw an error to skip processing
        if (fileType !== "pdf") {
          throw new Error(`File is not a PDF, but a ${fileType} file`);
        }
      }
    }

    const { filePath, s3Key, s3Url } = await downloadAndSavePDF(url, siteId);
    const document = await fromPDF(filePath, url);

    // Add S3 information to metadata
    if (s3Key && s3Url) {
      document.metadata.s3Key = s3Key;
      document.metadata.s3Url = s3Url;
    }

    // Normalize date fields to prevent database errors
    if (document.metadata.creationDate) {
      document.metadata.creationDate = safeParseDate(document.metadata.creationDate as string);
    }

    if (document.metadata.lastModified) {
      document.metadata.lastModified = safeParseDate(document.metadata.lastModified as string);
    }

    return document;
  } catch (error) {
    logger.error(`Error fetching or parsing PDF from URL: ${url}`, error);

    // If we have a fileLinkId, update the FileLink status
    if (fileLinkId) {
      try {
        const fileLink = await FileLink.findByPk(fileLinkId);
        if (fileLink) {
          await fileLink.update({
            status: "download_failed",
            processedAt: new Date(),
            comment: error instanceof Error ? error.message : String(error),
          });
          logger.info(`Marked FileLink ${fileLinkId} as download_failed`);
        }
      } catch (updateError) {
        logger.error(`Failed to update FileLink status: ${updateError}`);
      }
    }

    throw error;
  }
};

// // Utility function to ensure document has all required metadata fields
// const ensureDocumentMetadata = (document: Document): Document => {
//   if (!document) {
//     // Create a minimal valid document if none exists
//     document = {
//       pageContent: "[Error: Unable to process document]",
//       metadata: {},
//       linksOnPage: [],
//       fileLinksOnPage: [],
//       originalHtml: null,
//     };
//   }

//   if (!document.metadata) document.metadata = {};
//   if (document.metadata.isContentTooShort === undefined) document.metadata.isContentTooShort = false;
//   if (!document.metadata.linksOnPage) document.metadata.linksOnPage = [];
//   if (!document.metadata.fileLinksOnPage) document.metadata.fileLinksOnPage = [];
//   if (!document.linksOnPage) document.linksOnPage = [];
//   if (!document.fileLinksOnPage) document.fileLinksOnPage = [];

//   // Enhance document with metadata and sources
//   return enhanceDocumentWithMetadataAndSources(document);
// };

export const documentLoader = {
  fromLocalFile: () => ensureDocumentMetadata(fromLocalFile()),
  fromHTMLPage: async (url: string) => ensureDocumentMetadata(await fromHTMLPage(url)),
  fromExistingHtml: (html: string, url: string) => ensureDocumentMetadata(fromExistingHtml(html, url)),
  fromPDF: async (filePath: string, sourceUrl?: string) => ensureDocumentMetadata(await fromPDF(filePath, sourceUrl)),
  fromPDFUrl: async (url: string, siteId?: number, fileLinkId?: number) =>
    ensureDocumentMetadata(await fromPDFUrl(url, siteId, fileLinkId)),
};

// Helper function to detect file type from content
const detectFileTypeFromContent = (content: Buffer | string): { isFile: boolean; fileType: string | null } => {
  // Convert to buffer if it's a string
  const buffer = Buffer.isBuffer(content) ? content : Buffer.from(content);

  // Check for PDF signature
  if (buffer.toString("ascii", 0, 5) === "%PDF-") {
    return { isFile: true, fileType: "pdf" };
  }

  // Check for Office document signatures
  // DOC (Microsoft Word)
  if (buffer.toString("hex", 0, 8) === "d0cf11e0a1b11ae1") {
    return { isFile: true, fileType: "doc" };
  }

  // DOCX, XLSX, PPTX (Office Open XML)
  if (buffer.toString("hex", 0, 4) === "504b0304") {
    return { isFile: true, fileType: "office" }; // Need deeper inspection to determine exact type
  }

  // Check for binary content (high concentration of non-printable characters)
  const sample = buffer.slice(0, 1000);
  let binaryCharCount = 0;

  for (let i = 0; i < sample.length; i++) {
    if (sample[i] < 9 || (sample[i] > 13 && sample[i] < 32) || sample[i] > 126) {
      binaryCharCount++;
    }
  }

  // If more than 10% of characters are binary, consider it a binary file
  if (binaryCharCount > sample.length * 0.1) {
    return { isFile: true, fileType: "binary" };
  }

  return { isFile: false, fileType: null };
};

// Improve the getFileType function in the helpers.ts file or add it here
const getDocumentCenterFileType = async (url: string): Promise<string> => {
  try {
    // For DocumentCenter links, we need to make a HEAD request to determine the content type
    logger.info(`Checking content type for DocumentCenter URL: ${url}`);

    const response = await fetchWithUserHeaders(url, { method: "HEAD" });
    const contentType = response.headers.get("content-type") || "";

    if (contentType.includes("application/pdf")) {
      return "pdf";
    } else if (contentType.includes("application/msword")) {
      return "doc";
    } else if (contentType.includes("application/vnd.openxmlformats-officedocument.wordprocessingml.document")) {
      return "docx";
    } else if (contentType.includes("application/vnd.ms-excel")) {
      return "xls";
    } else if (contentType.includes("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")) {
      return "xlsx";
    } else if (contentType.includes("application/vnd.ms-powerpoint")) {
      return "ppt";
    } else if (contentType.includes("application/vnd.openxmlformats-officedocument.presentationml.presentation")) {
      return "pptx";
    } else if (contentType.includes("text/plain")) {
      return "txt";
    } else if (contentType.includes("text/csv")) {
      return "csv";
    } else if (contentType.includes("application/octet-stream")) {
      // For octet-stream, assume PDF as a fallback for DocumentCenter links
      return "pdf";
    }

    // Default to PDF for DocumentCenter links if we can't determine the type
    return "pdf";
  } catch (error) {
    logger.error(`Error determining file type for DocumentCenter URL: ${url}`, error);
    // Default to PDF as a fallback
    return "pdf";
  }
};

// Add this function to extract dates from URL paths
const extractDateFromUrl = (url: string): string | null => {
  // Look for year patterns in URL (2020, 2021, etc.)
  const yearMatch = url.match(/\b(20\d{2})\b/);
  if (yearMatch) {
    // Look for month patterns
    const monthMatch = url.match(/\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\w*\b/i);
    if (monthMatch) {
      return `${monthMatch[0]} ${yearMatch[0]}`;
    }
    return yearMatch[0];
  }
  return null;
};

// Add a function to normalize PDF date formats
const normalizePdfDate = (pdfDate: string): string | null => {
  if (!pdfDate) return null;

  // Handle PDF date format: D:YYYYMMDDHHmmSSOHH'mm'
  // Example: D:20100816073441-05'00'
  if (pdfDate.startsWith("D:")) {
    try {
      // Extract the date components
      const year = pdfDate.substring(2, 6);
      const month = pdfDate.substring(6, 8);
      const day = pdfDate.substring(8, 10);
      const hour = pdfDate.substring(10, 12);
      const minute = pdfDate.substring(12, 14);
      const second = pdfDate.substring(14, 16);

      // Check if we have valid date components
      if (year.length === 4 && month.length === 2 && day.length === 2) {
        return `${year}-${month}-${day}T${hour || "00"}:${minute || "00"}:${second || "00"}Z`;
      }
    } catch (error) {
      logger.warn(`Failed to parse PDF date: ${pdfDate}`, error);
    }
  }

  return null;
};

// Add a function to safely convert any date string to ISO format
export const safeParseDate = (dateStr: string | null | undefined): string | null => {
  if (!dateStr) return null;

  // First try to normalize PDF date format
  const normalizedPdfDate = normalizePdfDate(dateStr);
  if (normalizedPdfDate) return normalizedPdfDate;

  // Try to parse as a regular date
  try {
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
      return date.toISOString();
    }
  } catch (error) {
    // Ignore parsing errors
  }

  // If we can't parse it, return null
  return null;
};

// Extend the enrichDocumentWithDates function to handle PDF dates
export const enrichDocumentWithDates = (doc: Document): Document => {
  if (!doc.metadata) {
    doc.metadata = {};
  }

  // Normalize any date fields in metadata
  const dateFields = ["lastModified", "createdAt", "creationDate", "modDate", "createDate"];
  for (const field of dateFields) {
    if (doc.metadata[field] && typeof doc.metadata[field] === "string") {
      const parsedDate = safeParseDate(doc.metadata[field]);
      if (parsedDate) {
        doc.metadata[field] = parsedDate;
      } else {
        // If we can't parse it, remove it to avoid database errors
        delete doc.metadata[field];
      }
    }
  }

  // Extract dates from content if not already present
  if (!doc.metadata.datesInContent && doc.pageContent) {
    const extractedDates = extractDatesFromContent(doc.pageContent);
    if (extractedDates.length > 0) {
      doc.metadata.datesInContent = extractedDates;

      // Try to determine the most recent date if no date metadata exists
      if (!doc.metadata.lastModified && !doc.metadata.createdAt && !doc.metadata.extractedDate) {
        try {
          const validDates = extractedDates
            .map((dateStr) => {
              const parsed = safeParseDate(dateStr);
              return parsed ? new Date(parsed) : null;
            })
            .filter((date) => date !== null && !isNaN(date!.getTime()))
            .sort((a, b) => b!.getTime() - a!.getTime());

          if (validDates.length > 0 && validDates[0]) {
            doc.metadata.extractedDate = validDates[0].toISOString();
          }
        } catch (error) {
          logger.warn("Error parsing dates from content:", error);
        }
      }
    }
  }

  // Extract date from URL if available and not already present
  if (!doc.metadata.urlDate && doc.metadata.sourceUrl && typeof doc.metadata.sourceUrl === "string") {
    const urlDate = extractDateFromUrl(doc.metadata.sourceUrl);
    if (urlDate) {
      doc.metadata.urlDate = urlDate;
    }
  }

  return doc;
};

// Add this function to enhance documents with metadata and sources
export const enhanceDocumentWithMetadataAndSources = (doc: Document): Document => {
  if (!doc) return doc;

  // Add metadata prefix if needed
  if (doc.pageContent && !doc.pageContent.startsWith("THIS ARTICLE IS RELATED TO:")) {
    doc.pageContent = createDocumentMetadata(doc) + doc.pageContent;
  }

  // Add sources if needed
  if (doc.pageContent && !doc.pageContent.includes("\n\nSOURCES:")) {
    doc.pageContent = doc.pageContent + createDocumentSources(doc);
  }

  return doc;
};

// Update the ensureDocumentMetadata function to use the new enhancer
const ensureDocumentMetadata = (doc: Document): Document => {
  return enhanceDocumentWithMetadataAndSources(doc);
};

// Modify the fromUrl function to include date extraction
export const fromUrl = async (url: string): Promise<Document> => {
  // ... existing code ...

  // Extract publication date information
  let publicationDate = null;

  // Look for explicit publication date in the HTML
  const pubDateEl =
    $('meta[property="article:published_time"]').attr("content") ||
    $("time[datetime]").attr("datetime") ||
    $(".news-date").text() ||
    $(".date").text();

  if (pubDateEl) {
    try {
      publicationDate = new Date(pubDateEl);
      if (isNaN(publicationDate.getTime())) publicationDate = null;
    } catch (e) {
      publicationDate = null;
    }
  }

  // For archive pages, try to extract from content
  if (!publicationDate && (url.includes("/Archive.aspx") || url.includes("/ArchiveCenter/"))) {
    const postedMatch = html.match(/Posted\s+on\s+([A-Za-z]+\s+\d{1,2},\s+20\d{2})/i);
    if (postedMatch && postedMatch[1]) {
      try {
        publicationDate = new Date(postedMatch[1]);
        if (isNaN(publicationDate.getTime())) publicationDate = null;
      } catch (e) {
        publicationDate = null;
      }
    }
  }

  return {
    pageContent: text,
    metadata: {
      sourceUrl: url,
      linksOnPage,
      fileLinksOnPage,
      isContentTooShort: text.length < 100,
      cleanHtmlFilePath,
      pugFilePath,
      publicationDate: publicationDate ? publicationDate.toISOString() : null,
      // ... other existing metadata
    },
    originalHtml: html,
    linksOnPage,
    fileLinksOnPage,
  };
};

// Add this method to handle JavaScript-required pages with Puppeteer
async function fromHTMLPageWithPuppeteer(url: string): Promise<LoaderResult> {
  logger.info(`Loading page with Puppeteer: ${url}`);

  const browser = await puppeteer.launch({
    headless: "new",
    args: [
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-web-security",
      "--disable-features=IsolateOrigins,site-per-process",
    ],
  });

  try {
    const page = await browser.newPage();

    // Set realistic browser fingerprinting
    await page.setUserAgent(
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    );

    // Set extra HTTP headers
    await page.setExtraHTTPHeaders({
      "Accept-Language": "en-US,en;q=0.9",
      Accept: "*/*",
      Connection: "keep-alive",
      "Sec-Fetch-Dest": "document",
      "Sec-Fetch-Mode": "navigate",
      "Sec-Fetch-Site": "none",
      "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": '"macOS"',
    });

    // For Morrisville-specific sites, use CORS-style headers
    if (url.includes("morrisvillenc.gov")) {
      await page.setExtraHTTPHeaders({
        "Accept-Language": "en-US,en;q=0.9",
        Accept: "*/*",
        Accepts: "application/json",
        Connection: "keep-alive",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-site",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        Origin: "http://localhost:3001",
        Referer: "http://localhost:3001/",
      });
    }

    // Navigate to the URL and wait for content to load
    await page.goto(url, { waitUntil: "networkidle2", timeout: 30000 });

    // Wait for any JavaScript to execute
    await page.waitForTimeout(2000);

    // Get the rendered HTML
    const renderedHtml = await page.content();

    // Extract links from the page
    const linksOnPage = await page.evaluate(() => {
      return Array.from(document.querySelectorAll("a[href]"))
        .map((a) => a.getAttribute("href"))
        .filter((href) => href && !href.startsWith("#") && !href.startsWith("javascript:"));
    });

    // Extract file links
    const fileLinksOnPage = linksOnPage.filter((link) => link && /\.(pdf|doc|docx|xls|xlsx|ppt|pptx)$/i.test(link));

    // Process the HTML as usual
    const $ = cheerio.load(renderedHtml);

    // ... rest of the HTML processing code ...

    return {
      pageContent: cleanedText,
      metadata: {
        source: url,
        title: title || url,
        lastModified: lastModified,
        extractedDate: new Date().toISOString(),
      },
      linksOnPage,
      fileLinksOnPage,
    };
  } finally {
    await browser.close();
  }
}
