/**
 * Link processing utilities for discovering and managing site links
 */

import { logger } from "@/services/logger";
import { SiteLink, SiteLinkModel } from "@/store/models/site_link";
import { FileLink } from "@/store/models/file_link";
import { extractDomainAndTld, shouldProcessUrl, isFileUrl } from "@/utils/url-utils";
import { getFileType } from "@/utils/file-utils";
import { delay } from "@/utils/async-utils";

/**
 * Process newly discovered links with timeout and concurrency control
 */
export const processNewlyDiscoveredLinks = async (link: SiteLinkModel, siteUrl: string): Promise<number> => {
  try {
    const siteId = link.getDataValue("siteId");
    const linksOnPage = link.getDataValue("linksOnPage") || [];

    logger.info(`🔍 Processing ${linksOnPage.length} discovered links from ${link.getDataValue("url")}`);

    // Extract the base domain of the site we're crawling FIRST
    let siteDomain;
    try {
      siteDomain = extractDomainAndTld(siteUrl);
      logger.info(`🌐 Site domain: ${siteDomain}`);
    } catch (error) {
      logger.error(`Error extracting domain from site URL ${siteUrl}:`, error);
      return 0;
    }

    // Get the base URL for resolving relative links
    const baseUrl = new URL(siteUrl).origin;

    const currentPageUrl = link.getDataValue("url");
    const currentPagePath = new URL(currentPageUrl).pathname;
    const directoryPath = currentPagePath.substring(0, currentPagePath.lastIndexOf("/") + 1);

    // FIRST: Convert all URLs to absolute and filter by domain
    const internalLinks = [];

    for (const url of linksOnPage) {
      // Skip empty URLs and fragments
      if (!url || url.length === 0 || url.startsWith("#")) continue;

      let absoluteUrl;
      let isInternal = false;

      try {
        if (url.startsWith("/")) {
          // Absolute path relative to domain root - always internal
          absoluteUrl = new URL(url, baseUrl).href;
          isInternal = true;
        } else if (!url.includes("://")) {
          // Relative path - always internal
          const resolveBase = `${baseUrl}${directoryPath}`;
          absoluteUrl = new URL(url, resolveBase).href;
          isInternal = true;
        } else {
          // Absolute URL - check domain
          absoluteUrl = url;
          const linkDomain = extractDomainAndTld(url);
          isInternal = linkDomain === siteDomain;
        }

        // Only add if it's internal and passes basic URL validation
        if (isInternal && shouldProcessUrl(absoluteUrl)) {
          // Remove fragments
          const cleanUrl = absoluteUrl.includes("#") ? absoluteUrl.split("#")[0] : absoluteUrl;
          if (cleanUrl && cleanUrl.length > 0) {
            internalLinks.push(cleanUrl);
          }
        }
      } catch (error) {
        // Skip URLs that can't be parsed
        continue;
      }
    }

    // Deduplicate after filtering
    const uniqueInternalLinks = [...new Set(internalLinks)];

    logger.info(`✨ Found ${uniqueInternalLinks.length} internal links (filtered from ${linksOnPage.length} total)`);

    if (uniqueInternalLinks.length === 0) {
      return 0;
    }

    let newLinksAdded = 0;

    // Process links with concurrency control
    const CONCURRENCY_LIMIT = 1; // Reduced from 3
    const chunks = [];

    for (let i = 0; i < uniqueInternalLinks.length; i += CONCURRENCY_LIMIT) {
      chunks.push(uniqueInternalLinks.slice(i, i + CONCURRENCY_LIMIT));
    }

    for (const chunk of chunks) {
      const promises = chunk.map(async (absoluteUrl) => {
        return Promise.race([
          addLinkToDatabase(absoluteUrl, siteId),
          new Promise<null>(
            (_, reject) =>
              setTimeout(() => {
                reject(new Error("Operation timeout"));
              }, 15000), // Increased from 5000
          ),
        ]);
      });

      const results = await Promise.allSettled(promises);

      for (const result of results) {
        if (result.status === "fulfilled" && result.value) {
          newLinksAdded++;
        }
      }

      if (chunks.length > 1) {
        await delay(1000);
      }
    }

    return newLinksAdded;
  } catch (error) {
    logger.error(`Error processing newly discovered links for ${link.getDataValue("url")}:`, error);
    return 0;
  }
};

/**
 * Add a link to the appropriate database table
 */
const addLinkToDatabase = async (absoluteUrl: string, siteId: number): Promise<boolean> => {
  try {
    // Add a small delay to prevent database overload
    await new Promise((resolve) => setTimeout(resolve, 100));

    if (isFileUrl(absoluteUrl)) {
      const fileType = getFileType(absoluteUrl);
      const [fileLink, created] = await FileLink.findOrCreate({
        where: { url: absoluteUrl, siteId },
        defaults: {
          url: absoluteUrl,
          fileType,
          siteId,
          siteLinkId: 0,
          status: "pending",
        },
      });

      if (created) {
        logger.info(`📄 Added new file link: ${absoluteUrl} (type: ${fileType})`);
        return true;
      }
    } else {
      const [newLink, created] = await SiteLink.findOrCreate({
        where: { url: absoluteUrl, siteId },
        defaults: {
          url: absoluteUrl,
          siteId,
          status: "pending",
        },
      });

      if (created) {
        logger.info(`🔗 Added new site link: ${absoluteUrl}`);
        return true;
      }
    }

    return false;
  } catch (error) {
    logger.error(`Error adding link to database ${absoluteUrl}:`, error);
    return false;
  }
};

/**
 * Resolve a relative URL to an absolute URL
 */
export const resolveUrl = (url: string, baseUrl: string, directoryPath?: string): string => {
  try {
    // Handle relative URLs
    if (url.startsWith("/")) {
      // Absolute path relative to domain root
      return new URL(url, baseUrl).href;
    } else if (!url.includes("://")) {
      // Relative path (no protocol)
      const resolveBase = directoryPath ? `${baseUrl}${directoryPath}` : baseUrl;
      return new URL(url, resolveBase).href;
    } else {
      // Already absolute URL
      return url;
    }
  } catch (error) {
    throw new Error(`Error resolving URL: ${url} with base: ${baseUrl} - ${error}`);
  }
};

/**
 * Check if URL should be skipped based on content patterns
 */
export const shouldSkipUrl = (url: string): { skip: boolean; reason?: string } => {
  const urlLower = url.toLowerCase();

  if (urlLower.includes("banner")) {
    return { skip: true, reason: "URL contains 'banner', probably just a picture" };
  }

  if (urlLower.includes("advertisement") || urlLower.includes("ads")) {
    return { skip: true, reason: "URL contains advertisement content" };
  }

  return { skip: false };
};

/**
 * Extract file links from a list of URLs
 */
export const extractFileLinksFromUrls = (urls: string[]): string[] => {
  const fileExtensions = ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "csv"];

  return urls.filter((url) => {
    try {
      // Remove query parameters and fragments
      const cleanUrl = url.split("?")[0].split("#")[0];
      const extension = cleanUrl.split(".").pop()?.toLowerCase();
      return extension && fileExtensions.includes(extension);
    } catch (error) {
      return false;
    }
  });
};

/**
 * Normalize URLs by removing fragments and trailing slashes
 */
export const normalizeUrls = (urls: string[]): string[] => {
  return [
    ...new Set(
      urls
        .map((url) => {
          try {
            // Remove fragments
            let normalized = url.includes("#") ? url.split("#")[0] : url;

            // Remove trailing slash unless it's the root path
            if (normalized.endsWith("/") && normalized !== new URL(normalized).origin + "/") {
              normalized = normalized.slice(0, -1);
            }

            return normalized;
          } catch (error) {
            return url; // Return original if normalization fails
          }
        })
        .filter((url) => url && url.length > 0),
    ),
  ];
};

/**
 * Filter links by domain to only include internal links
 */
export const filterInternalLinks = (links: string[], siteDomain: string): string[] => {
  return links.filter((url) => {
    try {
      // Skip relative URLs (they're internal by definition)
      if (url.startsWith("/") || !url.includes("://")) {
        return true;
      }

      // Check if absolute URL belongs to the same domain
      const linkDomain = extractDomainAndTld(url);
      return linkDomain === siteDomain;
    } catch (error) {
      // If we can't determine the domain, exclude the link
      return false;
    }
  });
};

/**
 * Get directory path from a URL for resolving relative links
 */
export const getDirectoryPath = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    return pathname.substring(0, pathname.lastIndexOf("/") + 1);
  } catch (error) {
    return "/";
  }
};

/**
 * Batch process links with concurrency control
 */
export const batchProcessLinks = async <T>(
  links: string[],
  processor: (link: string) => Promise<T>,
  options: {
    concurrency?: number;
    delayBetweenBatches?: number;
    timeout?: number;
  } = {},
): Promise<{ results: T[]; errors: Array<{ link: string; error: Error }> }> => {
  const { concurrency = 3, delayBetweenBatches = 1000, timeout = 5000 } = options;

  const results: T[] = [];
  const errors: Array<{ link: string; error: Error }> = [];

  // Split links into chunks
  const chunks: string[][] = [];
  for (let i = 0; i < links.length; i += concurrency) {
    chunks.push(links.slice(i, i + concurrency));
  }

  for (const chunk of chunks) {
    // Process each chunk concurrently
    const promises = chunk.map(async (link) => {
      try {
        const result = await Promise.race([
          processor(link),
          new Promise<never>((_, reject) =>
            setTimeout(() => {
              reject(new Error("Operation timeout"));
            }, timeout),
          ),
        ]);
        return { success: true, result, link };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error : new Error(String(error)),
          link,
        };
      }
    });

    const chunkResults = await Promise.allSettled(promises);

    // Process results
    for (const promiseResult of chunkResults) {
      if (promiseResult.status === "fulfilled") {
        const { success, result, error, link } = promiseResult.value;
        if (success) {
          results.push(result);
        } else {
          errors.push({ link, error });
        }
      }
    }

    // Add delay between chunks
    if (chunks.length > 1 && delayBetweenBatches > 0) {
      await delay(delayBetweenBatches);
    }
  }

  return { results, errors };
};
