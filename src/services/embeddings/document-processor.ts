/**
 * Document processing utilities for creating chunks and managing content
 */

import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { Document } from "@langchain/core/documents";

import { logger } from "@/services/logger";
import { config } from "@/config";
import { enhanceDocumentWithMetadataAndSources } from "./document_loaders";
import { extractDatesFromContent } from "@/utils/date-utils";

/**
 * Text splitter instance configured with app settings
 */
const splitter = new RecursiveCharacterTextSplitter({
  chunkSize: config.chunkSize,
  chunkOverlap: config.chunkOverlap,
});

/**
 * Create chunks from text content with metadata enhancement
 * @param text Text content to chunk
 * @param metadata Base metadata to include with chunks
 * @returns Array of enhanced document chunks
 */
export const createChunks = async (text: string, metadata: Record<string, unknown> = {}): Promise<Document[]> => {
  // Extract dates from the content
  const datesInContent = extractDatesFromContent(text);

  // Add dates to metadata if found
  if (datesInContent.length > 0) {
    metadata.datesInContent = datesInContent;

    // Try to determine the most recent date
    if (!metadata.lastModified && !metadata.createdAt) {
      try {
        const sortedDates = datesInContent
          .map((dateStr) => new Date(dateStr))
          .filter((date) => !isNaN(date.getTime()))
          .sort((a, b) => b.getTime() - a.getTime());

        if (sortedDates.length > 0) {
          metadata.extractedDate = sortedDates[0].toISOString();
        }
      } catch (error) {
        logger.warn("Error parsing dates from content:", error);
      }
    }
  }

  // Add processing timestamp
  metadata.createdAt = new Date().toISOString();
  metadata.contentLength = text.length;

  // Create document chunks
  const docs = await splitter.createDocuments([text], [metadata]);

  // Enhance each document with metadata and sources
  return docs.map((doc: Document) => enhanceDocumentWithMetadataAndSources(doc));
};

/**
 * Validate and sanitize document content
 * @param content Raw content to validate
 * @returns Sanitized content or null if invalid
 */
export const validateDocumentContent = (content: string): string | null => {
  if (!content || typeof content !== "string") {
    return null;
  }

  // Remove excessive whitespace
  const sanitized = content
    .replace(/\s+/g, " ") // Replace multiple whitespace with single space
    .replace(/\n\s*\n/g, "\n") // Replace multiple newlines with single newline
    .trim();

  // Check minimum content length
  if (sanitized.length < 50) {
    logger.warn(`Content too short: ${sanitized.length} characters`);
    return null;
  }

  // Check for suspicious content patterns
  if (isSuspiciousContent(sanitized)) {
    logger.warn("Content appears to be suspicious or low-quality");
    return null;
  }

  return sanitized;
};

/**
 * Check if content appears to be suspicious or low-quality
 */
function isSuspiciousContent(content: string): boolean {
  const suspiciousPatterns = [
    /^(error|404|not found|page not found)/i,
    /^(access denied|forbidden|unauthorized)/i,
    /^(loading|please wait|redirecting)/i,
    /^(javascript required|enable javascript)/i,
    /^(\s*\n\s*){10,}/, // Too many empty lines
  ];

  return suspiciousPatterns.some((pattern) => pattern.test(content));
}

/**
 * Extract metadata from document content
 * @param content Document content
 * @param url Source URL
 * @returns Extracted metadata object
 */
export const extractContentMetadata = (content: string, url: string): Record<string, unknown> => {
  const metadata: Record<string, unknown> = {
    sourceUrl: url,
    extractedAt: new Date().toISOString(),
    contentLength: content.length,
  };

  // Extract title from content (first line or heading)
  const lines = content.split("\n").filter((line) => line.trim().length > 0);
  if (lines.length > 0) {
    const firstLine = lines[0].trim();
    if (firstLine.length < 200) {
      // Reasonable title length
      metadata.extractedTitle = firstLine;
    }
  }

  // Extract dates from content
  const dates = extractDatesFromContent(content);
  if (dates.length > 0) {
    metadata.datesInContent = dates;

    // Try to find the most recent date
    try {
      const sortedDates = dates
        .map((dateStr) => new Date(dateStr))
        .filter((date) => !isNaN(date.getTime()) && date <= new Date())
        .sort((a, b) => b.getTime() - a.getTime());

      if (sortedDates.length > 0) {
        metadata.mostRecentDate = sortedDates[0].toISOString();
      }
    } catch (error) {
      logger.warn("Error processing dates from content:", error);
    }
  }

  // Estimate reading time (average 200 words per minute)
  const wordCount = content.split(/\s+/).length;
  metadata.estimatedReadingTimeMinutes = Math.ceil(wordCount / 200);
  metadata.wordCount = wordCount;

  // Extract language hints (basic detection)
  metadata.language = detectLanguage(content);

  return metadata;
};

/**
 * Basic language detection
 */
function detectLanguage(content: string): string {
  // Very basic language detection based on common words
  const englishWords = ["the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"];
  const spanishWords = ["el", "la", "y", "o", "pero", "en", "de", "con", "por", "para"];
  const frenchWords = ["le", "la", "et", "ou", "mais", "dans", "de", "avec", "par", "pour"];

  const words = content.toLowerCase().split(/\s+/).slice(0, 100); // Check first 100 words

  const englishCount = words.filter((word) => englishWords.includes(word)).length;
  const spanishCount = words.filter((word) => spanishWords.includes(word)).length;
  const frenchCount = words.filter((word) => frenchWords.includes(word)).length;

  if (englishCount > spanishCount && englishCount > frenchCount) {
    return "en";
  } else if (spanishCount > frenchCount) {
    return "es";
  } else if (frenchCount > 0) {
    return "fr";
  }

  return "unknown";
}

/**
 * Merge metadata from multiple sources
 * @param baseMetadata Base metadata object
 * @param additionalMetadata Additional metadata to merge
 * @returns Merged metadata object
 */
export const mergeMetadata = (
  baseMetadata: Record<string, unknown>,
  additionalMetadata: Record<string, unknown>,
): Record<string, unknown> => {
  const merged = { ...baseMetadata };

  for (const [key, value] of Object.entries(additionalMetadata)) {
    if (value !== undefined && value !== null) {
      // Handle arrays by merging and deduplicating
      if (Array.isArray(value) && Array.isArray(merged[key])) {
        merged[key] = [...new Set([...merged[key], ...value])];
      }
      // Handle objects by deep merging
      else if (typeof value === "object" && typeof merged[key] === "object" && !Array.isArray(value)) {
        merged[key] = { ...(merged[key] as Record<string, unknown>), ...(value as Record<string, unknown>) };
      }
      // Otherwise, override with new value
      else {
        merged[key] = value;
      }
    }
  }

  return merged;
};

/**
 * Calculate content quality score
 * @param content Document content
 * @returns Quality score between 0 and 1
 */
export const calculateContentQuality = (content: string): number => {
  let score = 0.5; // Base score

  // Length factor (optimal around 1000-5000 characters)
  const length = content.length;
  if (length > 100 && length < 10000) {
    score += 0.2;
  }
  if (length > 500 && length < 5000) {
    score += 0.1;
  }

  // Sentence structure (presence of periods, proper capitalization)
  const sentences = content.split(/[.!?]+/).filter((s) => s.trim().length > 0);
  if (sentences.length > 2) {
    score += 0.1;
  }

  // Paragraph structure (presence of line breaks)
  const paragraphs = content.split(/\n\s*\n/).filter((p) => p.trim().length > 0);
  if (paragraphs.length > 1) {
    score += 0.1;
  }

  // Vocabulary diversity (unique words ratio)
  const words = content.toLowerCase().split(/\s+/);
  const uniqueWords = new Set(words);
  const diversityRatio = uniqueWords.size / words.length;
  if (diversityRatio > 0.3) {
    score += 0.1;
  }

  // Penalize repetitive content
  const repeatedPhrases = findRepeatedPhrases(content);
  if (repeatedPhrases > 5) {
    score -= 0.2;
  }

  return Math.max(0, Math.min(1, score));
};

/**
 * Find repeated phrases in content
 */
function findRepeatedPhrases(content: string): number {
  const phrases = content.toLowerCase().match(/\b\w+\s+\w+\s+\w+\b/g) || [];
  const phraseCount = new Map<string, number>();

  phrases.forEach((phrase) => {
    phraseCount.set(phrase, (phraseCount.get(phrase) || 0) + 1);
  });

  return Array.from(phraseCount.values()).filter((count) => count > 2).length;
}

/**
 * Split large content into optimal chunks for processing
 * @param content Large content to split
 * @param maxChunkSize Maximum size per chunk
 * @returns Array of content chunks
 */
export const splitLargeContent = (content: string, maxChunkSize = 50000): string[] => {
  if (content.length <= maxChunkSize) {
    return [content];
  }

  const chunks: string[] = [];
  let currentPosition = 0;

  while (currentPosition < content.length) {
    let chunkEnd = currentPosition + maxChunkSize;

    // Try to break at a natural boundary (paragraph, sentence, or word)
    if (chunkEnd < content.length) {
      // Look for paragraph break
      const paragraphBreak = content.lastIndexOf("\n\n", chunkEnd);
      if (paragraphBreak > currentPosition + maxChunkSize * 0.7) {
        chunkEnd = paragraphBreak + 2;
      } else {
        // Look for sentence break
        const sentenceBreak = content.lastIndexOf(". ", chunkEnd);
        if (sentenceBreak > currentPosition + maxChunkSize * 0.8) {
          chunkEnd = sentenceBreak + 2;
        } else {
          // Look for word break
          const wordBreak = content.lastIndexOf(" ", chunkEnd);
          if (wordBreak > currentPosition + maxChunkSize * 0.9) {
            chunkEnd = wordBreak + 1;
          }
        }
      }
    }

    chunks.push(content.substring(currentPosition, chunkEnd).trim());
    currentPosition = chunkEnd;
  }

  return chunks.filter((chunk) => chunk.length > 0);
};
