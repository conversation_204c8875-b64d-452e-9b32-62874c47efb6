/**
 * File processing utilities for handling PDF and other document types
 */

import { PGVectorStore } from "@langchain/community/vectorstores/pgvector";

import { logger } from "@/services/logger";
import { FileLink, FileLinkModel } from "@/store/models/file_link";
import { SiteLinkModel } from "@/store/models/site_link";
import { documentLoader, safeParseDate } from "./document_loaders";
import { createChunks } from "./document-processor";
import { isSupportedFileType, getFileType } from "@/utils/file-utils";
import { associateEmbeddingsWithSiteLink } from "./database-operations";

/**
 * Process file links associated with a site link
 * @param vectorStore Vector store instance
 * @param link Site link containing file links
 * @returns Number of files processed successfully
 */
export const processFileLinks = async (vectorStore: PGVectorStore, link: SiteLinkModel): Promise<number> => {
  const fileLinks = await FileLink.findAll({
    where: {
      siteLinkId: link.getDataValue("id"),
      status: "pending",
    },
  });

  if (fileLinks.length === 0) {
    return 0;
  }

  logger.info(`Found ${fileLinks.length} file links to process for ${link.getDataValue("url")}`);

  let processedCount = 0;
  for (const fileLink of fileLinks) {
    try {
      const processed = await processFileLink(vectorStore, fileLink, link);
      if (processed) {
        processedCount++;
      }
    } catch (error) {
      logger.error(`Failed to process file link: ${fileLink.getDataValue("url")}`, error);

      // Only update if the status isn't already set to download_failed
      if (fileLink.getDataValue("status") !== "download_failed") {
        await fileLink.update({
          status: "failed",
          processedAt: new Date(),
          comment: error instanceof Error ? error.message : String(error),
        });
      }
    }
  }

  return processedCount;
};

/**
 * Process a single file link
 * @param vectorStore Vector store instance
 * @param fileLink File link to process
 * @param siteLink Associated site link
 * @returns True if processed successfully
 */
export const processFileLink = async (
  vectorStore: PGVectorStore,
  fileLink: FileLinkModel,
  siteLink: SiteLinkModel,
): Promise<boolean> => {
  const fileUrl = fileLink.getDataValue("url");
  const fileType = fileLink.getDataValue("fileType") || getFileType(fileUrl);
  const siteId = siteLink.getDataValue("siteId");
  const fileLinkId = fileLink.getDataValue("id");

  // Check if file type is supported
  if (!isSupportedFileType(fileType)) {
    logger.info(`Skipping unsupported file type: ${fileType} for ${fileUrl}`);
    await fileLink.update({
      status: "ignored",
      comment: `Unsupported file type: ${fileType}`,
    });
    return false;
  }

  logger.info(`Processing ${fileType.toUpperCase()}: ${fileUrl}`);

  try {
    // Process based on file type
    const document = await loadDocumentByType(fileUrl, fileType, siteId, fileLinkId);

    if (!document) {
      throw new Error("Failed to create document from file URL");
    }

    // Ensure document.metadata exists
    if (!document.metadata) {
      document.metadata = {};
    }
    
    // Add siteId to metadata
    document.metadata.siteId = siteId;

    // Normalize date fields to prevent database errors
    normalizeDocumentDates(document);

    // Create chunks and add to vector store
    const chunks = await createChunks(document.pageContent, document.metadata);
    
    // Add siteId to each chunk's metadata if not already present
    chunks.forEach(chunk => {
      if (!chunk.metadata.siteId) {
        chunk.metadata.siteId = siteId;
      }
    });
    
    await vectorStore.addDocuments(chunks);

    // Note: File embeddings are stored in the embeddings table but without fromSiteLinkId
    // They can be identified by their metadata containing file information
    logger.info(`Added ${chunks.length} chunks to vector store for file: ${fileUrl}`);

    // Update fileLink with success status and metadata
    await updateFileLink(fileLink, document, chunks.length, "processed");

    logger.info(`Successfully processed ${fileType.toUpperCase()}: ${fileUrl} (${chunks.length} chunks)`);
    return true;
  } catch (error) {
    logger.error(`Failed to process ${fileType.toUpperCase()}: ${fileUrl}`, error);

    await fileLink.update({
      status: "failed",
      processedAt: new Date(),
      comment: error instanceof Error ? error.message : String(error),
    });

    return false;
  }
};

/**
 * Load document based on file type
 */
async function loadDocumentByType(fileUrl: string, fileType: string, siteId: number, fileLinkId: number): Promise<any> {
  switch (fileType.toLowerCase()) {
    case "pdf":
      return await documentLoader.fromPDFUrl(fileUrl, siteId, fileLinkId);

    case "doc":
    case "docx":
      // Add support for Word documents if needed
      throw new Error(`${fileType.toUpperCase()} processing not yet implemented`);

    case "xls":
    case "xlsx":
      // Add support for Excel documents if needed
      throw new Error(`${fileType.toUpperCase()} processing not yet implemented`);

    case "txt":
      // Add support for text files if needed
      throw new Error(`${fileType.toUpperCase()} processing not yet implemented`);

    case "csv":
      // Add support for CSV files if needed
      throw new Error(`${fileType.toUpperCase()} processing not yet implemented`);

    default:
      throw new Error(`Unsupported file type: ${fileType}`);
  }
}

/**
 * Normalize date fields in document metadata
 */
function normalizeDocumentDates(document: any): void {
  if (document.metadata.creationDate) {
    document.metadata.creationDate = safeParseDate(document.metadata.creationDate as string) || undefined;
  }

  if (document.metadata.lastModified) {
    document.metadata.lastModified = safeParseDate(document.metadata.lastModified as string) || undefined;
  }

  if (document.metadata.modificationDate) {
    document.metadata.modificationDate = safeParseDate(document.metadata.modificationDate as string) || undefined;
  }
}

/**
 * Update file link with processing results
 */
async function updateFileLink(
  fileLink: FileLinkModel,
  document: any,
  chunkCount: number,
  status: string,
): Promise<void> {
  const updateData: any = {
    status,
    processedAt: new Date(),
    comment: `Successfully processed, ${chunkCount} chunks created`,
  };

  // Add S3 information if available
  if (document.metadata.s3Key) {
    updateData.s3Key = document.metadata.s3Key;
  }
  if (document.metadata.s3Url) {
    updateData.s3Url = document.metadata.s3Url;
  }

  // Add relevant metadata
  updateData.metadata = {
    title: document.metadata.title,
    author: document.metadata.author,
    subject: document.metadata.subject,
    keywords: document.metadata.keywords,
    creationDate: document.metadata.creationDate,
    lastModified: document.metadata.lastModified,
    numPages: document.metadata.numPages,
    fileSize: document.metadata.fileSize,
    contentLength: document.pageContent?.length,
    chunkCount,
  };

  await fileLink.update(updateData);
}

/**
 * Process file links for a specific site
 * @param siteId Site ID to process file links for
 * @returns Number of files processed
 */
export const processFileLinksForSite = async (siteId: number): Promise<number> => {
  logger.info(`Processing file links for site with ID ${siteId}`);

  const fileLinks = await FileLink.findAll({
    where: {
      siteId,
      status: "pending",
    },
    limit: 100, // Process in batches
  });

  if (fileLinks.length === 0) {
    logger.info(`No pending file links found for site ${siteId}`);
    return 0;
  }

  logger.info(`Found ${fileLinks.length} file links to process for site ${siteId}`);

  // Import vector store here to avoid circular dependencies
  const { getVectorStore } = await import("./create_embeddings");
  const vectorStore = await getVectorStore();

  let processedCount = 0;

  // Process file links with concurrency control
  const CONCURRENCY_LIMIT = 3;
  const fileLinkPromises: Promise<void>[] = [];

  for (let i = 0; i < fileLinks.length; i += CONCURRENCY_LIMIT) {
    const batch = fileLinks.slice(i, i + CONCURRENCY_LIMIT);

    const batchPromises = batch.map(async (fileLink) => {
      try {
        // Create a mock site link for compatibility
        const mockSiteLink = {
          getDataValue: (key: string) => {
            if (key === "siteId") return siteId;
            if (key === "id") return null;
            if (key === "url") return ""; // Not needed for file processing
            return null;
          },
        } as SiteLinkModel;

        const processed = await processFileLink(vectorStore, fileLink, mockSiteLink);
        if (processed) {
          processedCount++;
        }
      } catch (error) {
        logger.error(`Error processing file link ${fileLink.getDataValue("url")}:`, error);
      }
    });

    fileLinkPromises.push(...batchPromises);

    // Wait for current batch to complete before starting next batch
    await Promise.all(batchPromises);

    // Add delay between batches
    if (i + CONCURRENCY_LIMIT < fileLinks.length) {
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }
  }

  await Promise.all(fileLinkPromises);

  logger.info(`Completed processing file links for site with ID ${siteId}. Processed: ${processedCount}`);
  return processedCount;
};

/**
 * Get file processing statistics for a site
 */
export const getFileProcessingStats = async (
  siteId: number,
): Promise<{
  total: number;
  pending: number;
  processed: number;
  failed: number;
  ignored: number;
}> => {
  const [total, pending, processed, failed, ignored] = await Promise.all([
    FileLink.count({ where: { siteId } }),
    FileLink.count({ where: { siteId, status: "pending" } }),
    FileLink.count({ where: { siteId, status: "processed" } }),
    FileLink.count({ where: { siteId, status: "failed" } }),
    FileLink.count({ where: { siteId, status: "ignored" } }),
  ]);

  return { total, pending, processed, failed, ignored };
};

/**
 * Retry failed file links
 */
export const retryFailedFileLinks = async (siteId: number, limit = 10): Promise<number> => {
  const failedLinks = await FileLink.findAll({
    where: {
      siteId,
      status: "failed",
    },
    limit,
    order: [["updatedAt", "ASC"]], // Retry oldest failures first
  });

  if (failedLinks.length === 0) {
    return 0;
  }

  logger.info(`Retrying ${failedLinks.length} failed file links for site ${siteId}`);

  // Reset status to pending for retry
  await Promise.all(
    failedLinks.map((link) =>
      link.update({
        status: "pending",
        comment: "Retrying after failure",
      }),
    ),
  );

  // Process the retried links
  return await processFileLinksForSite(siteId);
};
