/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { OpenAIEmbeddings } from "@langchain/openai";
import { PGVectorStore } from "@langchain/community/vectorstores/pgvector";
import { Op, WhereOptions } from "sequelize";

import { logger, trackProgress } from "@/services/logger";
import { initDb } from "@/store/instance";
import { config } from "@/config";
import { documentLoader } from "./document_loaders";
import { SiteLink, SiteLinkAttributes, SiteLinkModel } from "@/store/models/site_link";
import { FileLink } from "@/store/models/file_link";
import { Site } from "@/store/models/site";
import { delay } from "@/utils/async-utils";
import { getFileType } from "@/utils/file-utils";
import { isFileUrl } from "@/utils/url-utils";
// Import our new modular services
import { loadJavaScriptHeavySite } from "./browser-loader";
import { processNewlyDiscoveredLinks, shouldSkipUrl } from "./link-processor";
import { processFileLinks, processFileLinksForSite } from "./file-processor";
import { createChunks } from "./document-processor";
import { associateEmbeddingsWithSiteLink, deleteExistingEmbeddings } from "./database-operations";

export async function getVectorStore() {
  await initDb();

  const embeddingsInstance = new OpenAIEmbeddings({
    modelName: config.embeddingModel,
  });

  return PGVectorStore.initialize(embeddingsInstance, {
    postgresConnectionOptions: {
      connectionString: config.databaseUrl,
    },
    tableName: "embeddings",
    columns: {
      idColumnName: "id",
      vectorColumnName: "embedding",
      contentColumnName: "content",
      metadataColumnName: "metadata",
    },
    verbose: true,
    chunkSize: 2000,
  });
}

// Removed duplicate processNewlyDiscoveredLinks and processLink functions - now using imported versions

export const createEmbeddingsForSiteLinks = async ({
  linksLimit = 10000,
  urlIncludesFilter,
  includeScraped = false,
  crawlInternalLinks = true,
  maxDepth = 5, // Add maxDepth parameter
}: {
  linksLimit?: number;
  urlIncludesFilter?: string;
  includeScraped?: boolean;
  crawlInternalLinks?: boolean;
  maxDepth?: number; // Add maxDepth parameter
}) => {
  await initDb();

  const whereClause: WhereOptions<SiteLinkAttributes> = {
    status: {
      [Op.or]: includeScraped ? ["pending", "scraped"] : ["pending"],
    },
  };

  // Check if urlIncludesFilter is a complete URL (starts with http/https)
  if (urlIncludesFilter && (urlIncludesFilter.startsWith("http://") || urlIncludesFilter.startsWith("https://"))) {
    // Use exact matching for complete URLs
    whereClause.url = urlIncludesFilter;
    logger.info(`Filtering for exact URL match: ${urlIncludesFilter}`);
  } else if (urlIncludesFilter) {
    // Use partial matching for substrings
    whereClause.url = {
      [Op.iLike]: `%${urlIncludesFilter}%`,
    };
    logger.info(`Filtering for URLs containing: ${urlIncludesFilter}`);
  }

  if (pendingLinks.length === 0) {
    logger.info("No pending links to process");
    return;
  }

  logger.info(`Found ${pendingLinks.length} links to process`);

  const stopTrackingTotalTime = trackProgress(`Processing links`);

  const vectorStore = await getVectorStore();

  const BATCH_SIZE = 10;
  const totalBatches = Math.ceil(pendingLinks.length / BATCH_SIZE);

  for (let i = 0; i < pendingLinks.length; i += BATCH_SIZE) {
    const batch = pendingLinks.slice(i, i + BATCH_SIZE);
    const batchNumber = Math.floor(i / BATCH_SIZE) + 1;

    const stopTrackingBatchTime = trackProgress(`Processing batch ${batchNumber}/${totalBatches}`, BATCH_SIZE * 10000);

    const batchPromises = batch.map(async (link: SiteLinkModel) => {
      try {
        const url = link.getDataValue("url");
        const siteLinkId = link.getDataValue("id");

        const { skip, reason } = shouldSkipUrl(url);

        if (skip) {
          logger.info(`Skipping URL: ${url} - Reason: ${reason}`);

          await link.update({
            status: "ignored",
            comment: `Ignored: ${reason}`,
          });

          return;
        }

        const stopTrackingTime = trackProgress(`Processing URL: ${url}`);

        let deletedCount = 0;
        if (includeScraped && link.getDataValue("status") === "scraped") {
          deletedCount = await deleteExistingEmbeddings(link);
        }

        let document;
        try {
          const existingHtml = link.getDataValue("originalHtml");
          if (existingHtml) {
            logger.info(`Using existing HTML for URL: ${url}`);
            document = documentLoader.fromExistingHtml(existingHtml, url);
          } else {
            logger.info(`Fetching HTML for URL: ${url}`);
            document = await documentLoader.fromHTMLPage(url);
          }

          // Ensure document is valid
          if (!document) {
            throw new Error("Failed to create document");
          }
        } catch (docError) {
          logger.error(`Error creating document for ${url}:`, docError);
          await link.update({
            status: "failed",
            lastScrapedAt: new Date(),
            comment: docError instanceof Error ? docError.message : String(docError),
          });
          return;
        }

        // Ensure document has all required metadata fields
        if (!document.metadata) document.metadata = {};
        if (document.metadata.isContentTooShort === undefined) document.metadata.isContentTooShort = false;
        if (!document.metadata.linksOnPage) document.metadata.linksOnPage = [];
        if (!document.metadata.fileLinksOnPage) document.metadata.fileLinksOnPage = [];

        // Ensure document has linksOnPage and fileLinksOnPage arrays
        const linksOnPage = document.linksOnPage || [];
        const fileLinksOnPage = document.fileLinksOnPage || [];
        const { pageContent, metadata, originalHtml } = document;

        if (metadata.isContentTooShort) {
          logger.info(`Skipping URL: ${url} - Content too short`);

          await link.update({
            status: "ignored",
            originalHtml,
            pageContent,
            comment: "Content too short (less than 100 characters)",
          });

          return;
        }

        // Add this check for file content
        if (metadata.isFile) {
          logger.info(`URL: ${url} contains a file of type ${metadata.fileType}`);

          await link.update({
            status: "scraped", // Changed from "processed" to "scraped"
            lastScrapedAt: new Date(),
            originalHtml: undefined, // Don't store binary content in the database
            pageContent: `[File detected: ${metadata.fileType}]`,
            comment: `File of type ${metadata.fileType} detected and saved`,
            linksOnPage: (metadata.linksOnPage as string[]) || [],
            fileLinksOnPage: (metadata.fileLinksOnPage as string[]) || [],
          });

          // If it's not a PDF, we can return here
          if (metadata.fileType !== "pdf") {
            return;
          }

          // Otherwise, continue with processing the PDF
        }

        const chunks = await createChunks(pageContent, metadata);

        await vectorStore.addDocuments(chunks);

        const updatedEmbeddings = await associateEmbeddingsWithSiteLink(url, siteLinkId, link.getDataValue("siteId"));

        let fileLinksStored = 0;
        let fileLinksProcessed = 0;
        if (fileLinksOnPage && fileLinksOnPage.length > 0) {
          const fileLinkPromises = fileLinksOnPage.map((fileUrl) => {
            const fileType = getFileType(fileUrl);
            return FileLink.findOrCreate({
              where: { url: fileUrl },
              defaults: {
                url: fileUrl,
                fileType,
                siteLinkId,
                siteId: link.getDataValue("siteId"), // Add the siteId from the parent link
              },
            });
          });

          const results = await Promise.all(fileLinkPromises);
          fileLinksStored = results.filter((result) => result[1]).length;

          // Process PDFs and other supported file types
          fileLinksProcessed = await processFileLinks(vectorStore, link);
        }

        const timeElapsed = stopTrackingTime(
          `${chunks.length} chunks, ${fileLinksStored} new file links stored, ${fileLinksProcessed} file links processed`,
        );

        await link.update({
          status: "scraped",
          lastScrapedAt: new Date(),
          linksOnPage,
          fileLinksOnPage,
          originalHtml,
          pageContent,
          timeElapsed,
          metadata: {
            ...link.getDataValue("metadata"), // Preserve existing metadata
            ...metadata, // Add new metadata from document
            processedAt: new Date().toISOString(),
          },
          comment: `
          Successfully scraped, ${String(chunks.length)} chunks created,
          ${String(linksOnPage.length)} links found, ${String(fileLinksOnPage.length)} file links found,
          ${updatedEmbeddings} embeddings associated, ${deletedCount} old embeddings deleted,
          ${fileLinksProcessed} file links processed`,
        });

        // Add this block to process internal links if crawlInternalLinks is true
        if (crawlInternalLinks && linksOnPage && linksOnPage.length > 0) {
          const siteUrl = link.getDataValue("url").split("/").slice(0, 3).join("/"); // Get base URL
          const newLinksAdded = await processNewlyDiscoveredLinks(link, siteUrl);

          if (newLinksAdded > 0) {
            logger.info(`Added ${newLinksAdded} new internal links from ${link.getDataValue("url")}`);
          }
        }

        logger.info(`Successfully stored ${String(chunks.length)} chunks with their embeddings in vector store`);
      } catch (error) {
        logger.error(`Failed to process link: ${link.get("url")}`, error);
        await link.update({
          status: "failed",
          lastScrapedAt: new Date(),
          comment: error instanceof Error ? error.message : String(error),
        });
      }
    });

    await Promise.all(batchPromises);
    const batchTimeElapsed = stopTrackingBatchTime(`${batch.length} URLs processed`);
    logger.info(`Completed batch ${batchNumber}/${totalBatches} in ${batchTimeElapsed}ms`);
  }

  const totalTimeElapsed = stopTrackingTotalTime();
  logger.info(`Total time elapsed: ${totalTimeElapsed}ms`);

  if (crawlInternalLinks) {
    logger.info(`Initial crawl completed. Starting recursive crawl up to depth ${maxDepth}`);
    await processAdditionalDepths(1, maxDepth, urlIncludesFilter, includeScraped);
  }
};

// Add this new function to handle additional crawl depths
const processAdditionalDepths = async (
  currentDepth: number,
  maxDepth: number,
  urlIncludesFilter?: string,
  includeScraped = false,
) => {
  if (currentDepth >= maxDepth) {
    logger.info(`Reached maximum crawl depth of ${maxDepth}`);
    return;
  }

  logger.info(`Processing links at depth ${currentDepth + 1}`);

  // Find links that were discovered in the previous depth but not yet processed
  const whereClause: WhereOptions<SiteLinkAttributes> = {
    status: "pending",
  };

  if (urlIncludesFilter) {
    whereClause.url = {
      [Op.iLike]: `%${urlIncludesFilter}%`,
    };
  }

  const pendingLinks = await SiteLink.findAll({
    where: whereClause,
    order: [["createdAt", "DESC"]], // Process newest links first (likely from previous depth)
    limit: 100, // Limit per depth to avoid processing too many links
  });

  if (pendingLinks.length === 0) {
    logger.info(`No pending links to process at depth ${currentDepth + 1}`);
    return;
  }

  logger.info(`Found ${pendingLinks.length} links to process at depth ${currentDepth + 1}`);

  const stopTrackingTotalTime = trackProgress(`Processing links at depth ${currentDepth + 1}`);

  const vectorStore = await getVectorStore();

  // Process these links (using existing code structure)
  const BATCH_SIZE = 10;
  const totalBatches = Math.ceil(pendingLinks.length / BATCH_SIZE);

  for (let i = 0; i < pendingLinks.length; i += BATCH_SIZE) {
    // Existing batch processing code...
  }

  const totalTimeElapsed = stopTrackingTotalTime();
  logger.info(`Total time elapsed for depth ${currentDepth + 1}: ${totalTimeElapsed}ms`);

  // Recursively process the next depth
  await processAdditionalDepths(currentDepth + 1, maxDepth, urlIncludesFilter, includeScraped);
};

// Add a function to process embeddings for multiple sites in parallel
export const createEmbeddingsForMultipleSites = async ({
  urlIncludesFilter,
  sites,
  linksLimit = 100,
  includeScraped = false,
  crawlInternalLinks = true,
  concurrency = 3,
  maxDepth = 3, // Add maxDepth parameter with default value
  forceRefresh = false, // Add this parameter
}: {
  urlIncludesFilter?: string;
  sites: string[];
  linksLimit?: number;
  includeScraped?: boolean;
  crawlInternalLinks?: boolean;
  concurrency?: number;
  maxDepth?: number; // Add type definition
  forceRefresh?: boolean; // Add type definition
}) => {
  logger.info(`Processing embeddings for ${sites.length} sites with concurrency of ${concurrency}`);

  // Process sites in batches based on concurrency
  for (let i = 0; i < sites.length; i += concurrency) {
    const batch = sites.slice(i, i + concurrency);
    logger.info(`Processing batch ${Math.floor(i / concurrency) + 1} with ${batch.length} sites`);

    const batchPromises = batch.map(async (siteUrl) => {
      try {
        await initDb();

        // Find the site
        const site = await Site.findOne({
          where: {
            url: siteUrl,
            enabled: true,
          },
        });

        if (!site) {
          logger.warn(`Site ${siteUrl} not found or disabled. Skipping.`);
          return;
        }

        const siteId = site.getDataValue("id");

        // Get links for this site
        const whereClause: WhereOptions<SiteLinkAttributes> = {
          siteId,
          ...(urlIncludesFilter && {
            url: {
              [Op.iLike]: `%${urlIncludesFilter}%`,
            },
          }),
          status: {
            [Op.or]: includeScraped ? ["pending", "scraped"] : ["pending"],
          },
        };

        const pendingLinks = await SiteLink.findAll({
          where: whereClause,
          order: [
            ["status", "ASC"],
            ["lastScrapedAt", "ASC"],
          ],
          limit: linksLimit,
        });

        if (pendingLinks.length === 0) {
          logger.info(`No pending links to process for site ${siteUrl}`);
          return;
        }

        logger.info(`Found ${pendingLinks.length} links to process for site ${siteUrl}`);

        // Process the links for this site - pass maxDepth parameter
        await processLinksForSite(
          pendingLinks,
          includeScraped,
          crawlInternalLinks,
          maxDepth,
          1,
          new Set<string>(),
          forceRefresh,
        );

        // Process file links also
        await processFileLinksForSite(siteId);

        logger.info(`Completed processing embeddings for site ${siteUrl}`);
      } catch (error) {
        logger.error(`Error processing embeddings for site ${siteUrl}:`, error);
      }
    });

    await Promise.all(batchPromises);

    // Add a small delay between batches
    if (i + concurrency < sites.length) {
      logger.info("Waiting between batches...");
      await delay(5000);
    }
  }

  logger.info("Completed processing embeddings for all sites");
};

// Add this function to check for potential circular references
const isCircularReference = (url: string, processedUrls: Set<string>): boolean => {
  return processedUrls.has(url);
};

// Helper function to process links for a single site
const processLinksForSite = async (
  pendingLinks: SiteLinkModel[],
  includeScraped = false,
  crawlInternalLinks = true,
  maxDepth = 3,
  currentDepth = 1,
  processedUrls = new Set<string>(),
  forceRefresh = false, // Add this parameter
) => {
  const vectorStore = await getVectorStore();

  const BATCH_SIZE = 10;
  const totalBatches = Math.ceil(pendingLinks.length / BATCH_SIZE);

  const newlyDiscoveredLinks: SiteLinkModel[] = [];

  for (let i = 0; i < pendingLinks.length; i += BATCH_SIZE) {
    const batch = pendingLinks.slice(i, i + BATCH_SIZE);
    const batchNumber = Math.floor(i / BATCH_SIZE) + 1;

    const stopTrackingBatchTime = trackProgress(
      `Processing batch ${batchNumber}/${totalBatches} at depth ${currentDepth}`,
      BATCH_SIZE * 10000,
    );

    // Process each link in the batch
    const batchPromises = batch.map(async (link) => {
      try {
        const url = link.getDataValue("url");
        const siteLinkId = link.getDataValue("id");

        // Skip if this is a file URL that was incorrectly added as a SiteLink
        // Only skip non-PDF file types that cannot be processed for content extraction
        if (isFileUrl(url) && !url.toLowerCase().includes(".pdf")) {
          const fileType = getFileType(url);
          logger.info(`Skipping non-PDF file URL that should be processed as FileLink: ${url} (type: ${fileType})`);
          await link.update({
            status: "ignored",
            comment: `Non-PDF file URL should be processed as FileLink, not SiteLink (type: ${fileType})`,
          });
          return;
        }

        // Skip if we've already processed this URL to prevent circular references
        if (isCircularReference(url, processedUrls)) {
          logger.info(`Skipping already processed URL: ${url}`);
          return;
        }

        // Add URL to processed set
        processedUrls.add(url);

        const stopTrackingTime = trackProgress(`Processing URL: ${url}`);

        let deletedCount = 0;
        if (includeScraped && link.getDataValue("status") === "scraped") {
          deletedCount = await deleteExistingEmbeddings(link);
        }

        // Fetch and process document
        let document;
        try {
          const existingHtml = link.getDataValue("originalHtml");
          if (!forceRefresh && existingHtml) {
            logger.info(`Using existing HTML for URL: ${url}`);
            document = documentLoader.fromExistingHtml(existingHtml, url);
          } else {
            logger.info(`Fetching fresh content for URL: ${url}`);

            // Get the site to check if it requires JavaScript
            const siteId = link.getDataValue("siteId");
            const site = await Site.findByPk(siteId);
            const requiresJavaScript = site?.getDataValue("requiresJavascript") || false;

            // Check if the URL is likely to be a JavaScript-heavy site
            const isJSHeavySite =
              requiresJavaScript ||
              url.includes("spa") ||
              url.includes("app") ||
              url.includes("dashboard") ||
              url.includes("#/") ||
              url.includes("react") ||
              url.includes("angular") ||
              url.includes("vue");

            if (isJSHeavySite || forceRefresh) {
              logger.info(
                `Using Puppeteer for JavaScript-heavy site (${requiresJavaScript ? "site flag" : "URL pattern"}) or force refresh: ${url}`,
              );
              document = await loadJavaScriptHeavySite(url);
            } else {
              // Try normal loading first
              try {
                document = await documentLoader.fromHTMLPage(url);

                // If content is too short, it might be a JS site that didn't render properly
                if (document.pageContent && document.pageContent.length < 500) {
                  logger.info(
                    `Content too short (${document.pageContent.length} chars), trying with Puppeteer: ${url}`,
                  );
                  document = await loadJavaScriptHeavySite(url);

                  // If we get good content with Puppeteer, update the site to mark it as requiring JavaScript
                  if (document.pageContent && document.pageContent.length >= 500 && site) {
                    logger.info(`Marking site as requiring JavaScript based on content length improvement: ${url}`);
                    await site.update({ requiresJavascript: true });
                  }
                }
              } catch (error) {
                logger.warn(`Error with standard loader, trying with Puppeteer: ${url}`, error);
                document = await loadJavaScriptHeavySite(url);

                // If Puppeteer succeeds where standard loader failed, update the site
                if (document && site) {
                  logger.info(`Marking site as requiring JavaScript based on standard loader failure: ${url}`);
                  await site.update({ requiresJavascript: true });
                }
              }
            }
          }

          // Ensure document is valid
          if (!document) {
            throw new Error("Failed to create document");
          }
        } catch (docError) {
          logger.error(`Error creating document for ${url}:`, docError);
          await link.update({
            status: "failed",
            lastScrapedAt: new Date(),
            comment: docError instanceof Error ? docError.message : String(docError),
          });
          return;
        }

        // Process document and create embeddings
        const { pageContent, metadata, originalHtml } = document;
        const linksOnPage = document.linksOnPage || [];
        const fileLinksOnPage = document.fileLinksOnPage || [];

        // Create chunks and add to vector store
        const chunks = await createChunks(pageContent, metadata);
        await vectorStore.addDocuments(chunks);

        // Associate embeddings with site link
        const updatedEmbeddings = await associateEmbeddingsWithSiteLink(url, siteLinkId, link.getDataValue("siteId"));

        // Process file links if any
        let fileLinksStored = 0;
        let fileLinksProcessed = 0;
        if (fileLinksOnPage && fileLinksOnPage.length > 0) {
          const fileLinkPromises = fileLinksOnPage.map((fileUrl) => {
            const fileType = getFileType(fileUrl);
            return FileLink.findOrCreate({
              where: { url: fileUrl },
              defaults: {
                url: fileUrl,
                fileType,
                siteLinkId,
                siteId: link.getDataValue("siteId"),
              },
            });
          });

          const results = await Promise.all(fileLinkPromises);
          fileLinksStored = results.filter((result) => result[1]).length;

          // Process PDFs and other supported file types
          fileLinksProcessed = await processFileLinks(vectorStore, link);
        }

        // Update link status
        const timeElapsed = stopTrackingTime(`Processed URL: ${url}`);
        await link.update({
          status: "scraped",
          lastScrapedAt: new Date(),
          linksOnPage,
          fileLinksOnPage,
          originalHtml,
          pageContent,
          timeElapsed,
          metadata: {
            ...link.getDataValue("metadata"),
            ...metadata,
            processedAt: new Date().toISOString(),
          },
          comment: `Successfully scraped, ${String(chunks.length)} chunks created,
          ${String(linksOnPage.length)} links found, ${String(fileLinksOnPage.length)} file links found,
          ${updatedEmbeddings} embeddings associated, ${deletedCount} old embeddings deleted,
          ${fileLinksProcessed} file links processed`,
        });

        // Process newly discovered links if crawlInternalLinks is true
        if (crawlInternalLinks && linksOnPage && linksOnPage.length > 0) {
          const siteUrl = link.getDataValue("url").split("/").slice(0, 3).join("/"); // Get base URL
          const newLinksAdded = await processNewlyDiscoveredLinks(link, siteUrl);

          if (newLinksAdded > 0) {
            logger.info(`Added ${newLinksAdded} new internal links from ${link.getDataValue("url")}`);
          }
        }

        logger.info(`Successfully stored ${String(chunks.length)} chunks with their embeddings in vector store`);
      } catch (error) {
        logger.error(`Failed to process link: ${link.get("url")}`, error);
        await link.update({
          status: "failed",
          lastScrapedAt: new Date(),
          comment: error instanceof Error ? error.message : String(error),
        });
      }
    });

    await Promise.all(batchPromises);

    const batchTimeElapsed = stopTrackingBatchTime(`${batch.length} URLs processed`);
    logger.info(`Completed batch ${batchNumber}/${totalBatches} in ${batchTimeElapsed}ms`);
  }

  // Process newly discovered links recursively if we haven't reached max depth
  if (crawlInternalLinks && newlyDiscoveredLinks.length > 0 && currentDepth < maxDepth) {
    logger.info(`Found ${newlyDiscoveredLinks.length} new links to process at depth ${currentDepth + 1}`);
    try {
      await processLinksForSite(
        newlyDiscoveredLinks,
        includeScraped,
        crawlInternalLinks,
        maxDepth,
        currentDepth + 1,
        processedUrls,
      );
    } catch (error) {
      logger.error(`Error processing links at depth ${currentDepth + 1}:`, error);
      // Continue with the rest of the process instead of failing completely
    }
  }
};

// Removed duplicate processFileLinksForSite function - now using imported version

// Removed duplicate loadJavaScriptHeavySite function - now using imported version
