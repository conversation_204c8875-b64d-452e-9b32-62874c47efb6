/**
 * Database operations for embeddings and site links
 */

import { QueryTypes } from "sequelize";

import { logger } from "@/services/logger";
import { sequelize } from "@/store/instance";
import { Embedding } from "@/store/models/embedding";
import { SiteLinkModel } from "@/store/models/site_link";

/**
 * Associate embeddings with a site link
 * @param url URL to match embeddings against
 * @param siteLinkId Site link ID to associate with
 * @param siteId Site ID to associate with
 * @returns Number of embeddings updated
 */
export const associateEmbeddingsWithSiteLink = async (
  url: string, 
  siteLinkId: number,
  siteId?: number
): Promise<number> => {
  try {
    const query = `
      UPDATE embeddings
      SET "from_site_link_id" = :siteLinkId,
          "site_id" = :siteId
      WHERE metadata->>'sourceUrl' = :url 
      AND "from_site_link_id" IS NULL
    `;

    const [, rowCount] = await sequelize.query(query, {
      replacements: { url, siteLinkId, siteId },
      type: QueryTypes.UPDATE,
    });

    if (rowCount > 0) {
      logger.info(`Associated ${rowCount} embeddings with site link ${siteLinkId} and site ${siteId}`);
    }

    return rowCount;
  } catch (error) {
    logger.error("Error associating embeddings with site link:", error);
    return 0;
  }
};

/**
 * Delete existing embeddings for a site link
 * @param link Site link to delete embeddings for
 * @returns Number of embeddings deleted
 */
export const deleteExistingEmbeddings = async (link: SiteLinkModel): Promise<number> => {
  try {
    const siteLinkId = link.getDataValue("id");
    const url = link.getDataValue("url");

    logger.info(`Deleting existing embeddings for URL: ${url}`);

    const deleted = await Embedding.destroy({
      where: {
        fromSiteLinkId: siteLinkId,
      },
    });

    logger.info(`Deleted ${deleted} existing embeddings for URL: ${url}`);
    return deleted;
  } catch (error) {
    logger.error(`Error deleting embeddings for link: ${link.getDataValue("url")}`, error);
    return 0;
  }
};

/**
 * Delete embeddings by URL pattern
 * @param urlPattern URL pattern to match (supports LIKE patterns)
 * @returns Number of embeddings deleted
 */
export const deleteEmbeddingsByUrlPattern = async (urlPattern: string): Promise<number> => {
  try {
    const query = `
      DELETE FROM embeddings
      WHERE metadata->>'sourceUrl' LIKE :urlPattern
    `;

    const [, rowCount] = await sequelize.query(query, {
      replacements: { urlPattern },
      type: QueryTypes.DELETE,
    });

    logger.info(`Deleted ${rowCount} embeddings matching pattern: ${urlPattern}`);
    return rowCount;
  } catch (error) {
    logger.error(`Error deleting embeddings by pattern ${urlPattern}:`, error);
    return 0;
  }
};

/**
 * Get embedding statistics for a site
 * @param siteId Site ID to get statistics for
 * @returns Embedding statistics
 */
export const getEmbeddingStats = async (
  siteId: number,
): Promise<{
  totalEmbeddings: number;
  embeddingsWithSiteLink: number;
  embeddingsWithFileLink: number;
  averageContentLength: number;
  oldestEmbedding: Date | null;
  newestEmbedding: Date | null;
}> => {
  try {
    const query = `
      SELECT 
        COUNT(*) as total_embeddings,
        COUNT(from_site_link_id) as embeddings_with_site_link,
        COUNT(from_file_link_id) as embeddings_with_file_link,
        AVG(LENGTH(content)) as average_content_length,
        MIN(created_at) as oldest_embedding,
        MAX(created_at) as newest_embedding
      FROM embeddings e
      LEFT JOIN site_links sl ON e.from_site_link_id = sl.id
      LEFT JOIN file_links fl ON e.from_file_link_id = fl.id
      WHERE sl.site_id = :siteId OR fl.site_id = :siteId
    `;

    const [results] = await sequelize.query(query, {
      replacements: { siteId },
      type: QueryTypes.SELECT,
    });

    const result = results || {};

    return {
      totalEmbeddings: parseInt(result.total_embeddings) || 0,
      embeddingsWithSiteLink: parseInt(result.embeddings_with_site_link) || 0,
      embeddingsWithFileLink: parseInt(result.embeddings_with_file_link) || 0,
      averageContentLength: parseFloat(result.average_content_length) || 0,
      oldestEmbedding: result.oldest_embedding ? new Date(result.oldest_embedding) : null,
      newestEmbedding: result.newest_embedding ? new Date(result.newest_embedding) : null,
    };
  } catch (error) {
    logger.error(`Error getting embedding stats for site ${siteId}:`, error);
    return {
      totalEmbeddings: 0,
      embeddingsWithSiteLink: 0,
      embeddingsWithFileLink: 0,
      averageContentLength: 0,
      oldestEmbedding: null,
      newestEmbedding: null,
    };
  }
};

/**
 * Find duplicate embeddings based on content similarity
 * @param siteId Site ID to check for duplicates
 * @param similarityThreshold Similarity threshold (0-1)
 * @returns Array of duplicate embedding groups
 */
export const findDuplicateEmbeddings = async (
  siteId: number,
  similarityThreshold = 0.95,
): Promise<Array<{ content: string; ids: number[]; count: number }>> => {
  try {
    const query = `
      SELECT 
        content,
        array_agg(id) as ids,
        COUNT(*) as count
      FROM embeddings e
      LEFT JOIN site_links sl ON e.from_site_link_id = sl.id
      LEFT JOIN file_links fl ON e.from_file_link_id = fl.id
      WHERE (sl.site_id = :siteId OR fl.site_id = :siteId)
      GROUP BY content
      HAVING COUNT(*) > 1
      ORDER BY COUNT(*) DESC
    `;

    const results = await sequelize.query(query, {
      replacements: { siteId },
      type: QueryTypes.SELECT,
    });

    return results.map((result) => ({
      content: result.content,
      ids: result.ids,
      count: parseInt(result.count),
    }));
  } catch (error) {
    logger.error(`Error finding duplicate embeddings for site ${siteId}:`, error);
    return [];
  }
};

/**
 * Remove duplicate embeddings, keeping the newest one
 * @param siteId Site ID to clean duplicates for
 * @returns Number of embeddings removed
 */
export const removeDuplicateEmbeddings = async (siteId: number): Promise<number> => {
  try {
    const duplicates = await findDuplicateEmbeddings(siteId);
    let removedCount = 0;

    for (const duplicate of duplicates) {
      // Keep the newest embedding (highest ID) and remove the rest
      const idsToRemove = duplicate.ids.slice(0, -1);

      if (idsToRemove.length > 0) {
        const deleted = await Embedding.destroy({
          where: {
            id: idsToRemove,
          },
        });

        removedCount += deleted;
        logger.info(`Removed ${deleted} duplicate embeddings for content: ${duplicate.content.substring(0, 100)}...`);
      }
    }

    logger.info(`Total duplicate embeddings removed for site ${siteId}: ${removedCount}`);
    return removedCount;
  } catch (error) {
    logger.error(`Error removing duplicate embeddings for site ${siteId}:`, error);
    return 0;
  }
};

/**
 * Update embedding metadata in bulk
 * @param updates Array of updates to apply
 * @returns Number of embeddings updated
 */
export const bulkUpdateEmbeddingMetadata = async (
  updates: Array<{ id: number; metadata: Record<string, unknown> }>,
): Promise<number> => {
  try {
    let updatedCount = 0;

    // Process updates in batches to avoid overwhelming the database
    const batchSize = 100;
    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);

      const promises = batch.map(async (update) => {
        try {
          const [affectedRows] = await Embedding.update({ metadata: update.metadata }, { where: { id: update.id } });
          return affectedRows;
        } catch (error) {
          logger.error(`Error updating embedding ${update.id}:`, error);
          return 0;
        }
      });

      const results = await Promise.all(promises);
      updatedCount += results.reduce((sum, count) => sum + count, 0);

      // Add small delay between batches
      if (i + batchSize < updates.length) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }

    logger.info(`Bulk updated metadata for ${updatedCount} embeddings`);
    return updatedCount;
  } catch (error) {
    logger.error("Error in bulk update embedding metadata:", error);
    return 0;
  }
};

/**
 * Get embeddings that need metadata updates
 * @param siteId Site ID to check
 * @returns Array of embeddings needing updates
 */
export const getEmbeddingsNeedingMetadataUpdate = async (
  siteId: number,
): Promise<Array<{ id: number; content: string; metadata: Record<string, unknown> }>> => {
  try {
    const query = `
      SELECT e.id, e.content, e.metadata
      FROM embeddings e
      LEFT JOIN site_links sl ON e.from_site_link_id = sl.id
      LEFT JOIN file_links fl ON e.from_file_link_id = fl.id
      WHERE (sl.site_id = :siteId OR fl.site_id = :siteId)
        AND (
          e.metadata->>'sourceUrl' IS NULL 
          OR e.metadata->>'extractedAt' IS NULL
          OR e.metadata->>'contentLength' IS NULL
        )
      LIMIT 1000
    `;

    const results = await sequelize.query(query, {
      replacements: { siteId },
      type: QueryTypes.SELECT,
    });

    return results.map((result) => ({
      id: result.id,
      content: result.content,
      metadata: result.metadata || {},
    }));
  } catch (error) {
    logger.error(`Error getting embeddings needing metadata update for site ${siteId}:`, error);
    return [];
  }
};

/**
 * Clean up orphaned embeddings (embeddings without valid site or file links)
 * @returns Number of orphaned embeddings removed
 */
export const cleanupOrphanedEmbeddings = async (): Promise<number> => {
  try {
    const query = `
      DELETE FROM embeddings
      WHERE from_site_link_id IS NOT NULL 
        AND from_site_link_id NOT IN (SELECT id FROM site_links)
        OR from_file_link_id IS NOT NULL 
        AND from_file_link_id NOT IN (SELECT id FROM file_links)
    `;

    const [, rowCount] = await sequelize.query(query, {
      type: QueryTypes.DELETE,
    });

    logger.info(`Cleaned up ${rowCount} orphaned embeddings`);
    return rowCount;
  } catch (error) {
    logger.error("Error cleaning up orphaned embeddings:", error);
    return 0;
  }
};
