/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/**
 * Browser-based content loading using Puppeteer for JavaScript-heavy sites
 */

import puppeteer from "puppeteer";

import { logger } from "@/services/logger";
import { getFileType } from "@/utils/file-utils";
import { config } from "@/config";

let activeBrowsers = 0;

export interface BrowserLoadResult {
  pageContent: string;
  metadata: Record<string, any>;
  originalHtml: string;
  linksOnPage: string[];
  fileLinksOnPage: string[];
}

/**
 * Load JavaScript-heavy sites using Puppeteer
 * @param url URL to load
 * @returns Document-like object with content and metadata
 */
export const loadJavaScriptHeavySite = async (url: string): Promise<BrowserLoadResult> => {
  activeBrowsers++;
  logger.info(`Loading JavaScript-heavy site: ${url} (Active browsers: ${activeBrowsers})`);

  const browser = await puppeteer.launch({
    headless: true,
    ...(config.chromiumPath && { executablePath: config.chromiumPath }),
    // executablePath: "/snap/bin/chromium", // "/usr/bin/google-chrome",
    args: [
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-dev-shm-usage",
      "--disable-gpu",
      "--disable-web-security",
      "--no-first-run",
      "--no-default-browser-check",
    ],
  });

  let page: puppeteer.Page | undefined;

  try {
    page = await browser.newPage();

    // Set request interception to block unnecessary resources
    await page.setRequestInterception(true);
    page.on("request", (req) => {
      const resourceType = req.resourceType();
      if (["image", "stylesheet", "font", "media"].includes(resourceType)) {
        req.abort();
      } else {
        req.continue();
      }
    });

    // Set a reasonable viewport
    await page.setViewport({ width: 1280, height: 800 });

    // Set a user agent to avoid being blocked
    await page.setUserAgent(
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    );

    // Navigate to the URL with a longer timeout and better error handling
    try {
      await page.goto(url, {
        waitUntil: "networkidle2",
        timeout: 30000,
      });
    } catch (navigationError) {
      logger.warn(`Navigation error for ${url}: ${navigationError.message}`);

      // Check if this is a PDF print URL
      if (url.includes("/print/pdf/")) {
        logger.info(`Detected PDF print URL: ${url}, treating as PDF document`);

        await browser.close();

        // Instead of returning a minimal document, redirect to PDF processing
        try {
          // Import the PDF processor
          const { documentLoader } = await import("@/services/embeddings/document_loaders");

          // Process as a PDF URL
          const pdfDocument = await documentLoader.fromPDFUrl(url);

          // Convert to BrowserLoadResult format
          return {
            pageContent: pdfDocument.pageContent,
            metadata: {
              ...pdfDocument.metadata,
              sourceUrl: url,
              processedAsPdf: true,
            },
            originalHtml: "",
            linksOnPage: pdfDocument.linksOnPage || [],
            fileLinksOnPage: pdfDocument.fileLinksOnPage || [],
          };
        } catch (pdfError) {
          logger.error(`Error processing PDF URL ${url}:`, pdfError);
          throw pdfError;
        }
      }

      // For other navigation errors, try to continue with whatever content we have
      logger.info(`Attempting to continue with partial content for: ${url}`);
    }

    // Wait for content to load
    await page.waitForSelector("body", { timeout: 10000 });

    // Handle hash fragments in URLs (like citycode.net/#!articleBuildingCode)
    if (url.includes("#!") || url.includes("#/")) {
      await handleHashFragments(page, url);
    }

    // Wait a bit more for any dynamic content to finish loading
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Check for iframes and extract content
    const iframeResult = await extractIframeContent(page);

    // Get the page title
    const title = await page.title();

    // Get the main page content
    let pageContent = await page.evaluate(`document.body.innerText || ""`);

    // Get the original HTML
    const originalHtml = await page.content();

    // Extract links from main page
    const linksOnPage = await extractLinks(page);

    // Extract file links from main page
    const fileLinksOnPage = await extractFileLinks(page);

    // Prepare metadata
    const metadata = {
      title,
      sourceUrl: url,
      renderedWithPuppeteer: true,
      contentLength: pageContent.length,
      extractedAt: new Date().toISOString(),
    };

    await browser.close();

    // If we found content in an iframe, use that instead
    if (iframeResult.content && iframeResult.content.length > pageContent.length) {
      logger.info(
        `Using content from iframe for ${url} (${iframeResult.content.length} chars vs ${pageContent.length} chars in main page)`,
      );
      pageContent = iframeResult.content;

      // Merge links, with iframe links taking precedence
      if (iframeResult.links.length > 0) {
        linksOnPage.push(...iframeResult.links);
      }

      // Merge file links, with iframe file links taking precedence
      if (iframeResult.fileLinks.length > 0) {
        fileLinksOnPage.push(...iframeResult.fileLinks);
      }

      // Add iframe info to metadata
      metadata.contentFromIframe = true;
    }

    return {
      pageContent,
      metadata,
      originalHtml,
      linksOnPage: [...new Set(linksOnPage)], // Remove duplicates
      fileLinksOnPage: [...new Set(fileLinksOnPage)], // Remove duplicates
    };
  } catch (error) {
    await browser.close();
    logger.error(`Error loading JavaScript-heavy site ${url}:`, error);
    // Log the full stack trace for better debugging
    if (error instanceof Error && error.stack) {
      logger.error(`Stack trace: ${error.stack}`);
    }
    throw error;
  } finally {
    activeBrowsers--;
    try {
      if (page) await page.close();
      await browser.close();
      logger.debug(`Browser closed for ${url} (Active browsers: ${activeBrowsers})`);
    } catch (closeError) {
      logger.warn(`Error closing browser for ${url}:`, closeError);
    }
  }
};

/**
 * Handle hash fragments in URLs for single-page applications
 */
async function handleHashFragments(page: any, url: string): Promise<void> {
  // Extract the fragment
  const fragment = url.split("#")[1];

  // Wait a bit for initial load
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Try to find and click on elements that might load the content
  try {
    // Try different selector patterns based on the fragment
    const possibleSelectors = [
      `a[href="#${fragment}"]`,
      `a[href="#!${fragment}"]`,
      `a[href="#/${fragment}"]`,
      `[data-target="#${fragment}"]`,
      `[data-section="${fragment}"]`,
      `#${fragment}`,
    ];

    for (const selector of possibleSelectors) {
      const elementExists = await page.$(selector);
      if (elementExists) {
        logger.info(`Found clickable element for fragment: ${selector}`);
        await page.click(selector);
        // Wait for content to load after clicking
        await new Promise((resolve) => setTimeout(resolve, 2000));
        break;
      }
    }
  } catch (clickError) {
    logger.warn(`Error clicking fragment element: ${clickError.message}`);
    // Continue even if clicking fails
  }
}

/**
 * Extract content from iframes on the page
 */
async function extractIframeContent(page: any): Promise<{
  content: string;
  links: string[];
  fileLinks: string[];
}> {
  let iframeContent = "";
  let iframeLinks: string[] = [];
  let iframeFileLinks: string[] = [];

  try {
    // Check if there's an iframe with id "body-frame" (common in citycode.net)
    const hasBodyFrame = await page.evaluate(`!!document.getElementById('body-frame')`);

    if (hasBodyFrame) {
      logger.info("Found body-frame iframe, extracting content...");

      // Get the iframe element
      const bodyFrame = await page.frames().find((frame: any) => frame.name() === "body-frame");

      if (bodyFrame) {
        // Wait for content in the iframe
        await bodyFrame.waitForSelector("body", { timeout: 5000 });

        // Get content from the iframe
        iframeContent = await bodyFrame.evaluate(`document.body.innerText || ""`);

        // Get links from the iframe
        iframeLinks = await extractLinksFromFrame(bodyFrame);

        // Get file links from the iframe
        iframeFileLinks = await extractFileLinksFromFrame(bodyFrame);

        logger.info(`Extracted ${iframeContent.length} characters from iframe`);
      }
    }
  } catch (iframeError) {
    logger.warn(`Error extracting iframe content: ${iframeError.message}`);
  }

  return {
    content: iframeContent,
    links: iframeLinks,
    fileLinks: iframeFileLinks,
  };
}

/**
 * Extract links from the main page
 */
async function extractLinks(page: any): Promise<string[]> {
  try {
    const linksScript = `
      (function() {
        const links = document.querySelectorAll('a[href]');
        const results = [];
        for (let i = 0; i < links.length; i++) {
          const href = links[i].getAttribute('href');
          if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
            results.push(href);
          }
        }
        return JSON.stringify(results);
      })()
    `;
    const linksJson = await page.evaluate(linksScript);
    return JSON.parse(linksJson);
  } catch (error) {
    logger.warn(`Error extracting links: ${error.message}`);
    return [];
  }
}

/**
 * Extract file links from the main page
 */
async function extractFileLinks(page: any): Promise<string[]> {
  try {
    const fileLinksScript = `
      (function() {
        const links = document.querySelectorAll('a[href]');
        const results = [];
        for (let i = 0; i < links.length; i++) {
          const href = links[i].getAttribute('href');
          if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
            const extension = href.split('.').pop()?.toLowerCase();
            if (extension && ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv'].includes(extension)) {
              results.push(href);
            }
          }
        }
        return JSON.stringify(results);
      })()
    `;
    const fileLinksJson = await page.evaluate(fileLinksScript);
    return JSON.parse(fileLinksJson);
  } catch (error) {
    logger.warn(`Error extracting file links: ${error.message}`);
    return [];
  }
}

/**
 * Extract links from an iframe
 */
async function extractLinksFromFrame(frame: any): Promise<string[]> {
  try {
    const linksJson = await frame.evaluate(`
      (function() {
        const links = document.querySelectorAll('a[href]');
        const results = [];
        for (let i = 0; i < links.length; i++) {
          const href = links[i].getAttribute('href');
          if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
            results.push(href);
          }
        }
        return JSON.stringify(results);
      })()
    `);
    return JSON.parse(linksJson);
  } catch (error) {
    logger.warn(`Error extracting links from iframe: ${error.message}`);
    return [];
  }
}

/**
 * Extract file links from an iframe
 */
async function extractFileLinksFromFrame(frame: any): Promise<string[]> {
  try {
    const fileLinksJson = await frame.evaluate(`
      (function() {
        const links = document.querySelectorAll('a[href]');
        const results = [];
        for (let i = 0; i < links.length; i++) {
          const href = links[i].getAttribute('href');
          if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
            const extension = href.split('.').pop()?.toLowerCase();
            if (extension && ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv'].includes(extension)) {
              results.push(href);
            }
          }
        }
        return JSON.stringify(results);
      })()
    `);

    return JSON.parse(fileLinksJson);
  } catch (error) {
    logger.warn(`Error extracting file links from iframe: ${error.message}`);
    return [];
  }
}

/**
 * Check if a URL is likely to be a JavaScript-heavy site
 */
export const isJavaScriptHeavySite = (url: string, requiresJavaScript = false): boolean => {
  return (
    requiresJavaScript ||
    url.includes("spa") ||
    url.includes("app") ||
    url.includes("dashboard") ||
    url.includes("#/") ||
    url.includes("react") ||
    url.includes("angular") ||
    url.includes("vue")
  );
};
