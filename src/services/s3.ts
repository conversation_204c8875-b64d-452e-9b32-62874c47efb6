import * as fs from "fs";
import * as path from "path";

import { S3Client, PutObjectCommand, HeadObjectCommand } from "@aws-sdk/client-s3";

import { config } from "@/config";
import { logger } from "@/services/logger";

// Initialize S3 client
const s3Client = new S3Client({
  region: config.aws.region,
  credentials: {
    accessKeyId: config.aws.accessKeyId,
    secretAccessKey: config.aws.secretAccessKey,
  },
});

// S3 bucket name
const BUCKET_NAME = config.aws.s3Bucket;

/**
 * Upload a file to S3
 * @param filePath Local file path
 * @param key S3 object key (including folder structure)
 * @returns S3 URL of the uploaded file
 */
export const uploadFileToS3 = async (filePath: string, key: string): Promise<string> => {
  try {
    logger.info(`Uploading file to S3: ${filePath} -> ${key}`);

    // Read file content
    const fileContent = fs.readFileSync(filePath);

    // Set up the upload parameters
    const params = {
      Bucket: BUCKET_NAME,
      Key: key,
      Body: fileContent,
      ContentType: getContentType(filePath),
    };

    // Upload to S3
    const command = new PutObjectCommand(params);
    await s3Client.send(command);

    logger.info(`Successfully uploaded file to S3: ${key}`);

    // Return the S3 URL
    return `https://${BUCKET_NAME}.s3.${config.aws.region}.amazonaws.com/${key}`;
  } catch (error) {
    logger.error(`Error uploading file to S3: ${filePath}`, error);
    throw error;
  }
};

/**
 * Check if a file exists in S3
 * @param key S3 object key
 * @returns Boolean indicating if the file exists
 */
export const fileExistsInS3 = async (key: string): Promise<boolean> => {
  try {
    const command = new HeadObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    });

    await s3Client.send(command);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Get content type based on file extension
 * @param filePath File path
 * @returns Content type
 */
const getContentType = (filePath: string): string => {
  const extension = path.extname(filePath).toLowerCase();

  switch (extension) {
    case ".pdf":
      return "application/pdf";
    case ".doc":
      return "application/msword";
    case ".docx":
      return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
    case ".xls":
      return "application/vnd.ms-excel";
    case ".xlsx":
      return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    case ".ppt":
      return "application/vnd.ms-powerpoint";
    case ".pptx":
      return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
    case ".txt":
      return "text/plain";
    case ".csv":
      return "text/csv";
    default:
      return "application/octet-stream";
  }
};
