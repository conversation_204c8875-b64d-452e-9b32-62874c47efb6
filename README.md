
# QA from Website
A system to extract information from website content, build embeddings, and provide question-answering capabilities.

## Features

- Scrapes and processes web content from sitemaps or specific URLs
- Handles both HTML web pages and PDF documents
- Splits content into optimized chunks for embedding
- Creates vector embeddings using OpenAI's embedding models
- Stores embeddings in PostgreSQL with pgvector for efficient similarity search
- Downloads and caches PDF files locally for faster processing
- Records metadata about content sources and related links
- Provides conversational question answering based on embedded content
- Stores question-answer pairs with their embeddings for future reference
- Includes proper attribution with source links in responses
- Comprehensive logging with automatic log rotation and level-based filtering
- Supports multiple sites with parallel processing
- Configurable crawl depth for internal links
- AWS S3 integration for PDF storage and retrieval

## Source Management

The system maintains detailed source information for all answers:

### Source Types

1. **Content Sources**: URLs and documents used to generate answers
   - Extracted from search results with relevance scores
   - Includes metadata like publication dates and titles
   - Limited to top 5 most relevant sources

2. **Override Sources**: Manually curated content for specific topics
   - Triggered by keyword matching in questions
   - Takes precedence over regular content sources
   - Used for time-sensitive or specialized information

3. **Context Sources**: Full context used to generate the answer
   - Includes the system prompt and user prompt
   - Stored for debugging and answer verification

### Source Storage

Sources are stored in the `sources` column (JSONB type) of the `question_embeddings` table, with the following structure:

```json
[
  {
    "text": "Override content...",
    "type": "override"
  },
  {
    "url": "https://example.com/page",
    "title": "Page Title",
    "score": 0.92,
    "summary": "Brief summary of the content"
  },
  {
    "context": "Full context used...",
    "systemPrompt": "System prompt used...",
    "userPrompt": "User prompt used..."
  }
]
```

### Source Attribution

When generating answers, the system:
- Includes relevant links as HTML anchors
- Notes when information might be outdated
- Provides publication dates when available
- Formats source URLs for readability

## Prerequisites

- Node.js
- Docker

## Getting Started

### 1. Clone the repository

### 2. Environment setup
Copy the example environment file and update the values as needed:

```bash
cp .env.example .env
```

Key environment variables to configure:
- `OPENAI_API_KEY`: Your OpenAI API key
- `DATABASE_URL`: PostgreSQL connection string
- `SITE_URL`: Default website to scrape
- `AWS_*`: AWS credentials for S3 storage (optional)

### 3. Start the database
The project uses pgvector (PostgreSQL with vector extensions) for storing embeddings. To start the database and pgAdmin:

```bash
cd docker
docker compose up -d
```

This will start:
- PostgreSQL database with pgvector extension on port 5432
- pgAdmin web interface on port 5050

#### Database Access with pgAdmin

1. Open your browser and navigate to: http://localhost:5050
2. Login with:
   - Email: `<EMAIL>`
   - Password: `admin`
3. Add a new server connection:
   - Right-click on "Servers" in the left sidebar
   - Select "Create" → "Server..."
4. Configure the connection:
   - General tab:
     - Name: `pgvector-db` (or any name you prefer)
   - Connection tab:
     - Host name/address: `db` (use the service name from docker-compose)
     - Port: `5432`
     - Maintenance database: `ragdb`
     - Username: `postgres`
     - Password: `postgres`
5. Click "Save" to connect

### 4. Install dependencies

```bash
npm install
```

### 5. Site Management

#### Adding a new site
```bash
npm run add-site -- "example.com" true
```
This adds a site with the URL "example.com" and sets it as enabled.

#### Listing all sites
```bash
npm run list-sites
```

#### Toggling site status
```bash
npm run toggle-site -- "example.com" false
```
This disables the site with URL "example.com".

#### Deleting site data
```bash
npm run delete-site-data -- "example.com"
```
This removes all data associated with the site.

### 6. Content Processing Workflow

#### Option A: Automated Background Job System (Recommended)

The new background job system automates the entire scraping process with pagination and error handling.

**First-time setup:** Run the database migration to create the required tables:
```bash
# Option 1: Using the migration runner
npm run migrate

# Option 2: Using SQL script directly
psql -U postgres -d marvin -f src/scripts/psql/create-background-job-tables.sql
```

**Start the automated system:**
```bash
# 1. Start the background job processor
npm run run-background-jobs

# 2. Start automated scraping (in another terminal)
npm run automated-scraping start 30 100
```

This will automatically:
- Parse sitemaps for all enabled sites
- Create embeddings in batches of 100 URLs
- Run SQL migration scripts
- Upload files to S3
- Repeat every 30 minutes

**Management Commands:**
```bash
# Check automation status
npm run automated-scraping status
```

See [BACKGROUND_JOBS.md](BACKGROUND_JOBS.md) for detailed documentation.

#### Option B: Manual Process (Legacy)

##### 1. Parse sitemap
```bash
npm run parse-sitemap
```
For the default site in your .env file, or:
```bash
npm run parse-multiple-sitemaps
```
For all enabled sites.

##### 2. Add specific URLs (optional)
If you want to add specific pages not in the sitemap:
```bash
npm run add-specific-url -- "example.com" "https://example.com/specific-page"
```

##### 3. Create embeddings
Basic usage:
```bash
npm run create-embeddings
```

With advanced options:
```bash
npm run create-embeddings-with-args -- --linksLimit=500 --crawlInternalLinks=true --maxDepth=5 --urlIncludesFilter="example.com"
```

Parameters:
- `linksLimit`: Maximum number of URLs to process (default: 100)
- `urlIncludesFilter`: Only process URLs containing this string
- `includeScraped`: Whether to reprocess already scraped URLs (default: false)
- `crawlInternalLinks`: Whether to follow and process internal links (default: true)
- `maxDepth`: How many levels deep to crawl (default: 3)
- `parallel`: Use parallel processing (default: false)
- `workerCount`: Number of parallel workers (default: 4)
- `batchSize`: URLs to process in each batch (default: 15)

For multiple sites:
```bash
npm run create-embeddings-multiple
```

##### 4. Test question answering
```bash
npm run test-question
```

### 7. Deployment

#### Quick Deployment with Background Jobs
```bash
# Build and deploy all services (API + Background Jobs + Automation)
chmod +x deploy-with-jobs.sh
./deploy-with-jobs.sh
```

#### Manual Deployment
For production deployment:
```bash
npm run build
```

Using PM2:
```bash
pm2 start ecosystem.config.js
```

This starts three services:
- `marvin`: Main API server
- `marvin-jobs`: Background job processor
- `marvin-automation`: Automated scraping service

### 8. Database Backup and Restore

Backup:
```bash
npm run backup-db
```

Restore:
```bash
npm run restore-db
```
Or restore a specific backup:
```bash
npm run restore-db -- backups/ragdb_backup_20240630_120000.sql.gz
```

## Database Structure

The application uses PostgreSQL with the pgvector extension for storing and querying vector embeddings.

### Models and Relationships

#### Site
Represents a website that is being scraped.

- **Fields:**
  - `id`: Primary key
  - `url`: Website base URL (unique)
  - `name`: Optional name for the website
  - `lastScrapedAt`: Timestamp of last scraping activity
  - `enabled`: Whether the site is active
  - `clientId`: Optional client identifier
  - `createdAt` / `updatedAt`: Timestamps

#### SiteLink
Represents individual URLs from a website that are scraped for content.

- **Fields:**
  - `id`: Primary key
  - `url`: Full URL of the page (unique)
  - `siteId`: Foreign key to the parent Site
  - `status`: ("pending", "scraped", "failed", "ignored")
  - `lastScrapedAt`: Timestamp when the URL was last processed
  - `timeElapsed`: Processing time in milliseconds
  - `comment`: Additional information or processing notes
  - `linksOnPage`: Array of links found on the page
  - `fileLinksOnPage`: Array of file links found on the page

#### FileLink
Represents document files (PDF, DOCX, etc.) found on scraped pages.

- **Fields:**
  - `id`: Primary key
  - `url`: URL to the file (unique)
  - `fileType`: Type of file (pdf, document, spreadsheet, etc.)
  - `siteLinkId`: Foreign key to the SiteLink where it was found
  - `status`: Processing status
  - `s3Key`: S3 storage key (if using AWS)
  - `s3Url`: S3 access URL (if using AWS)

#### Embedding
Stores vector embeddings of content chunks for semantic search.

- **Fields:**
  - `id`: Primary key
  - `content`: Text content that was embedded
  - `embedding`: Vector representation (handled by pgvector)
  - `metadata`: JSONB object with additional information
  - `fromSiteLinkId`: Foreign key to the SiteLink

#### QuestionEmbedding
Stores question-answer pairs and their embeddings for future reference.

- **Fields:**
  - `id`: Primary key
  - `question`: The original question text
  - `answer`: The generated answer
  - `embedding`: Vector representation of the question
  - `relatedEmbeddingIds`: Array of IDs for content embeddings used

## License

[License information] 
