#!/bin/bash

# Deploy script for <PERSON> with background job system
# This script builds the application and starts all services using PM2

set -e  # Exit on any error

echo "🚀 Starting Marvin deployment with background job system..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
    echo "❌ Error: PM2 is not installed. Please install it first:"
    echo "   npm install -g pm2"
    exit 1
fi

# Build the application
echo "📦 Building application..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo "✅ Build completed successfully"

# Create logs directory if it doesn't exist
mkdir -p logs

# Stop existing PM2 processes (if any)
echo "🛑 Stopping existing PM2 processes..."
pm2 stop ecosystem.config.js 2>/dev/null || echo "No existing processes to stop"

# Start all services
echo "🚀 Starting all services with PM2..."
pm2 start ecosystem.config.js

# Check if all services started successfully
echo "⏳ Waiting for services to start..."
sleep 5

# Show status
echo "📊 Service status:"
pm2 status

# Show logs for a few seconds
echo "📝 Recent logs:"
echo "--- Marvin API Server ---"
pm2 logs marvin --lines 5 --nostream || true

echo "--- Background Job Processor ---"
pm2 logs marvin-jobs --lines 5 --nostream || true

echo "--- Automation Service ---"
pm2 logs marvin-automation --lines 5 --nostream || true

echo ""
echo "✅ Deployment completed!"
echo ""
echo "📋 Available commands:"
echo "   pm2 status                    - Show service status"
echo "   pm2 logs                      - Show all logs"
echo "   pm2 logs marvin              - Show API server logs"
echo "   pm2 logs marvin-jobs         - Show job processor logs"
echo "   pm2 logs marvin-automation   - Show automation service logs"
echo "   pm2 restart all              - Restart all services"
echo "   pm2 stop all                 - Stop all services"
echo ""
echo "🔧 Automation management:"
echo "   npm run automated-scraping status              - Show automation status"
echo ""
echo "🌐 API Server: http://localhost:3001"
echo "📊 PM2 Monitoring: pm2 monit"
echo ""
echo "🎉 All services are now running!"
