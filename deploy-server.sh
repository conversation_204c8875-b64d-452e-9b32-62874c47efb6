#!/bin/bash

pem="$HOME/.ssh/projects/repd.pem"
# Define server IPs as an array
server_ips=(
    "**********"     # Production server
    "*************"  # Server 1
    "**************" # Server 2
)

user="ubuntu"
local_path="./"  # Local directory to upload
remote_path="~/marvin"   # Remote directory on server
zip_name="deploy.zip"

# Check PEM file
if [ ! -f "$pem" ]; then
    echo "Error: PEM file not found at $pem"
    exit 1
fi

# npm run build

# Set permissions
chmod 400 "$pem"

echo "Creating zip file..."
zip -r $zip_name $local_path \
    -x "*.git/*" \
    -x "*node_modules/*" \
    -x "*.DS_Store" \
    -x "*dist/*" \
    -x "*.env*" \
    -x "*.zip" \
    -x "downloads/*" \
    -x "Embed/*" \
    -x "logs/*"

initializeNVM='
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
'

# Deploy to each server
for ip in "${server_ips[@]}"; do
    echo "Deploying to server $ip..."
    
    echo "Uploading zip to $ip..."
    # Upload zip
    scp -i "$pem" $zip_name "$user@$ip:$remote_path"
    
    # Unzip on server and cleanup
    ssh -i "$pem" "$user@$ip" <<EOF
        cd $remote_path
        unzip -o $zip_name
        rm $zip_name

        # Load NVM and Node
        $initializeNVM

        # npm install
EOF
    
    # Deploy
    ssh -i "$pem" "$user@$ip" << EOF
        $initializeNVM

        # pm2 restart marvin
EOF
    
    if [ $? -eq 0 ]; then
        echo "Deployment to $ip successful!"
    else
        echo "Deployment to $ip failed!"
        exit 1
    fi
done

# Remove local zip file after all deployments
rm $zip_name
echo "All deployments completed successfully!"
