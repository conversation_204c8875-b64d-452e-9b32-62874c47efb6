// pm2 start ecosystem.config.js

module.exports = {
  apps: [{
    name: 'marvin',
    script: './dist/src/server.js',
    interpreter: 'node',
    interpreter_args: '-r module-alias/register',
    exec_mode: 'fork',        // Explicitly set fork mode
    instance_var: 'marvin',
    instances: 1,             // Single instance
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    output: './logs/out.log',
    error: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss',
    merge_logs: true,
    kill_timeout: 3000,
    wait_ready: true,
    listen_timeout: 5000,
    restart_delay: 4000,      // Add delay between restarts
    exp_backoff_restart_delay: 100
  }, {
    name: 'marvin-jobs',
    script: './dist/src/scripts/jobs/run-background-jobs.js',
    interpreter: 'node',
    interpreter_args: '-r module-alias/register',
    exec_mode: 'fork',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '2G',  // Higher memory limit for job processing
    env: {
      NODE_ENV: 'production'
    },
    output: './logs/jobs-out.log',
    error: './logs/jobs-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss',
    merge_logs: true,
    kill_timeout: 30000,      // Longer timeout for graceful shutdown
    restart_delay: 10000,     // Longer delay between restarts
    exp_backoff_restart_delay: 1000
  }, {
    name: 'marvin-automation',
    script: './dist/src/scripts/automation/automated_scraping_with_jobs.js',
    interpreter: 'node',
    interpreter_args: '-r module-alias/register',
    exec_mode: 'fork',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    },
    output: './logs/automation-out.log',
    error: './logs/automation-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss',
    merge_logs: true,
    kill_timeout: 30000,
    restart_delay: 10000,
    exp_backoff_restart_delay: 1000
  }]
};
